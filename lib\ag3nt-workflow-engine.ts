/**
 * AG3NT Workflow Engine - Universal Workflow Orchestration
 * 
 * Generalized from planning-graph.ts to support any type of workflow with:
 * - Dynamic step sequences based on context
 * - MCP-enhanced execution with real-time context
 * - Sequential thinking integration
 * - Context propagation and enrichment
 * - Agent coordination and communication
 * - Progress tracking and session management
 * - Error handling and retry mechanisms
 * 
 * This forms the core of the AG3NT Framework for autonomous agent workflows.
 */

import { ChatOpenAI } from "@langchain/openai"
import { HumanMessage, SystemMessage } from "@langchain/core/messages"
import { UnifiedContextEngine } from "./unified-context-engine-v2"

// Universal workflow state interface
export interface WorkflowState {
  workflowId: string
  sessionId: string
  agentId: string
  agentType: string
  input: any
  context: Record<string, any>
  currentStep: string
  stepHistory: string[]
  results: Record<string, any>
  needsInput?: boolean
  question?: WorkflowQuestion
  completed: boolean
  error?: string
  metadata: WorkflowMetadata
}

export interface WorkflowQuestion {
  id: string
  question: string
  type: 'text' | 'select' | 'multiselect' | 'boolean'
  options?: string[]
  placeholder?: string
  required?: boolean
  validation?: string
}

export interface WorkflowMetadata {
  startTime: string
  lastUpdated: string
  totalSteps: number
  completedSteps: number
  estimatedDuration?: number
  priority: 'low' | 'medium' | 'high' | 'critical'
  tags: string[]
}

// Workflow step definition
export interface WorkflowStep {
  id: string
  name: string
  description: string
  dependencies: string[]
  requiredCapabilities: string[]
  timeout: number
  maxRetries: number
  mcpEnhanced: boolean
  sequentialThinking: boolean
  contextEnrichment: boolean
  executor: WorkflowStepExecutor
}

export interface WorkflowStepExecutor {
  execute(state: WorkflowState, context: any): Promise<WorkflowState>
  validate?(state: WorkflowState): boolean
  rollback?(state: WorkflowState): Promise<WorkflowState>
}

// Workflow definition
export interface WorkflowDefinition {
  id: string
  name: string
  description: string
  version: string
  agentTypes: string[]
  steps: WorkflowStep[]
  stepSequences: Record<string, string[]> // Dynamic sequences based on context
  defaultSequence: string[]
  entryPoint: string
  exitConditions: string[]
  errorHandling: WorkflowErrorHandling
}

export interface WorkflowErrorHandling {
  retryStrategy: 'immediate' | 'exponential' | 'linear'
  maxRetries: number
  fallbackSteps: Record<string, string>
  errorNotification: boolean
}

/**
 * AG3NT Workflow Engine
 * Universal workflow orchestration engine for autonomous agents
 */
export class AG3NTWorkflowEngine {
  private model: ChatOpenAI | null = null
  private contextEngine: UnifiedContextEngine | null = null
  private workflows: Map<string, WorkflowDefinition> = new Map()
  private activeSessions: Map<string, WorkflowState> = new Map()
  private stepExecutors: Map<string, WorkflowStepExecutor> = new Map()

  constructor() {
    // Initialize with default configurations
  }

  /**
   * Register a workflow definition
   */
  registerWorkflow(workflow: WorkflowDefinition): void {
    this.workflows.set(workflow.id, workflow)
    
    // Register step executors
    workflow.steps.forEach(step => {
      this.stepExecutors.set(`${workflow.id}:${step.id}`, step.executor)
    })
  }

  /**
   * Start a new workflow session
   */
  async startWorkflow(
    workflowId: string,
    agentId: string,
    agentType: string,
    input: any,
    options: {
      sessionId?: string
      priority?: 'low' | 'medium' | 'high' | 'critical'
      tags?: string[]
    } = {}
  ): Promise<WorkflowState> {
    const workflow = this.workflows.get(workflowId)
    if (!workflow) {
      throw new Error(`Workflow not found: ${workflowId}`)
    }

    if (!workflow.agentTypes.includes(agentType)) {
      throw new Error(`Agent type ${agentType} not supported by workflow ${workflowId}`)
    }

    // Initialize context engine for this session
    await this.initializeContextEngine(agentId, agentType, input)

    const sessionId = options.sessionId || `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    
    const state: WorkflowState = {
      workflowId,
      sessionId,
      agentId,
      agentType,
      input,
      context: {},
      currentStep: workflow.entryPoint,
      stepHistory: [],
      results: {},
      completed: false,
      metadata: {
        startTime: new Date().toISOString(),
        lastUpdated: new Date().toISOString(),
        totalSteps: workflow.defaultSequence.length,
        completedSteps: 0,
        priority: options.priority || 'medium',
        tags: options.tags || []
      }
    }

    this.activeSessions.set(sessionId, state)
    return state
  }

  /**
   * Execute workflow with dynamic step sequencing
   */
  async executeWorkflow(sessionId: string): Promise<WorkflowState> {
    const state = this.activeSessions.get(sessionId)
    if (!state) {
      throw new Error(`Session not found: ${sessionId}`)
    }

    const workflow = this.workflows.get(state.workflowId)
    if (!workflow) {
      throw new Error(`Workflow not found: ${state.workflowId}`)
    }

    try {
      // Determine step sequence based on current context
      const stepSequence = this.determineStepSequence(workflow, state)
      
      // Execute steps sequentially with context propagation
      for (const stepId of stepSequence) {
        if (state.needsInput) {
          break // Stop execution if input is needed
        }

        state.currentStep = stepId
        state.stepHistory.push(stepId)
        
        const updatedState = await this.executeStepWithContext(stepId, state)
        Object.assign(state, updatedState)
        
        state.metadata.completedSteps++
        state.metadata.lastUpdated = new Date().toISOString()
      }

      // Check completion conditions
      if (!state.needsInput && this.checkCompletionConditions(workflow, state)) {
        state.completed = true
      }

      this.activeSessions.set(sessionId, state)
      return state

    } catch (error) {
      state.error = error instanceof Error ? error.message : 'Unknown error'
      this.activeSessions.set(sessionId, state)
      throw error
    }
  }

  /**
   * Execute a single step with MCP-enhanced context
   */
  async executeStepWithContext(stepId: string, state: WorkflowState): Promise<Partial<WorkflowState>> {
    const workflow = this.workflows.get(state.workflowId)
    if (!workflow) {
      throw new Error(`Workflow not found: ${state.workflowId}`)
    }

    const step = workflow.steps.find(s => s.id === stepId)
    if (!step) {
      throw new Error(`Step not found: ${stepId}`)
    }

    console.log(`🧠 Executing ${stepId} with enhanced context...`)

    // 1. Get enhanced context with MCP enrichment (if enabled)
    let enhancedContext = null
    if (step.mcpEnhanced && this.contextEngine) {
      enhancedContext = await this.contextEngine.enhanceWithRAG(stepId, JSON.stringify(state.input))
      console.log(`📚 Enhanced context includes ${enhancedContext.enrichments?.length || 0} enrichments`)
    }

    // 2. Apply sequential thinking (if enabled)
    let sequentialThought = null
    if (step.sequentialThinking && this.contextEngine) {
      sequentialThought = await this.contextEngine.performSequentialThinking(
        `Executing ${stepId} for ${state.agentType}: ${JSON.stringify(state.input)}`,
        { thoughtNumber: 1, totalThoughts: 3 }
      )
      console.log(`🤔 Sequential thinking applied for ${stepId}`)
    }

    // 3. Build execution context
    const executionContext = {
      step,
      enhancedContext,
      sequentialThought,
      workflowState: state,
      metadata: {
        stepId,
        agentId: state.agentId,
        agentType: state.agentType,
        sessionId: state.sessionId,
        timestamp: new Date().toISOString()
      }
    }

    // 4. Execute the step
    const executor = this.stepExecutors.get(`${state.workflowId}:${stepId}`)
    if (!executor) {
      throw new Error(`Executor not found for step: ${stepId}`)
    }

    const result = await this.executeWithRetry(
      () => executor.execute(state, executionContext),
      step.maxRetries,
      step.timeout
    )

    // 5. Update context engine with results
    if (this.contextEngine && result.results && result.results[stepId]) {
      this.contextEngine.updateContext(stepId, result.results[stepId])
    }

    return result
  }
