import { z } from 'zod'

/**
 * AG3NT Project Plan Schema
 * 
 * Defines the structure for project plans that can be uploaded
 * to skip the planning phase and go directly to coding
 */

// Tech Stack Schema
export const TechStackSchema = z.object({
  Frontend: z.string().optional(),
  Backend: z.string().optional(),
  Database: z.string().optional(),
  Hosting: z.string().optional(),
  Authentication: z.string().optional(),
  Mobile: z.string().optional(),
  DevOps: z.string().optional(),
  Testing: z.string().optional(),
})

export type TechStack = z.infer<typeof TechStackSchema>

// Architecture Schema
export const ArchitectureSchema = z.object({
  type: z.string().optional(),
  pattern: z.string().optional(),
  components: z.array(z.string()).optional(),
  dataFlow: z.string().optional(),
  scalability: z.string().optional(),
})

export type Architecture = z.infer<typeof ArchitectureSchema>

// Feature Schema
export const FeatureSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  priority: z.enum(['high', 'medium', 'low']),
  complexity: z.enum(['simple', 'moderate', 'complex']),
  estimatedTime: z.string().optional(),
  dependencies: z.array(z.string()).optional(),
})

export type Feature = z.infer<typeof FeatureSchema>

// Wireframe Schema
export const WireframeSchema = z.object({
  pages: z.array(z.object({
    name: z.string(),
    path: z.string(),
    description: z.string(),
    wireframe: z.string(), // ASCII wireframe
    components: z.array(z.string()).optional(),
  })).optional(),
  components: z.array(z.object({
    name: z.string(),
    type: z.string(),
    description: z.string(),
    props: z.array(z.string()).optional(),
  })).optional(),
})

export type Wireframes = z.infer<typeof WireframeSchema>

// Database Schema
export const DatabaseSchemaType = z.object({
  tables: z.array(z.object({
    name: z.string(),
    description: z.string(),
    fields: z.array(z.object({
      name: z.string(),
      type: z.string(),
      required: z.boolean().optional(),
      unique: z.boolean().optional(),
      description: z.string().optional(),
    })),
    relationships: z.array(z.object({
      type: z.string(),
      table: z.string(),
      field: z.string(),
    })).optional(),
  })).optional(),
  relationships: z.array(z.object({
    from: z.string(),
    to: z.string(),
    type: z.string(),
    description: z.string().optional(),
  })).optional(),
})

export type DatabaseSchema = z.infer<typeof DatabaseSchemaType>

// File System Schema
export const FileSystemSchema = z.object({
  structure: z.record(z.any()).optional(),
  directories: z.array(z.string()).optional(),
  files: z.array(z.object({
    path: z.string(),
    type: z.string(),
    description: z.string().optional(),
  })).optional(),
})

export type FileSystem = z.infer<typeof FileSystemSchema>

// Task Schema
export const TaskSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string(),
  type: z.enum(['frontend', 'backend', 'database', 'testing', 'deployment', 'integration']),
  priority: z.enum(['high', 'medium', 'low']),
  estimatedTime: z.string().optional(),
  dependencies: z.array(z.string()).optional(),
  assignedTo: z.string().optional(),
})

export type Task = z.infer<typeof TaskSchema>

// AG3NT Planning Results Schema (matches actual planning agent output)
export const AG3NTPlanningResultsSchema = z.object({
  prompt: z.string(),
  timestamp: z.string().optional(),
  results: z.object({
    analyze: z.any().optional(),
    clarify: z.any().optional(),
    summary: z.any().optional(),
    techstack: z.any().optional(),
    prd: z.any().optional(),
    'context-profile': z.any().optional(),
    wireframes: z.any().optional(),
    design: z.any().optional(),
    database: z.any().optional(),
    filesystem: z.any().optional(),
    workflow: z.any().optional(),
    tasks: z.any().optional(),
    scaffold: z.any().optional(),
  })
})

// Legacy Project Plan Schema (for backward compatibility)
export const LegacyProjectPlanSchema = z.object({
  // Basic Information
  projectName: z.string(),
  projectDescription: z.string(),
  version: z.string().default('1.0.0'),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),

  // Planning Results
  analysis: z.object({
    projectType: z.string().optional(),
    complexity: z.string().optional(),
    targetAudience: z.string().optional(),
    keyFeatures: z.array(z.string()).optional(),
    technicalRequirements: z.array(z.string()).optional(),
  }).optional(),

  techStack: TechStackSchema,
  architecture: ArchitectureSchema.optional(),
  features: z.array(FeatureSchema).optional(),
  wireframes: WireframeSchema.optional(),
  database: DatabaseSchemaType.optional(),
  filesystem: FileSystemSchema.optional(),
  tasks: z.array(TaskSchema).optional(),

  // Design Information
  design: z.object({
    theme: z.string().optional(),
    colorScheme: z.string().optional(),
    typography: z.string().optional(),
    layout: z.string().optional(),
    responsive: z.boolean().optional(),
  }).optional(),

  // Workflow Information
  workflow: z.object({
    phases: z.array(z.string()).optional(),
    milestones: z.array(z.object({
      name: z.string(),
      description: z.string(),
      deadline: z.string().optional(),
    })).optional(),
    estimatedDuration: z.string().optional(),
  }).optional(),

  // Metadata
  metadata: z.object({
    source: z.string().optional(), // 'upload', 'planning-agent', 'manual'
    planningMethod: z.string().optional(),
    confidence: z.number().optional(),
    tags: z.array(z.string()).optional(),
  }).optional(),
})

// Union schema that accepts both formats
export const ProjectPlanSchema = z.union([
  AG3NTPlanningResultsSchema,
  LegacyProjectPlanSchema
])

export type AG3NTPlanningResults = z.infer<typeof AG3NTPlanningResultsSchema>
export type LegacyProjectPlan = z.infer<typeof LegacyProjectPlanSchema>
export type ProjectPlan = z.infer<typeof ProjectPlanSchema>

// Helper function to determine plan type
export function isAG3NTPlanningResults(data: any): data is AG3NTPlanningResults {
  return data && typeof data === 'object' && 'prompt' in data && 'results' in data
}

// Helper function to extract project info from AG3NT planning results
export function extractProjectInfoFromAG3NT(planningResults: AG3NTPlanningResults): {
  projectName: string
  projectDescription: string
  techStack: any
} {
  const analyze = planningResults.results.analyze
  const techstack = planningResults.results.techstack

  // Extract project name from prompt or analysis
  const projectName = analyze?.projectName ||
                     analyze?.name ||
                     planningResults.prompt.split(' ').slice(0, 3).join(' ') + ' Project'

  // Use prompt as description, or extract from analysis
  const projectDescription = analyze?.description ||
                            analyze?.summary ||
                            planningResults.prompt

  return {
    projectName,
    projectDescription,
    techStack: techstack || {}
  }
}

// Enhanced validation function
export function validateProjectPlan(data: unknown): {
  success: boolean;
  data?: ProjectPlan;
  errors?: string[];
  type?: 'ag3nt' | 'legacy'
} {
  try {
    const result = ProjectPlanSchema.parse(data)
    const type = isAG3NTPlanningResults(result) ? 'ag3nt' : 'legacy'
    return { success: true, data: result, type }
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors = error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
      return { success: false, errors }
    }
    return { success: false, errors: ['Invalid project plan format'] }
  }
}

// Sample project plan in AG3NT format (matches actual planning agent output)
export const sampleProjectPlan: AG3NTPlanningResults = {
  prompt: "Create a modern task management application with real-time collaboration features",
  timestamp: "2024-01-15T10:00:00Z",
  results: {
    analyze: {
      projectType: "Web Application",
      complexity: "Moderate",
      targetAudience: "Teams and individuals",
      keyFeatures: ["Task creation", "Real-time updates", "Team collaboration", "Progress tracking"],
      technicalRequirements: ["Responsive design", "Real-time sync", "User authentication"],
      projectName: "Task Management App",
      description: "A modern task management application with real-time collaboration features"
    },

    techstack: {
      Frontend: "Next.js 14 with TypeScript and Tailwind CSS",
      Backend: "Node.js with Express and TypeScript",
      Database: "PostgreSQL with Prisma ORM",
      Authentication: "NextAuth.js with JWT",
      Hosting: "Vercel for frontend, Railway for backend",
      Testing: "Jest and React Testing Library"
    },

    summary: {
      projectOverview: "A comprehensive task management application designed for teams and individuals",
      coreFeatures: ["Task CRUD operations", "Real-time collaboration", "User authentication", "Team workspaces"],
      technicalApproach: "Full-stack web application using modern React ecosystem",
      estimatedTimeline: "4-6 weeks for MVP"
    },

    prd: {
      timeline: "6 weeks",
      features: ["Task creation and management", "Real-time collaboration", "User authentication", "Team workspaces"],
      userStories: [
        "As a user, I want to create and manage tasks",
        "As a team member, I want to collaborate in real-time",
        "As a team lead, I want to organize tasks in workspaces"
      ],
      requirements: "Responsive design, real-time sync, secure authentication",
      acceptanceCriteria: "All core features functional with 99% uptime"
    },

    wireframes: {
      pages: [
        {
          name: "Dashboard",
          path: "/dashboard",
          description: "Main dashboard with task overview",
          wireframe: "┌─────────────────────────────────────┐\n│ Header: Logo | Search | Profile     │\n├─────────────────────────────────────┤\n│ Sidebar:    │ Main Content:         │\n│ - My Tasks  │ ┌─ Quick Add Task ──┐ │\n│ - Teams     │ │ [+ New Task]      │ │\n│ - Projects  │ └───────────────────┘ │\n│ - Settings  │ ┌─ Today's Tasks ───┐ │\n│             │ │ □ Task 1          │ │\n│             │ │ ☑ Task 2          │ │\n│             │ │ □ Task 3          │ │\n│             │ └───────────────────┘ │\n└─────────────────────────────────────┘"
        }
      ]
    },

    design: {
      theme: "Modern and clean with dark mode support",
      colorScheme: "Primary: Blue (#3B82F6), Secondary: Gray (#6B7280), Accent: Green (#10B981)",
      typography: "Inter font family with clear hierarchy",
      layout: "Responsive grid layout with sidebar navigation"
    },

    database: {
      tables: [
        {
          name: "users",
          description: "User accounts and authentication",
          fields: [
            {"name": "id", "type": "UUID", "required": true},
            {"name": "email", "type": "VARCHAR(255)", "required": true},
            {"name": "name", "type": "VARCHAR(100)", "required": true}
          ]
        },
        {
          name: "tasks",
          description: "Task items with details",
          fields: [
            {"name": "id", "type": "UUID", "required": true},
            {"name": "title", "type": "VARCHAR(200)", "required": true},
            {"name": "status", "type": "ENUM", "required": true},
            {"name": "user_id", "type": "UUID", "required": true}
          ]
        }
      ]
    },

    filesystem: {
      structure: {
        "src/": {
          "app/": ["api/", "dashboard/", "globals.css", "layout.tsx", "page.tsx"],
          "components/": ["ui/", "TaskCard.tsx", "TaskList.tsx", "Header.tsx"],
          "lib/": ["auth.ts", "db.ts", "utils.ts"]
        },
        "prisma/": ["schema.prisma"],
        "package.json": null
      }
    },

    workflow: {
      phases: ["Setup", "Authentication", "Core Features", "UI Development", "Testing", "Deployment"],
      estimatedDuration: "4-6 weeks"
    },

    tasks: {
      breakdown: [
        {
          id: "setup-project",
          title: "Setup Project Structure",
          description: "Initialize Next.js project with dependencies",
          type: "setup",
          priority: "high",
          estimatedTime: "2 hours"
        },
        {
          id: "auth-system",
          title: "Implement Authentication",
          description: "Setup NextAuth.js with email/password",
          type: "backend",
          priority: "high",
          estimatedTime: "4 hours"
        },
        {
          id: "task-crud",
          title: "Build Task CRUD",
          description: "Create task management functionality",
          type: "fullstack",
          priority: "high",
          estimatedTime: "6 hours"
        }
      ]
    },

    scaffold: {
      files: [
        {"path": "package.json", "type": "config", "description": "Project dependencies"},
        {"path": "src/app/layout.tsx", "type": "component", "description": "Root layout"},
        {"path": "src/components/TaskCard.tsx", "type": "component", "description": "Task display component"}
      ],
      commands: [
        "npm create next-app@latest task-manager --typescript --tailwind --eslint",
        "npm install prisma @prisma/client next-auth",
        "npx prisma init"
      ]
    }
  }
}

// Export utility functions
export { ProjectPlanSchema as default }
