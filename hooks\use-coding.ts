/**
 * AG3NT Platform - Coding Hook
 * 
 * React hook for managing autonomous coding workflow
 */

import { useState, useEffect, useCallback } from 'react'

export interface CodingTask {
  id: string
  type: 'frontend' | 'backend' | 'database' | 'testing' | 'deployment'
  title: string
  description: string
  dependencies: string[]
  estimatedTime: number
  priority: 'high' | 'medium' | 'low'
  status: 'pending' | 'in_progress' | 'completed' | 'failed'
  agentId?: string
  startTime?: number
  endTime?: number
  output?: any
}

export interface CodingProgress {
  totalTasks: number
  completedTasks: number
  inProgressTasks: number
  failedTasks: number
  currentPhase: string
  estimatedCompletion: number
  activeAgents: string[]
}

export interface CodingStatus {
  isRunning: boolean
  loading: boolean
  error: string | null
}

export function useCoding() {
  const [status, setStatus] = useState<CodingStatus>({
    isRunning: false,
    loading: false,
    error: null
  })

  const [progress, setProgress] = useState<CodingProgress | null>(null)
  const [tasks, setTasks] = useState<CodingTask[]>([])
  const [streamingUpdates, setStreamingUpdates] = useState<any[]>([])
  const [generatedFiles, setGeneratedFiles] = useState<Record<string, string>>({})

  /**
   * Fetch streaming updates
   */
  const fetchStreamingUpdates = useCallback(async () => {
    try {
      const response = await fetch('/api/coding?action=streaming_updates')
      const result = await response.json()

      if (result.success && result.data.updates) {
        setStreamingUpdates(result.data.updates)

        // Extract file content from updates
        const files: Record<string, string> = {}
        result.data.updates.forEach((update: any) => {
          if (update.type === 'generation' && update.fileContent) {
            files[update.filePath] = update.fileContent
          }
        })
        setGeneratedFiles(prev => ({ ...prev, ...files }))
      }
    } catch (error) {
      console.error('Failed to fetch streaming updates:', error)
    }
  }, [])

  /**
   * Fetch coding status
   */
  const fetchStatus = useCallback(async () => {
    try {
      const response = await fetch('/api/coding?action=status')
      const result = await response.json()

      if (result.success) {
        setStatus(prev => ({
          ...prev,
          isRunning: result.data.isRunning,
          error: null
        }))
      }
    } catch (error) {
      console.error('Failed to fetch coding status:', error)
      setStatus(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Unknown error'
      }))
    }
  }, [])

  /**
   * Fetch coding progress
   */
  const fetchProgress = useCallback(async () => {
    try {
      const response = await fetch('/api/coding?action=progress')
      const result = await response.json()

      if (result.success) {
        setProgress(result.data)
      }
    } catch (error) {
      console.error('Failed to fetch coding progress:', error)
    }
  }, [])

  /**
   * Fetch coding tasks
   */
  const fetchTasks = useCallback(async () => {
    try {
      const response = await fetch('/api/coding?action=tasks')
      const result = await response.json()

      if (result.success) {
        setTasks(result.data)
      }
    } catch (error) {
      console.error('Failed to fetch coding tasks:', error)
    }
  }, [])

  /**
   * Start coding workflow
   */
  const startCoding = useCallback(async (plan: any) => {
    try {
      setStatus(prev => ({ ...prev, loading: true, error: null }))

      const response = await fetch('/api/coding', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'start_coding',
          plan
        })
      })

      const result = await response.json()

      if (result.success) {
        setStatus(prev => ({
          ...prev,
          loading: false,
          isRunning: true,
          error: null
        }))
        
        // Start polling for updates
        startPolling()
        
        return { success: true, data: result.data }
      } else {
        setStatus(prev => ({
          ...prev,
          loading: false,
          error: result.error || 'Failed to start coding'
        }))
        
        return { success: false, error: result.error }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      setStatus(prev => ({
        ...prev,
        loading: false,
        error: errorMessage
      }))
      
      return { success: false, error: errorMessage }
    }
  }, [])

  /**
   * Stop coding workflow
   */
  const stopCoding = useCallback(async () => {
    try {
      setStatus(prev => ({ ...prev, loading: true, error: null }))

      const response = await fetch('/api/coding', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'stop_coding'
        })
      })

      const result = await response.json()

      if (result.success) {
        setStatus(prev => ({
          ...prev,
          loading: false,
          isRunning: false,
          error: null
        }))
        
        stopPolling()
        
        return { success: true }
      } else {
        setStatus(prev => ({
          ...prev,
          loading: false,
          error: result.error || 'Failed to stop coding'
        }))
        
        return { success: false, error: result.error }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      setStatus(prev => ({
        ...prev,
        loading: false,
        error: errorMessage
      }))
      
      return { success: false, error: errorMessage }
    }
  }, [])

  /**
   * Polling for real-time updates
   */
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null)

  const startPolling = useCallback(() => {
    if (pollingInterval) return

    const interval = setInterval(async () => {
      await Promise.all([
        fetchStatus(),
        fetchProgress(),
        fetchTasks(),
        fetchStreamingUpdates()
      ])
    }, 2000) // Poll every 2 seconds

    setPollingInterval(interval)
  }, [pollingInterval, fetchStatus, fetchProgress, fetchTasks])

  const stopPolling = useCallback(() => {
    if (pollingInterval) {
      clearInterval(pollingInterval)
      setPollingInterval(null)
    }
  }, [pollingInterval])

  /**
   * Refresh all data
   */
  const refresh = useCallback(async () => {
    await Promise.all([
      fetchStatus(),
      fetchProgress(),
      fetchTasks(),
      fetchStreamingUpdates()
    ])
  }, [fetchStatus, fetchProgress, fetchTasks, fetchStreamingUpdates])

  // Initialize on mount
  useEffect(() => {
    fetchStatus()
  }, [fetchStatus])

  // Start polling if coding is running
  useEffect(() => {
    if (status.isRunning && !pollingInterval) {
      startPolling()
    } else if (!status.isRunning && pollingInterval) {
      stopPolling()
    }
  }, [status.isRunning, pollingInterval, startPolling, stopPolling])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (pollingInterval) {
        clearInterval(pollingInterval)
      }
    }
  }, [pollingInterval])

  return {
    // Status
    status,
    progress,
    tasks,
    streamingUpdates,
    generatedFiles,

    // Actions
    startCoding,
    stopCoding,
    refresh,

    // Utilities
    isRunning: status.isRunning,
    isLoading: status.loading,
    hasError: !!status.error,
    completionPercentage: progress ? Math.round((progress.completedTasks / progress.totalTasks) * 100) : 0
  }
}

export default useCoding
