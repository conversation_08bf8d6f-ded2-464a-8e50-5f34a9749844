import neo4j from 'neo4j-driver';
import { createLogger } from '../../utils/logger.js';

const logger = createLogger('Neo4jClient');

/**
 * Neo4j database client with connection management and query execution
 */
export class Neo4jClient {
  constructor(config) {
    this.config = config;
    this.driver = null;
    this.isConnected = false;
  }

  /**
   * Initialize connection to Neo4j database
   */
  async connect() {
    try {
      logger.info('Connecting to Neo4j database', {
        uri: this.config.uri,
        database: this.config.database
      });

      this.driver = neo4j.driver(
        this.config.uri,
        neo4j.auth.basic(this.config.user, this.config.password),
        {
          maxConnectionPoolSize: this.config.maxConnectionPoolSize,
          connectionTimeout: this.config.connectionTimeout,
          maxTransactionRetryTime: this.config.maxTransactionRetryTime
        }
      );

      // Verify connectivity
      await this.driver.verifyConnectivity();
      this.isConnected = true;

      logger.info('Successfully connected to Neo4j database');
      
      // Initialize database schema
      await this.initializeSchema();
      
    } catch (error) {
      logger.error('Failed to connect to Neo4j database', { error: error.message });
      throw error;
    }
  }

  /**
   * Initialize database schema and constraints
   */
  async initializeSchema() {
    const session = this.session();
    
    try {
      logger.info('Initializing Neo4j schema');

      // Create constraints for unique identifiers
      const constraints = [
        'CREATE CONSTRAINT file_path_unique IF NOT EXISTS FOR (f:File) REQUIRE f.path IS UNIQUE',
        'CREATE CONSTRAINT function_signature_unique IF NOT EXISTS FOR (fn:Function) REQUIRE (fn.name, fn.signature) IS UNIQUE',
        'CREATE CONSTRAINT class_name_unique IF NOT EXISTS FOR (c:Class) REQUIRE (c.name, c.namespace) IS UNIQUE',
        'CREATE CONSTRAINT variable_scope_unique IF NOT EXISTS FOR (v:Variable) REQUIRE (v.name, v.scope) IS UNIQUE'
      ];

      for (const constraint of constraints) {
        try {
          await session.run(constraint);
        } catch (error) {
          // Constraint might already exist, log but don't fail
          logger.debug('Constraint creation skipped', { constraint, error: error.message });
        }
      }

      // Create indexes for performance
      const indexes = [
        'CREATE INDEX file_language_index IF NOT EXISTS FOR (f:File) ON (f.language)',
        'CREATE INDEX function_name_index IF NOT EXISTS FOR (fn:Function) ON (fn.name)',
        'CREATE INDEX class_name_index IF NOT EXISTS FOR (c:Class) ON (c.name)',
        'CREATE INDEX variable_name_index IF NOT EXISTS FOR (v:Variable) ON (v.name)',
        'CREATE INDEX timestamp_index IF NOT EXISTS FOR (n) ON (n.timestamp)'
      ];

      for (const index of indexes) {
        try {
          await session.run(index);
        } catch (error) {
          logger.debug('Index creation skipped', { index, error: error.message });
        }
      }

      logger.info('Neo4j schema initialization completed');
      
    } catch (error) {
      logger.error('Failed to initialize Neo4j schema', { error: error.message });
      throw error;
    } finally {
      await session.close();
    }
  }

  /**
   * Create a new session
   */
  session(database = null) {
    if (!this.isConnected) {
      throw new Error('Neo4j client not connected');
    }
    
    return this.driver.session({
      database: database || this.config.database
    });
  }

  /**
   * Execute a Cypher query
   */
  async run(query, parameters = {}, session = null) {
    const useSession = session || this.session();
    const startTime = Date.now();
    
    try {
      logger.debug('Executing Cypher query', {
        query: query.substring(0, 200),
        parameterCount: Object.keys(parameters).length
      });

      const result = await useSession.run(query, parameters);
      const duration = Date.now() - startTime;
      
      logger.debug('Query executed successfully', {
        recordCount: result.records.length,
        duration
      });

      return result;
      
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error('Query execution failed', {
        query: query.substring(0, 200),
        error: error.message,
        duration
      });
      throw error;
    } finally {
      if (!session) {
        await useSession.close();
      }
    }
  }

  /**
   * Execute a transaction
   */
  async executeTransaction(transactionFunction) {
    const session = this.session();
    
    try {
      return await session.executeWrite(transactionFunction);
    } catch (error) {
      logger.error('Transaction execution failed', { error: error.message });
      throw error;
    } finally {
      await session.close();
    }
  }

  /**
   * Execute a read transaction
   */
  async executeReadTransaction(transactionFunction) {
    const session = this.session();
    
    try {
      return await session.executeRead(transactionFunction);
    } catch (error) {
      logger.error('Read transaction execution failed', { error: error.message });
      throw error;
    } finally {
      await session.close();
    }
  }

  /**
   * Check database health
   */
  async healthCheck() {
    try {
      const result = await this.run('RETURN 1 as health');
      return {
        status: 'healthy',
        connected: this.isConnected,
        recordCount: result.records.length
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        connected: false,
        error: error.message
      };
    }
  }

  /**
   * Get database statistics
   */
  async getStatistics() {
    try {
      const queries = [
        'MATCH (n) RETURN count(n) as nodeCount',
        'MATCH ()-[r]->() RETURN count(r) as relationshipCount',
        'MATCH (f:File) RETURN count(f) as fileCount',
        'MATCH (fn:Function) RETURN count(fn) as functionCount',
        'MATCH (c:Class) RETURN count(c) as classCount'
      ];

      const results = await Promise.all(
        queries.map(query => this.run(query))
      );

      return {
        nodeCount: results[0].records[0].get('nodeCount').toNumber(),
        relationshipCount: results[1].records[0].get('relationshipCount').toNumber(),
        fileCount: results[2].records[0].get('fileCount').toNumber(),
        functionCount: results[3].records[0].get('functionCount').toNumber(),
        classCount: results[4].records[0].get('classCount').toNumber()
      };
    } catch (error) {
      logger.error('Failed to get database statistics', { error: error.message });
      throw error;
    }
  }

  /**
   * Close the connection
   */
  async disconnect() {
    if (this.driver) {
      logger.info('Disconnecting from Neo4j database');
      await this.driver.close();
      this.isConnected = false;
      logger.info('Disconnected from Neo4j database');
    }
  }
}

export default Neo4jClient;
