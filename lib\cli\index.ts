#!/usr/bin/env node

/**
 * AG3NT Framework CLI
 * 
 * Command-line interface for AG3NT Framework
 * Provides tools for project creation, agent management, and framework operations
 */

import { Command } from 'commander'
import chalk from 'chalk'
import ora from 'ora'
import inquirer from 'inquirer'
import { createProject } from './commands/create'
import { initProject } from './commands/init'
import { addAgent } from './commands/add-agent'
import { listAgents } from './commands/list-agents'
import { runWorkflow } from './commands/run-workflow'
import { generateDocs } from './commands/generate-docs'
import { validateProject } from './commands/validate'
import { upgradeFramework } from './commands/upgrade'
import { marketplaceCommands } from './commands/marketplace'
import { monitoringCommands } from './commands/monitoring'
import { optimizationCommands } from './commands/optimization'

const program = new Command()

// CLI Header
console.log(chalk.cyan(`
  ╔═══════════════════════════════════════╗
  ║           AG3NT Framework             ║
  ║   Advanced Multi-Agent Development    ║
  ╚═══════════════════════════════════════╝
`))

program
  .name('ag3nt')
  .description('AG3NT Framework - Advanced Multi-Agent Development Platform')
  .version('1.0.0')

// Create new project
program
  .command('create <project-name>')
  .description('Create a new AG3NT project')
  .option('-t, --template <template>', 'Project template (basic, advanced, enterprise)', 'basic')
  .option('-d, --directory <dir>', 'Target directory')
  .option('--skip-install', 'Skip npm install')
  .option('--skip-git', 'Skip git initialization')
  .action(async (projectName, options) => {
    const spinner = ora('Creating AG3NT project...').start()
    try {
      await createProject(projectName, options)
      spinner.succeed(chalk.green(`Project ${projectName} created successfully!`))
      
      console.log(chalk.cyan('\nNext steps:'))
      console.log(chalk.white(`  cd ${projectName}`))
      console.log(chalk.white('  ag3nt dev'))
    } catch (error) {
      spinner.fail(chalk.red('Failed to create project'))
      console.error(error)
      process.exit(1)
    }
  })

// Initialize existing project
program
  .command('init')
  .description('Initialize AG3NT in existing project')
  .option('-f, --force', 'Force initialization even if AG3NT config exists')
  .action(async (options) => {
    const spinner = ora('Initializing AG3NT...').start()
    try {
      await initProject(options)
      spinner.succeed(chalk.green('AG3NT initialized successfully!'))
    } catch (error) {
      spinner.fail(chalk.red('Failed to initialize AG3NT'))
      console.error(error)
      process.exit(1)
    }
  })

// Agent management
const agentCommand = program
  .command('agent')
  .description('Agent management commands')

agentCommand
  .command('add <agent-type>')
  .description('Add a new agent to the project')
  .option('-n, --name <name>', 'Agent name')
  .option('-c, --config <config>', 'Agent configuration file')
  .action(async (agentType, options) => {
    const spinner = ora(`Adding ${agentType} agent...`).start()
    try {
      await addAgent(agentType, options)
      spinner.succeed(chalk.green(`${agentType} agent added successfully!`))
    } catch (error) {
      spinner.fail(chalk.red('Failed to add agent'))
      console.error(error)
      process.exit(1)
    }
  })

agentCommand
  .command('list')
  .description('List all agents in the project')
  .option('-v, --verbose', 'Show detailed information')
  .action(async (options) => {
    try {
      await listAgents(options)
    } catch (error) {
      console.error(chalk.red('Failed to list agents'))
      console.error(error)
      process.exit(1)
    }
  })

// Workflow management
const workflowCommand = program
  .command('workflow')
  .description('Workflow management commands')

workflowCommand
  .command('run <workflow-name>')
  .description('Run a workflow')
  .option('-i, --input <input>', 'Input data (JSON string or file path)')
  .option('-c, --config <config>', 'Workflow configuration')
  .option('-w, --watch', 'Watch mode for development')
  .action(async (workflowName, options) => {
    const spinner = ora(`Running workflow: ${workflowName}...`).start()
    try {
      await runWorkflow(workflowName, options)
      spinner.succeed(chalk.green('Workflow completed successfully!'))
    } catch (error) {
      spinner.fail(chalk.red('Workflow failed'))
      console.error(error)
      process.exit(1)
    }
  })

// Development commands
program
  .command('dev')
  .description('Start development server')
  .option('-p, --port <port>', 'Port number', '3000')
  .option('--hot-reload', 'Enable hot reload')
  .action(async (options) => {
    console.log(chalk.cyan('Starting AG3NT development server...'))
    // Implementation would start the development server
    console.log(chalk.green(`Server running on port ${options.port}`))
  })

program
  .command('build')
  .description('Build the project')
  .option('-e, --env <environment>', 'Build environment', 'production')
  .option('--analyze', 'Analyze bundle size')
  .action(async (options) => {
    const spinner = ora('Building project...').start()
    try {
      // Implementation would build the project
      spinner.succeed(chalk.green('Build completed successfully!'))
    } catch (error) {
      spinner.fail(chalk.red('Build failed'))
      console.error(error)
      process.exit(1)
    }
  })

// Documentation
program
  .command('docs')
  .description('Generate documentation')
  .option('-o, --output <dir>', 'Output directory', 'docs')
  .option('-f, --format <format>', 'Documentation format (html, markdown)', 'html')
  .action(async (options) => {
    const spinner = ora('Generating documentation...').start()
    try {
      await generateDocs(options)
      spinner.succeed(chalk.green('Documentation generated successfully!'))
    } catch (error) {
      spinner.fail(chalk.red('Failed to generate documentation'))
      console.error(error)
      process.exit(1)
    }
  })

// Validation
program
  .command('validate')
  .description('Validate project configuration and setup')
  .option('--fix', 'Automatically fix issues where possible')
  .action(async (options) => {
    const spinner = ora('Validating project...').start()
    try {
      const issues = await validateProject(options)
      if (issues.length === 0) {
        spinner.succeed(chalk.green('Project validation passed!'))
      } else {
        spinner.warn(chalk.yellow(`Found ${issues.length} issues`))
        issues.forEach(issue => console.log(chalk.yellow(`  - ${issue}`)))
      }
    } catch (error) {
      spinner.fail(chalk.red('Validation failed'))
      console.error(error)
      process.exit(1)
    }
  })

// Upgrade
program
  .command('upgrade')
  .description('Upgrade AG3NT Framework')
  .option('--version <version>', 'Target version')
  .option('--preview', 'Show what would be upgraded')
  .action(async (options) => {
    const spinner = ora('Checking for updates...').start()
    try {
      await upgradeFramework(options)
      spinner.succeed(chalk.green('Framework upgraded successfully!'))
    } catch (error) {
      spinner.fail(chalk.red('Upgrade failed'))
      console.error(error)
      process.exit(1)
    }
  })

// Marketplace commands
marketplaceCommands(program)

// Monitoring commands
monitoringCommands(program)

// Optimization commands
optimizationCommands(program)

// Interactive mode
program
  .command('interactive')
  .alias('i')
  .description('Start interactive mode')
  .action(async () => {
    console.log(chalk.cyan('Welcome to AG3NT Interactive Mode!'))
    
    while (true) {
      const { action } = await inquirer.prompt([
        {
          type: 'list',
          name: 'action',
          message: 'What would you like to do?',
          choices: [
            { name: 'Create new project', value: 'create' },
            { name: 'Add agent', value: 'add-agent' },
            { name: 'Run workflow', value: 'run-workflow' },
            { name: 'Generate documentation', value: 'docs' },
            { name: 'Validate project', value: 'validate' },
            { name: 'Browse marketplace', value: 'marketplace' },
            { name: 'View monitoring', value: 'monitoring' },
            { name: 'Exit', value: 'exit' }
          ]
        }
      ])

      if (action === 'exit') {
        console.log(chalk.cyan('Goodbye!'))
        break
      }

      // Handle interactive actions
      try {
        switch (action) {
          case 'create':
            const { projectName } = await inquirer.prompt([
              { type: 'input', name: 'projectName', message: 'Project name:' }
            ])
            await createProject(projectName, {})
            break
          case 'add-agent':
            const { agentType } = await inquirer.prompt([
              {
                type: 'list',
                name: 'agentType',
                message: 'Agent type:',
                choices: ['planning', 'executor', 'frontend-coder', 'backend-coder', 'tester', 'reviewer']
              }
            ])
            await addAgent(agentType, {})
            break
          // Add more interactive handlers
        }
        console.log(chalk.green('Action completed successfully!'))
      } catch (error) {
        console.error(chalk.red('Action failed:'), error)
      }
    }
  })

// Global error handler
process.on('uncaughtException', (error) => {
  console.error(chalk.red('Uncaught Exception:'), error)
  process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error(chalk.red('Unhandled Rejection at:'), promise, chalk.red('reason:'), reason)
  process.exit(1)
})

// Parse command line arguments
program.parse()

// Show help if no command provided
if (!process.argv.slice(2).length) {
  program.outputHelp()
}
