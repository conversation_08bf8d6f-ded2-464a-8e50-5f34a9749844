"use client"

import { useRef } from "react"
import type { PlanningTask } from "@/types/planning"
import type { ChatMessage } from "@/hooks/use-chat-state"

interface ChatSidebarProps {
  sidebarWidth: number
  chatMessages: ChatMessage[]
  chatInput: string
  setChatInput: (input: string) => void
  addChatMessage: (message: Omit<ChatMessage, 'id' | 'timestamp'>) => void
  
  // Planning state
  isProcessing: boolean
  hasStarted: boolean
  tasks: PlanningTask[]
  currentTaskIndex: number
  setActiveTab: (tab: string) => void
  
  // Coding state
  isCoding: boolean
  codingTasks: any[]
  activeFile: string | null
}

export function ChatSidebar({
  sidebarWidth,
  chatMessages,
  chatInput,
  setChatInput,
  addChatMessage,
  isProcessing,
  hasStarted,
  tasks,
  currentTaskIndex,
  setActiveTab,
  isCoding,
  codingTasks,
  activeFile,
}: ChatSidebarProps) {
  const taskListRef = useRef<HTMLDivElement>(null)

  const handleSendMessage = () => {
    if (chatInput.trim()) {
      // Add user message
      addChatMessage({
        type: 'user',
        content: chatInput,
      })
      setChatInput("")

      // Add AI response
      setTimeout(() => {
        addChatMessage({
          type: 'ai',
          content: "I'll help you with that! Let me analyze your request and create a plan.",
        })
      }, 500)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  return (
    <aside
      style={{ width: sidebarWidth }}
      className="bg-[#0a0a0a] rounded-xl flex flex-col overflow-hidden relative h-full shadow-xl"
    >
      {/* Chat Header */}
      <div className="px-4 md:px-6 py-3 md:py-4 border-b border-[#1a1a1a] flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <h2 className="font-medium text-white text-sm md:text-base">Chat</h2>
        </div>
      </div>

      {/* Chat Messages */}
      <div className="flex-1 min-h-0 overflow-hidden">
        <div className="px-4 md:px-6 py-3 md:py-4 space-y-3 md:space-y-4 h-full overflow-y-auto">
          {chatMessages.length === 0 ? (
            <div className="text-center text-gray-400 text-sm">
              Start a conversation to begin planning your project
            </div>
          ) : (
            chatMessages.map((message) => (
              <div key={message.id} className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                <div className={`max-w-[75%] p-3 rounded-lg ${
                  message.type === 'user'
                    ? 'bg-[#ff2d55] text-white'
                    : 'bg-[#1a1a1a] text-gray-200'
                }`}>
                  <div className="text-sm">{message.content}</div>
                  <div className="text-xs opacity-70 mt-1">
                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </div>
                </div>
              </div>
            ))
          )}

          {/* Planning Progress Message */}
          {(isProcessing || (!isProcessing && hasStarted && tasks.some(t => t.completed))) && (
            <div className="flex justify-start">
              <div className="max-w-[75%] p-4 rounded-lg bg-[#1a1a1a] text-gray-200">
                <div className="text-center mb-4">
                  <h3 className="text-lg font-bold text-white">
                    {isProcessing ? (
                      <>
                        <span className="text-white">AG3N</span>
                        <span className="text-[#ff2d55]">T</span>
                        <span className="text-white"> is Planning Your Project</span>
                      </>
                    ) : 'Planning Complete'}
                  </h3>
                </div>

                {/* Scrolling Task Container */}
                <div
                  ref={taskListRef}
                  className="h-[180px] overflow-y-auto space-y-2 pr-2 planning-scroll"
                  style={{
                    scrollbarWidth: 'thin',
                    scrollbarColor: '#374151 transparent'
                  }}
                >
                  {tasks.filter((_, originalIndex) => {
                    const maxIndex = isProcessing ? Math.max(currentTaskIndex + 1, 0) : tasks.length
                    return originalIndex <= maxIndex
                  }).map((task, displayIndex) => {
                    const originalIndex = tasks.findIndex(t => t.id === task.id)
                    const isActive = originalIndex === currentTaskIndex && isProcessing
                    const isCompleted = task.completed

                    return (
                      <div
                        key={task.id}
                        data-task-index={originalIndex}
                        className="flex items-center gap-3 py-2"
                      >
                        <div className="w-6 h-6 flex items-center justify-center flex-shrink-0">
                          {isCompleted ? (
                            <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                              <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                              </svg>
                            </div>
                          ) : isActive ? (
                            <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center animate-pulse">
                              <div className="w-2 h-2 bg-white rounded-full"></div>
                            </div>
                          ) : (
                            <div className="w-6 h-6 border-2 border-gray-600 rounded-full"></div>
                          )}
                        </div>
                        <span className={`text-sm ${
                          isCompleted ? 'text-white' : isActive ? 'text-white' : 'text-gray-400'
                        }`}>
                          {task.title}
                        </span>
                      </div>
                    )
                  })}
                </div>

                {!isProcessing && (
                  <div className="mt-4 pt-3 border-t border-gray-600 text-center">
                    <button
                      onClick={() => setActiveTab("planning")}
                      className="text-sm text-blue-400 hover:text-blue-300 transition-colors"
                    >
                      View detailed plan →
                    </button>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Coding Progress Message */}
          {isCoding && (
            <div className="flex justify-start">
              <div className="max-w-[75%] p-4 rounded-lg bg-[#1a1a1a] text-gray-200">
                <div className="text-center mb-4">
                  <h3 className="text-lg font-bold text-white">
                    <span className="text-white">AG3N</span>
                    <span className="text-[#ff2d55]">T</span>
                    <span className="text-white"> is Building Your Project</span>
                  </h3>
                  <p className="text-sm text-gray-400 mt-1">
                    Autonomous coding agents are generating your project
                  </p>
                </div>

                {/* Scrolling Coding Task Container */}
                <div
                  className="h-[180px] overflow-y-auto space-y-2 pr-2 planning-scroll"
                  style={{
                    scrollbarWidth: 'thin',
                    scrollbarColor: '#374151 transparent'
                  }}
                >
                  {codingTasks.map((task, index) => {
                    const isActive = task.status === 'in_progress'
                    const isCompleted = task.status === 'completed'

                    return (
                      <div
                        key={task.id}
                        className="flex items-center gap-3 py-2"
                      >
                        <div className="w-6 h-6 flex items-center justify-center flex-shrink-0">
                          {isCompleted ? (
                            <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                              <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                              </svg>
                            </div>
                          ) : isActive ? (
                            <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center animate-pulse">
                              <div className="w-2 h-2 bg-white rounded-full"></div>
                            </div>
                          ) : (
                            <div className="w-6 h-6 border-2 border-gray-600 rounded-full"></div>
                          )}
                        </div>
                        <div className="flex-1">
                          <span className={`text-sm ${
                            isCompleted ? 'text-white' : isActive ? 'text-white' : 'text-gray-400'
                          }`}>
                            {task.title}
                          </span>
                          {isActive && activeFile && (
                            <div className="text-xs text-green-400 mt-1 font-mono">
                              📄 {activeFile}
                            </div>
                          )}
                          {isCompleted && task.output && (
                            <div className="text-xs text-green-400 mt-1">
                              ✓ {task.output.files?.length || 0} files generated
                            </div>
                          )}
                        </div>
                      </div>
                    )
                  })}
                </div>

                {/* Overall Progress Bar */}
                <div className="mt-4 pt-3 border-t border-gray-600">
                  <div className="flex items-center justify-between text-sm text-gray-300 mb-2">
                    <span>Overall Progress</span>
                    <span>{Math.round((codingTasks.filter(t => t.status === 'completed').length / codingTasks.length) * 100)}%</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-green-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${(codingTasks.filter(t => t.status === 'completed').length / codingTasks.length) * 100}%` }}
                    ></div>
                  </div>
                  <div className="mt-2 text-center">
                    <button
                      onClick={() => setActiveTab("code")}
                      className="text-sm text-green-400 hover:text-green-300 transition-colors"
                    >
                      View live code generation →
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Chat Input */}
      <div className="shrink-0 p-3 md:p-4 border-t border-[#1a1a1a]">
        <div className="relative bg-[#1a1a1a] border border-[#333] rounded-lg">
          <textarea
            value={chatInput}
            onChange={(e) => setChatInput(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Ask a follow-up..."
            className="w-full min-h-[48px] max-h-[144px] p-3 pr-24 text-white placeholder-gray-400 bg-transparent border-0 resize-none focus:outline-none focus:ring-0 leading-6 rounded-lg"
            style={{
              scrollbarWidth: 'thin',
              scrollbarColor: '#666 transparent'
            }}
          />

          {/* Action Buttons */}
          <div className="absolute bottom-2 md:bottom-3 right-2 md:right-3 flex items-center gap-1 md:gap-2">
            {/* Attach Button */}
            <button
              className="w-7 h-7 md:w-8 md:h-8 flex items-center justify-center text-gray-400 hover:text-white transition-colors rounded clickable"
              title="Attach file"
            >
              <svg width="14" height="14" className="md:w-4 md:h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
              </svg>
            </button>

            {/* Send Button */}
            <button
              onClick={handleSendMessage}
              disabled={!chatInput.trim()}
              className="w-7 h-7 md:w-8 md:h-8 flex items-center justify-center text-gray-400 hover:text-white transition-colors rounded disabled:opacity-50 disabled:cursor-not-allowed clickable"
              title="Send message"
            >
              <svg width="14" height="14" className="md:w-4 md:h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </aside>
  )
}
