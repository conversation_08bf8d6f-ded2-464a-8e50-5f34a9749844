import { useState } from "react"
import type { PlanningTask, Question } from "@/types/planning"

const getBasePlanningTasks = (): PlanningTask[] => [
  { id: "analyze", title: "Analyze project requirements", completed: false },
  { id: "clarify", title: "Gather additional details", completed: false },
  { id: "summary", title: "Generate project summary", completed: false },
  { id: "techstack", title: "Select technology stack", completed: false },
  { id: "prd", title: "Create requirements document", completed: false },
]

export function usePlanningState() {
  // Core planning state
  const [userPrompt, setUserPrompt] = useState("")
  const [hasStarted, setHasStarted] = useState(false)
  const [isInteractive, setIsInteractive] = useState(false)
  const [tasks, setTasks] = useState<PlanningTask[]>(getBasePlanningTasks())
  const [currentTaskIndex, setCurrentTaskIndex] = useState(-1)
  const [isProcessing, setIsProcessing] = useState(false)
  const [results, setResults] = useState<Record<string, any>>({})
  const [showResults, setShowResults] = useState(false)
  
  // Question/Answer flow
  const [currentQuestion, setCurrentQuestion] = useState<Question | null>(null)
  const [questionAnswer, setQuestionAnswer] = useState("")
  const [userAnswers, setUserAnswers] = useState<Record<string, string>>({})
  
  // Context and error handling
  const [planningContext, setPlanningContext] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  const [canRetry, setCanRetry] = useState(false)
  
  // Image processing
  const [uploadedImages, setUploadedImages] = useState<File[]>([])
  const [isProcessingImages, setIsProcessingImages] = useState(false)
  const [designStyleGuideState, setDesignStyleGuideState] = useState<string | null>(null)

  // Planning section selection
  const [selectedPlanningSection, setSelectedPlanningSection] = useState<string | null>(null)

  return {
    // Core planning state
    userPrompt,
    setUserPrompt,
    hasStarted,
    setHasStarted,
    isInteractive,
    setIsInteractive,
    tasks,
    setTasks,
    currentTaskIndex,
    setCurrentTaskIndex,
    isProcessing,
    setIsProcessing,
    results,
    setResults,
    showResults,
    setShowResults,
    
    // Question/Answer flow
    currentQuestion,
    setCurrentQuestion,
    questionAnswer,
    setQuestionAnswer,
    userAnswers,
    setUserAnswers,
    
    // Context and error handling
    planningContext,
    setPlanningContext,
    error,
    setError,
    canRetry,
    setCanRetry,
    
    // Image processing
    uploadedImages,
    setUploadedImages,
    isProcessingImages,
    setIsProcessingImages,
    designStyleGuideState,
    setDesignStyleGuideState,

    // Planning section selection
    selectedPlanningSection,
    setSelectedPlanningSection,

    // Helper function
    getBasePlanningTasks,
  }
}
