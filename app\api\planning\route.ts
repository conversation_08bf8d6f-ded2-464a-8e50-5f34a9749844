import { type NextRequest, NextResponse } from "next/server"
import { aiService } from "@/lib/ai-service"
import { trackSessionStart, trackStepStart, trackStepComplete } from "@/lib/progress-tracker"
import { nanoid } from "nanoid"

export async function POST(request: NextRequest) {
  try {
    const { prompt, isInteractive, answers = {}, designStyleGuide, hasImages, userPreferences } = await request.json()

    if (!prompt) {
      return NextResponse.json({ error: "Prompt is required" }, { status: 400 })
    }

    // Generate session ID and start progress tracking
    const sessionId = `session-${nanoid()}`
    console.log("🚀 Starting planning session:", sessionId)
    console.log("📝 Prompt:", prompt)
    if (hasImages) {
      console.log("📸 Planning includes design style guide from uploaded images")
    }

    // Start progress tracking
    trackSessionStart(sessionId)
    trackStepStart(sessionId, "analyze")

    try {
      console.log("🧠 Executing AI analysis with progress tracking...")
      console.log("🤖 Using model:", userPreferences?.model || "default")

      // Configure AI service with user preferences
      const aiConfig = {
        model: userPreferences?.model,
        apiKey: userPreferences?.apiKey
      }

      const analysis = await aiService.analyzePrompt(prompt, { designStyleGuide, hasImages }, aiConfig)
      console.log("✅ AI analysis completed successfully")

      // Track step completion
      trackStepComplete(sessionId, "analyze", analysis)

      const result = {
        success: true,
        sessionId,
        prompt,
        isInteractive,
        answers,
        currentStep: "analyze",
        results: {
          analyze: analysis,
          ...(designStyleGuide && { design: designStyleGuide })
        },
        needsInput: false,
        completed: false,
        hasImages,
        designStyleGuide,
        progress: 8, // analyze is ~8% of total steps
        totalSteps: 13
      }

      console.log("📊 Planning session initialized with progress tracking")
      return NextResponse.json(result)

    } catch (aiError) {
      console.error("❌ AI analysis failed:", aiError)
      return NextResponse.json(
        {
          error: "AI analysis failed",
          details: aiError instanceof Error ? aiError.message : "Unknown AI error",
          suggestion: "Please check your OPENROUTER_API_KEY and try again",
          sessionId
        },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error("❌ Planning API error:", error)
    return NextResponse.json(
      {
        error: "Failed to process planning request",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    )
  }
}
