import { useState } from "react"

export interface ChatMessage {
  id: string
  type: 'user' | 'ai'
  content: string
  timestamp: Date
}

export function useChatState() {
  // Chat state
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([])
  const [chatInput, setChatInput] = useState("")
  
  // Sidebar state
  const [sidebarWidth, setSidebarWidth] = useState(500) // Default width for SSR (1/3 page width)
  const [isResizing, setIsResizing] = useState(false)

  // Helper functions
  const addChatMessage = (message: Omit<ChatMessage, 'id' | 'timestamp'>) => {
    const newMessage: ChatMessage = {
      ...message,
      id: Date.now().toString(),
      timestamp: new Date(),
    }
    setChatMessages(prev => [...prev, newMessage])
  }

  const clearChat = () => {
    setChatMessages([])
  }

  return {
    chatMessages,
    setChatMessages,
    chatInput,
    setChatInput,
    sidebarWidth,
    setSidebarWidth,
    isResizing,
    setIsResizing,
    addChatMessage,
    clearChat,
  }
}
