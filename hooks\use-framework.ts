/**
 * AG3NT Platform - Framework Hook
 *
 * React hook for interacting with the Isolated AG3NT Framework
 */

import { useState, useEffect, useCallback } from 'react'

export interface FrameworkStatus {
  initialized: boolean
  agentCount: number
  loading: boolean
  error: string | null
}

export interface FrameworkAnalytics {
  totalProjects: number
  successRate: number
  averageExecutionTime: number
  agentUtilization: Record<string, number>
}

export interface ProjectPlanRequest {
  projectName: string
  projectDescription: string
  projectType: string
  frontendFramework?: string
  backendFramework?: string
  database?: string
  features: string[]
}

export interface ProjectPlanResult {
  success: boolean
  data?: any
  error?: string
  executionTime?: number
  agentsUsed?: string[]
  workflowId?: string
}

export interface AgentInfo {
  id: string
  type: string
  status: string
}

export function useFramework() {
  const [status, setStatus] = useState<FrameworkStatus>({
    initialized: false,
    agentCount: 0,
    loading: true,
    error: null
  })

  const [analytics, setAnalytics] = useState<FrameworkAnalytics | null>(null)
  const [agents, setAgents] = useState<AgentInfo[]>([])

  /**
   * Fetch framework status
   */
  const fetchStatus = useCallback(async () => {
    try {
      setStatus(prev => ({ ...prev, loading: true, error: null }))

      const response = await fetch('/api/framework?action=status')
      const result = await response.json()

      if (result.success) {
        setStatus({
          initialized: result.data.initialized,
          agentCount: result.data.agentCount,
          loading: false,
          error: null
        })
      } else {
        setStatus(prev => ({
          ...prev,
          loading: false,
          error: result.error || 'Failed to fetch status'
        }))
      }
    } catch (error) {
      setStatus(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }))
    }
  }, [])

  /**
   * Initialize framework
   */
  const initializeFramework = useCallback(async () => {
    try {
      setStatus(prev => ({ ...prev, loading: true, error: null }))

      const response = await fetch('/api/framework', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'initialize' })
      })

      const result = await response.json()

      if (result.success) {
        await fetchStatus() // Refresh status after initialization
      } else {
        setStatus(prev => ({
          ...prev,
          loading: false,
          error: result.error || 'Failed to initialize framework'
        }))
      }
    } catch (error) {
      setStatus(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }))
    }
  }, [fetchStatus])

  /**
   * Fetch framework analytics
   */
  const fetchAnalytics = useCallback(async () => {
    try {
      const response = await fetch('/api/framework?action=analytics')
      const result = await response.json()

      if (result.success) {
        setAnalytics(result.data)
      } else {
        console.warn('Analytics fetch failed:', result.error)
        // Set default analytics
        setAnalytics({
          totalProjects: 0,
          successRate: 1.0,
          averageExecutionTime: 0,
          agentUtilization: {}
        })
      }
    } catch (error) {
      console.error('Failed to fetch analytics:', error)
      // Set default analytics
      setAnalytics({
        totalProjects: 0,
        successRate: 1.0,
        averageExecutionTime: 0,
        agentUtilization: {}
      })
    }
  }, [])

  /**
   * Fetch available agents
   */
  const fetchAgents = useCallback(async () => {
    try {
      const response = await fetch('/api/framework?action=agents')
      const result = await response.json()

      if (result.success) {
        setAgents(result.data)
      }
    } catch (error) {
      console.error('Failed to fetch agents:', error)
    }
  }, [])

  /**
   * Plan a project using the framework
   */
  const planProject = useCallback(async (request: ProjectPlanRequest): Promise<ProjectPlanResult> => {
    try {
      const response = await fetch('/api/framework', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'plan_project',
          ...request
        })
      })

      const result = await response.json()
      return result
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }, [])

  /**
   * Execute a task with a specific agent
   */
  const executeAgentTask = useCallback(async (agentType: string, task: any): Promise<ProjectPlanResult> => {
    try {
      const response = await fetch('/api/framework', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'execute_task',
          agentType,
          task
        })
      })

      const result = await response.json()
      return result
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }, [])

  /**
   * Refresh all data
   */
  const refresh = useCallback(async () => {
    await Promise.all([
      fetchStatus(),
      fetchAnalytics(),
      fetchAgents()
    ])
  }, [fetchStatus, fetchAnalytics, fetchAgents])

  // Initialize on mount
  useEffect(() => {
    const initializeOnMount = async () => {
      // First try to get status
      await fetchStatus()

      // Check if framework needs initialization
      try {
        const statusResponse = await fetch('/api/framework?action=status')
        const statusResult = await statusResponse.json()

        if (statusResult.success && !statusResult.data.initialized) {
          console.log('🚀 Auto-initializing framework...')
          await initializeFramework()
        }
      } catch (error) {
        console.warn('Framework auto-initialization check failed:', error)
      }
    }

    initializeOnMount()
  }, [fetchStatus, initializeFramework])

  // Fetch analytics and agents when framework is initialized
  useEffect(() => {
    if (status.initialized && !status.loading) {
      fetchAnalytics()
      fetchAgents()
    }
  }, [status.initialized, status.loading, fetchAnalytics, fetchAgents])

  return {
    // Status
    status,
    analytics,
    agents,

    // Actions
    initializeFramework,
    planProject,
    executeAgentTask,
    refresh,

    // Utilities
    isReady: status.initialized && !status.loading,
    hasError: !!status.error
  }
}

export default useFramework
