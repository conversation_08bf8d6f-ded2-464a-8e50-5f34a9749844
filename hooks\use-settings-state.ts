import { useState } from "react"

export function useSettingsState() {
  // Settings state
  const [isSettingsOpen, setIsSettingsOpen] = useState(false)
  const [userApiKey, setUserApiKey] = useState("")
  const [preferredModel, setPreferredModel] = useState("anthropic/claude-sonnet-4")
  const [isAutonomousMode, setIsAutonomousMode] = useState(true)

  return {
    isSettingsOpen,
    setIsSettingsOpen,
    userApiKey,
    setUserApiKey,
    preferredModel,
    setPreferredModel,
    isAutonomousMode,
    setIsAutonomousMode,
  }
}
