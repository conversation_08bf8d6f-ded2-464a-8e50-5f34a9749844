/**
 * AG3NT Library Exports
 * 
 * Main entry point for all AG3NT library components
 */

// Context Engine - MasterMold (Single Source of Truth)
export { UnifiedContextEngine } from './unified-context-engine-v2'
export type {
  AgentType,
  AgentContextScope,
  ProjectContext,
  CodebaseContext,
  FileContext,
  SymbolContext,
  ArchitecturalInsights,
  ContextNode,
  EnhancedContext,
  ContextEnrichment
} from './unified-context-engine-v2'

// Planning components
export { PlanningAgent } from './planning-agent'
export { PlanningGraph } from './planning-graph'

// Types
export type * from '../types/context-engine'
export type * from '../types/planning'

// Default export - the MasterMold context engine
export { UnifiedContextEngine as default } from './unified-context-engine-v2'
