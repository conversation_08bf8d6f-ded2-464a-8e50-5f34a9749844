#!/usr/bin/env node

/**
 * AG3NT E2B Setup Verification Script
 * 
 * Checks if E2B integration is properly configured
 */

const fs = require('fs')
const path = require('path')

console.log('🔍 AG3NT E2B Integration Setup Check\n')

// Check 1: Environment Variables
console.log('1. Checking environment variables...')
const envPath = path.join(process.cwd(), '.env.local')
let envVars = {}

if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8')
  envContent.split('\n').forEach(line => {
    const [key, value] = line.split('=')
    if (key && value) {
      envVars[key.trim()] = value.trim()
    }
  })
  console.log('   ✅ .env.local file found')
} else {
  console.log('   ❌ .env.local file not found')
}

// Check E2B API key
if (envVars.E2B_API_KEY || process.env.E2B_API_KEY) {
  console.log('   ✅ E2B_API_KEY is set')
} else {
  console.log('   ❌ E2B_API_KEY is missing')
  console.log('      Add E2B_API_KEY=your_api_key to .env.local')
}

// Check OpenAI API key (for AI service)
if (envVars.OPENAI_API_KEY || process.env.OPENAI_API_KEY) {
  console.log('   ✅ OPENAI_API_KEY is set')
} else {
  console.log('   ❌ OPENAI_API_KEY is missing')
  console.log('      Add OPENAI_API_KEY=your_api_key to .env.local')
}

console.log()

// Check 2: Dependencies
console.log('2. Checking dependencies...')
const packageJsonPath = path.join(process.cwd(), 'package.json')

if (fs.existsSync(packageJsonPath)) {
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
  const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies }
  
  const requiredDeps = [
    '@e2b/code-interpreter',
    '@ai-sdk/anthropic',
    '@ai-sdk/openai',
    'ai',
    '@monaco-editor/react',
    'monaco-editor',
    'prismjs',
    'react-textarea-autosize'
  ]
  
  requiredDeps.forEach(dep => {
    if (dependencies[dep]) {
      console.log(`   ✅ ${dep} is installed (${dependencies[dep]})`)
    } else {
      console.log(`   ❌ ${dep} is missing`)
    }
  })
} else {
  console.log('   ❌ package.json not found')
}

console.log()

// Check 3: Required Files
console.log('3. Checking required files...')
const requiredFiles = [
  'app/api/sandbox/route.ts',
  'lib/e2b-schema.ts',
  'components/e2b-preview.tsx',
  'components/live-code-display.tsx',
  'lib/coding-workflow-orchestrator.ts'
]

requiredFiles.forEach(file => {
  const filePath = path.join(process.cwd(), file)
  if (fs.existsSync(filePath)) {
    console.log(`   ✅ ${file}`)
  } else {
    console.log(`   ❌ ${file} is missing`)
  }
})

console.log()

// Check 4: API Routes
console.log('4. Checking API route structure...')
const apiPath = path.join(process.cwd(), 'app/api')

if (fs.existsSync(apiPath)) {
  const apiRoutes = fs.readdirSync(apiPath)
  
  const expectedRoutes = ['sandbox', 'coding', 'planning']
  expectedRoutes.forEach(route => {
    if (apiRoutes.includes(route)) {
      console.log(`   ✅ /api/${route}`)
    } else {
      console.log(`   ❌ /api/${route} is missing`)
    }
  })
} else {
  console.log('   ❌ app/api directory not found')
}

console.log()

// Check 5: TypeScript Configuration
console.log('5. Checking TypeScript configuration...')
const tsConfigPath = path.join(process.cwd(), 'tsconfig.json')

if (fs.existsSync(tsConfigPath)) {
  console.log('   ✅ tsconfig.json found')
  
  try {
    const tsConfig = JSON.parse(fs.readFileSync(tsConfigPath, 'utf8'))
    if (tsConfig.compilerOptions && tsConfig.compilerOptions.strict) {
      console.log('   ✅ Strict mode enabled')
    } else {
      console.log('   ⚠️  Strict mode not enabled (recommended)')
    }
  } catch (error) {
    console.log('   ❌ Invalid tsconfig.json')
  }
} else {
  console.log('   ❌ tsconfig.json not found')
}

console.log()

// Summary
console.log('📋 Setup Summary:')
console.log('   To complete E2B integration setup:')
console.log('   1. Get E2B API key from https://e2b.dev')
console.log('   2. Add E2B_API_KEY to .env.local')
console.log('   3. Ensure all dependencies are installed: pnpm install')
console.log('   4. Start development server: pnpm dev')
console.log('   5. Test with: "Create a simple calculator app"')

console.log()
console.log('🚀 Ready to test AG3NT E2B integration!')

// Exit with appropriate code
const hasE2BKey = envVars.E2B_API_KEY || process.env.E2B_API_KEY
const hasOpenAIKey = envVars.OPENAI_API_KEY || process.env.OPENAI_API_KEY
const hasRequiredFiles = requiredFiles.every(file => 
  fs.existsSync(path.join(process.cwd(), file))
)

if (hasE2BKey && hasOpenAIKey && hasRequiredFiles) {
  console.log('✅ All checks passed! Ready for testing.')
  process.exit(0)
} else {
  console.log('❌ Some checks failed. Please fix the issues above.')
  process.exit(1)
}
