/**
 * AG3NT Platform - Task Progress Animation
 * 
 * Animated visualization of coding tasks in progress
 */

'use client'

import React from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  Code, 
  Database, 
  TestTube, 
  Rocket, 
  CheckCircle, 
  Clock, 
  Loader2,
  Zap,
  Users
} from 'lucide-react'
import { useCoding } from '@/hooks/use-coding'
import { motion, AnimatePresence } from 'framer-motion'

const taskTypeConfig = {
  frontend: { 
    icon: Code, 
    color: 'text-blue-500', 
    bgColor: 'bg-blue-50', 
    label: 'Frontend' 
  },
  backend: { 
    icon: Code, 
    color: 'text-orange-500', 
    bgColor: 'bg-orange-50', 
    label: 'Backend' 
  },
  database: { 
    icon: Database, 
    color: 'text-green-500', 
    bgColor: 'bg-green-50', 
    label: 'Database' 
  },
  testing: { 
    icon: TestTube, 
    color: 'text-purple-500', 
    bgColor: 'bg-purple-50', 
    label: 'Testing' 
  },
  deployment: { 
    icon: Rocket, 
    color: 'text-red-500', 
    bgColor: 'bg-red-50', 
    label: 'Deployment' 
  }
}

export function TaskProgressAnimation() {
  const coding = useCoding()

  if (!coding.isRunning && !coding.progress) {
    return null
  }

  const getTaskProgress = (task: any) => {
    if (task.status === 'completed') return 100
    if (task.status === 'in_progress') {
      // Simulate progress based on time elapsed
      if (task.startTime) {
        const elapsed = Date.now() - task.startTime
        const estimated = task.estimatedTime || 5000
        return Math.min(95, (elapsed / estimated) * 100)
      }
      return 50
    }
    return 0
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Users className="h-5 w-5 text-blue-500" />
            <CardTitle className="text-lg">Agent Task Progress</CardTitle>
          </div>
          {coding.progress && (
            <Badge variant="outline">
              {coding.progress.currentPhase}
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Overall Progress */}
        {coding.progress && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Overall Progress</span>
              <span className="font-medium">
                {coding.completionPercentage}%
              </span>
            </div>
            <Progress value={coding.completionPercentage} className="h-3" />
          </div>
        )}

        {/* Animated Task List */}
        <div className="space-y-3">
          <AnimatePresence>
            {coding.tasks.map((task, index) => {
              const config = taskTypeConfig[task.type] || taskTypeConfig.frontend
              const Icon = config.icon
              const progress = getTaskProgress(task)

              return (
                <motion.div
                  key={task.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ delay: index * 0.1 }}
                  className={`p-3 rounded-lg border ${config.bgColor} ${
                    task.status === 'in_progress' ? 'ring-2 ring-blue-200' : ''
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    {/* Task Icon */}
                    <div className={`p-2 rounded-full bg-white ${config.color}`}>
                      {task.status === 'in_progress' ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : task.status === 'completed' ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : (
                        <Clock className="h-4 w-4 text-gray-400" />
                      )}
                    </div>

                    {/* Task Details */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <h4 className="text-sm font-medium truncate">{task.title}</h4>
                        <Badge 
                          variant={task.status === 'completed' ? 'default' : 'secondary'}
                          className="text-xs"
                        >
                          {config.label}
                        </Badge>
                      </div>
                      
                      <p className="text-xs text-muted-foreground mb-2 truncate">
                        {task.description}
                      </p>

                      {/* Progress Bar */}
                      <div className="space-y-1">
                        <Progress value={progress} className="h-1.5" />
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <span>
                            {task.status === 'completed' ? 'Completed' : 
                             task.status === 'in_progress' ? 'In Progress' : 
                             'Pending'}
                          </span>
                          {task.status === 'in_progress' && (
                            <span>{Math.round(progress)}%</span>
                          )}
                        </div>
                      </div>

                      {/* Live Output */}
                      {task.status === 'in_progress' && (
                        <motion.div
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          className="mt-2 text-xs text-blue-600 bg-blue-50 p-2 rounded"
                        >
                          <div className="flex items-center space-x-1">
                            <Zap className="h-3 w-3 animate-pulse" />
                            <span>
                              {task.type === 'frontend' && 'Generating React components...'}
                              {task.type === 'backend' && 'Creating API endpoints...'}
                              {task.type === 'database' && 'Setting up database schema...'}
                              {task.type === 'testing' && 'Writing test cases...'}
                              {task.type === 'deployment' && 'Configuring deployment...'}
                            </span>
                          </div>
                        </motion.div>
                      )}

                      {/* Completion Output */}
                      {task.status === 'completed' && task.output && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          className="mt-2 text-xs text-green-600 bg-green-50 p-2 rounded"
                        >
                          <div className="flex items-center space-x-1 mb-1">
                            <CheckCircle className="h-3 w-3" />
                            <span className="font-medium">Task Completed</span>
                          </div>
                          <div className="space-y-0.5">
                            {task.output.files && (
                              <div>📄 {task.output.files.length} files generated</div>
                            )}
                            {task.output.components && (
                              <div>🧩 {task.output.components.length} components created</div>
                            )}
                            {task.output.endpoints && (
                              <div>🔗 {task.output.endpoints.length} API endpoints</div>
                            )}
                            {task.output.tables && (
                              <div>🗃️ {task.output.tables.length} database tables</div>
                            )}
                          </div>
                        </motion.div>
                      )}
                    </div>
                  </div>
                </motion.div>
              )
            })}
          </AnimatePresence>
        </div>

        {/* Active Agents Display */}
        {coding.progress && coding.progress.activeAgents.length > 0 && (
          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <Users className="h-4 w-4 text-blue-500" />
              <span className="text-sm font-medium text-blue-700">
                Active Agents ({coding.progress.activeAgents.length})
              </span>
            </div>
            <div className="flex flex-wrap gap-2">
              {coding.progress.activeAgents.map((agent) => (
                <motion.div
                  key={agent}
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="flex items-center space-x-1 bg-white px-2 py-1 rounded text-xs"
                >
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                  <span>{agent.replace('-agent', '').replace('-', ' ')}</span>
                </motion.div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default TaskProgressAnimation
