/**
 * AG3NT Framework - User Interaction Agent
 * 
 * Specialized agent for interfacing with users, collecting feedback,
 * and providing explanations and clarifications.
 * 
 * Features:
 * - Natural language interaction
 * - User feedback collection
 * - Explanation generation
 * - Clarification requests
 * - User preference learning
 * - Multi-modal communication
 */

import { BaseAgent, AgentState, AgentConfig } from "../core/base-agent"
import { aiService } from "../../ai-service"

export interface UserInteractionInput {
  task: InteractionTask
  user: UserProfile
  context: InteractionContext
  requirements: InteractionRequirements
}

export interface InteractionTask {
  taskId: string
  type: 'conversation' | 'feedback' | 'explanation' | 'clarification' | 'guidance' | 'support'
  title: string
  description: string
  priority: 'critical' | 'high' | 'medium' | 'low'
  scope: InteractionScope
  deadline?: string
}

export interface InteractionScope {
  topics: string[]
  agents: string[]
  systems: string[]
  modalities: string[]
  includeHistory: boolean
  includeContext: boolean
  includePreferences: boolean
}

export interface UserProfile {
  userId: string
  name: string
  role: string
  experience: ExperienceLevel
  preferences: UserPreferences
  history: InteractionHistory
  context: UserContext
}

export interface ExperienceLevel {
  overall: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  technical: 'low' | 'medium' | 'high'
  domain: 'novice' | 'familiar' | 'experienced' | 'expert'
  tools: ToolExperience[]
}

export interface ToolExperience {
  tool: string
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  usage: number
  lastUsed: string
}

export interface UserPreferences {
  communication: CommunicationPreferences
  interface: InterfacePreferences
  content: ContentPreferences
  assistance: AssistancePreferences
}

export interface CommunicationPreferences {
  style: 'formal' | 'casual' | 'technical' | 'friendly'
  verbosity: 'concise' | 'detailed' | 'comprehensive'
  language: string
  tone: 'professional' | 'conversational' | 'instructional'
  examples: boolean
}

export interface InterfacePreferences {
  modality: 'text' | 'voice' | 'visual' | 'multimodal'
  layout: 'simple' | 'detailed' | 'dashboard'
  notifications: NotificationPreferences
  accessibility: AccessibilityPreferences
}

export interface NotificationPreferences {
  enabled: boolean
  frequency: 'immediate' | 'batched' | 'scheduled'
  channels: string[]
  types: string[]
}

export interface AccessibilityPreferences {
  screenReader: boolean
  highContrast: boolean
  largeText: boolean
  keyboardNavigation: boolean
  voiceControl: boolean
}

export interface ContentPreferences {
  depth: 'overview' | 'detailed' | 'comprehensive'
  format: 'text' | 'visual' | 'interactive' | 'mixed'
  examples: boolean
  tutorials: boolean
  references: boolean
}

export interface AssistancePreferences {
  proactivity: 'reactive' | 'suggestive' | 'proactive'
  automation: 'manual' | 'semi_auto' | 'automatic'
  confirmation: 'always' | 'important' | 'never'
  explanation: 'minimal' | 'standard' | 'detailed'
}

export interface InteractionHistory {
  sessions: InteractionSession[]
  patterns: InteractionPattern[]
  feedback: FeedbackHistory[]
  preferences: PreferenceHistory[]
}

export interface InteractionSession {
  sessionId: string
  startTime: string
  endTime: string
  duration: number
  interactions: Interaction[]
  outcome: SessionOutcome
  satisfaction: number
}

export interface Interaction {
  id: string
  timestamp: string
  type: 'question' | 'answer' | 'request' | 'response' | 'feedback' | 'clarification'
  content: InteractionContent
  context: any
  metadata: InteractionMetadata
}

export interface InteractionContent {
  text?: string
  voice?: VoiceContent
  visual?: VisualContent
  structured?: StructuredContent
}

export interface VoiceContent {
  audio: string
  transcript: string
  language: string
  confidence: number
}

export interface VisualContent {
  images: ImageContent[]
  diagrams: DiagramContent[]
  charts: ChartContent[]
  videos: VideoContent[]
}

export interface ImageContent {
  url: string
  caption: string
  alt: string
  metadata: any
}

export interface DiagramContent {
  type: string
  data: any
  description: string
  interactive: boolean
}

export interface ChartContent {
  type: string
  data: any
  title: string
  description: string
}

export interface VideoContent {
  url: string
  title: string
  description: string
  duration: number
}

export interface StructuredContent {
  type: string
  data: any
  schema: any
  presentation: any
}

export interface InteractionMetadata {
  agent: string
  confidence: number
  processing: ProcessingMetadata
  quality: QualityMetadata
}

export interface ProcessingMetadata {
  duration: number
  tokens: number
  model: string
  temperature: number
}

export interface QualityMetadata {
  relevance: number
  accuracy: number
  completeness: number
  clarity: number
}

export interface SessionOutcome {
  status: 'completed' | 'abandoned' | 'escalated' | 'deferred'
  goals: GoalOutcome[]
  issues: IssueOutcome[]
  satisfaction: number
  feedback: string
}

export interface GoalOutcome {
  goal: string
  achieved: boolean
  progress: number
  obstacles: string[]
}

export interface IssueOutcome {
  issue: string
  resolved: boolean
  resolution: string
  escalated: boolean
}

export interface InteractionPattern {
  type: string
  frequency: number
  context: string[]
  triggers: string[]
  outcomes: string[]
}

export interface FeedbackHistory {
  timestamp: string
  type: 'rating' | 'comment' | 'suggestion' | 'complaint'
  content: string
  rating?: number
  category: string
  resolved: boolean
}

export interface PreferenceHistory {
  timestamp: string
  preference: string
  oldValue: any
  newValue: any
  reason: string
  source: 'explicit' | 'inferred' | 'learned'
}

export interface UserContext {
  current: CurrentContext
  environment: EnvironmentContext
  task: TaskContext
  social: SocialContext
}

export interface CurrentContext {
  location: string
  time: string
  device: string
  application: string
  activity: string
}

export interface EnvironmentContext {
  workspace: string
  project: string
  team: string[]
  tools: string[]
  constraints: string[]
}

export interface TaskContext {
  currentTask: string
  goals: string[]
  progress: number
  blockers: string[]
  deadline?: string
}

export interface SocialContext {
  collaborators: string[]
  stakeholders: string[]
  communication: string[]
  permissions: string[]
}

export interface InteractionContext {
  session: SessionContext
  conversation: ConversationContext
  system: SystemContext
  domain: DomainContext
}

export interface SessionContext {
  sessionId: string
  startTime: string
  duration: number
  interactions: number
  mode: 'guided' | 'exploratory' | 'task_focused'
}

export interface ConversationContext {
  topic: string
  intent: string
  entities: Entity[]
  sentiment: SentimentAnalysis
  history: ConversationTurn[]
}

export interface Entity {
  type: string
  value: string
  confidence: number
  context: string
}

export interface SentimentAnalysis {
  polarity: 'positive' | 'negative' | 'neutral'
  confidence: number
  emotions: Emotion[]
}

export interface Emotion {
  type: string
  intensity: number
  confidence: number
}

export interface ConversationTurn {
  speaker: 'user' | 'agent'
  content: string
  intent: string
  entities: Entity[]
  timestamp: string
}

export interface SystemContext {
  agents: AgentContext[]
  services: ServiceContext[]
  resources: ResourceContext
  status: SystemStatus
}

export interface AgentContext {
  agentId: string
  status: string
  capabilities: string[]
  load: number
  availability: boolean
}

export interface ServiceContext {
  name: string
  status: string
  performance: number
  errors: number
}

export interface ResourceContext {
  cpu: number
  memory: number
  storage: number
  network: number
}

export interface SystemStatus {
  health: 'healthy' | 'degraded' | 'unhealthy'
  performance: number
  availability: number
  issues: string[]
}

export interface DomainContext {
  domain: string
  concepts: DomainConcept[]
  relationships: DomainRelationship[]
  rules: DomainRule[]
}

export interface DomainConcept {
  name: string
  definition: string
  examples: string[]
  related: string[]
}

export interface DomainRelationship {
  from: string
  to: string
  type: string
  strength: number
}

export interface DomainRule {
  name: string
  condition: string
  action: string
  priority: number
}

export interface InteractionRequirements {
  communication: CommunicationRequirements
  personalization: PersonalizationRequirements
  accessibility: AccessibilityRequirements
  quality: QualityRequirements
}

export interface CommunicationRequirements {
  modalities: string[]
  languages: string[]
  realTime: boolean
  multiTurn: boolean
  contextAware: boolean
}

export interface PersonalizationRequirements {
  adaptive: boolean
  learning: boolean
  preferences: boolean
  history: boolean
  prediction: boolean
}

export interface AccessibilityRequirements {
  wcag: string
  screenReader: boolean
  keyboard: boolean
  voice: boolean
  visual: boolean
}

export interface QualityRequirements {
  accuracy: number
  relevance: number
  completeness: number
  timeliness: number
  satisfaction: number
}

export interface UserInteractionResult {
  taskId: string
  status: 'completed' | 'failed' | 'partial'
  interactions: ProcessedInteraction[]
  insights: UserInsight[]
  recommendations: UserRecommendation[]
  improvements: ImprovementSuggestion[]
  metrics: InteractionMetrics
}

export interface ProcessedInteraction {
  id: string
  type: string
  content: any
  response: InteractionResponse
  quality: InteractionQuality
  learning: LearningOutcome[]
}

export interface InteractionResponse {
  content: any
  confidence: number
  alternatives: Alternative[]
  explanation: string
  followUp: FollowUpSuggestion[]
}

export interface Alternative {
  content: any
  confidence: number
  rationale: string
}

export interface FollowUpSuggestion {
  type: string
  content: string
  priority: number
}

export interface InteractionQuality {
  relevance: number
  accuracy: number
  completeness: number
  clarity: number
  satisfaction: number
}

export interface LearningOutcome {
  type: string
  insight: string
  confidence: number
  application: string
}

export interface UserInsight {
  type: 'preference' | 'behavior' | 'need' | 'goal' | 'challenge'
  description: string
  evidence: string[]
  confidence: number
  implications: string[]
}

export interface UserRecommendation {
  type: 'feature' | 'workflow' | 'training' | 'tool' | 'process'
  description: string
  rationale: string
  benefit: string
  effort: 'low' | 'medium' | 'high'
  priority: 'high' | 'medium' | 'low'
}

export interface ImprovementSuggestion {
  area: string
  current: string
  suggested: string
  impact: string
  implementation: string
}

export interface InteractionMetrics {
  satisfaction: number
  efficiency: number
  effectiveness: number
  engagement: number
  learning: number
  retention: number
}

/**
 * User Interaction Agent - Natural language user interface
 */
export class UserInteractionAgent extends BaseAgent {
  private readonly interactionSteps = [
    'analyze_user', 'understand_intent', 'generate_response',
    'personalize_content', 'collect_feedback', 'learn_preferences',
    'provide_explanations', 'suggest_improvements', 'update_profile'
  ]

  constructor(config: Partial<AgentConfig> = {}) {
    super('user-interaction', {
      capabilities: {
        requiredCapabilities: [
          'natural_language_processing',
          'user_modeling',
          'personalization',
          'explanation_generation',
          'feedback_collection',
          'preference_learning',
          'multimodal_communication'
        ],
        contextFilters: ['user', 'interaction', 'communication', 'feedback', 'preferences'],
        mcpEnhanced: true,
        sequentialThinking: true,
        contextEnrichment: true,
        ragIntegration: true
      },
      ...config
    })
  }

  /**
   * Execute user interaction workflow
   */
  protected async executeWorkflow(state: AgentState): Promise<AgentState> {
    const input = state.input as UserInteractionInput
    
    console.log(`👤 Starting user interaction: ${input.task.title}`)

    // Execute interaction steps sequentially
    for (const stepId of this.interactionSteps) {
      console.log(`🔄 Executing step: ${stepId}`)
      
      const stepResult = await this.executeStepWithContext(stepId, input)
      
      if (stepResult.needsInput) {
        state.needsInput = true
        state.question = stepResult.question
        break
      }

      // Update progress
      state.metadata.completedSteps++
      state.metadata.lastUpdated = new Date().toISOString()
      
      this.emit('step_completed', {
        agentId: this.agentId,
        stepId,
        progress: state.metadata.completedSteps / state.metadata.totalSteps
      })
    }

    // Mark as completed if no input needed
    if (!state.needsInput) {
      state.completed = true
      console.log(`✅ User interaction completed: ${input.task.title}`)
    }

    return state
  }

  /**
   * Execute individual interaction step with context enhancement
   */
  private async executeStepWithContext(stepId: string, input?: any): Promise<any> {
    // Get enhanced context from context engine
    const enhancedState = await this.getEnhancedContext(stepId)

    // Execute step with MCP enhancement
    switch (stepId) {
      case 'analyze_user':
        return await this.analyzeUserWithMCP(enhancedState, input)
      case 'understand_intent':
        return await this.understandIntentWithMCP(enhancedState)
      case 'generate_response':
        return await this.generateResponseWithMCP(enhancedState)
      case 'personalize_content':
        return await this.personalizeContentWithMCP(enhancedState)
      case 'collect_feedback':
        return await this.collectFeedbackWithMCP(enhancedState)
      case 'learn_preferences':
        return await this.learnPreferencesWithMCP(enhancedState)
      case 'provide_explanations':
        return await this.provideExplanationsWithMCP(enhancedState)
      case 'suggest_improvements':
        return await this.suggestImprovementsWithMCP(enhancedState)
      case 'update_profile':
        return await this.updateProfileWithMCP(enhancedState)
      default:
        throw new Error(`Unknown interaction step: ${stepId}`)
    }
  }

  /**
   * Get total steps for progress tracking
   */
  protected getTotalSteps(): number {
    return this.interactionSteps.length
  }

  /**
   * Get relevant documentation for user interaction
   */
  protected async getRelevantDocumentation(): Promise<Record<string, any>> {
    return {
      userInteraction: 'User interaction design and natural language processing',
      personalization: 'User personalization and adaptive interfaces',
      feedback: 'Feedback collection and analysis techniques',
      accessibility: 'Accessibility guidelines and inclusive design',
      communication: 'Effective communication and explanation strategies',
      learning: 'User preference learning and behavior modeling'
    }
  }

  // ============================================================================
  // STEP IMPLEMENTATIONS WITH MCP ENHANCEMENT
  // ============================================================================

  private async analyzeUserWithMCP(state: any, input: UserInteractionInput): Promise<any> {
    const userAnalysis = await aiService.analyzeUserProfile(
      input.user,
      input.context,
      input.task.scope
    )

    this.state!.results.userAnalysis = userAnalysis
    
    return {
      results: userAnalysis,
      needsInput: false,
      completed: false
    }
  }

  private async understandIntentWithMCP(state: any): Promise<any> {
    const userAnalysis = this.state!.results.userAnalysis
    
    const intentUnderstanding = await aiService.understandUserIntent(
      userAnalysis,
      this.state!.input.context
    )

    this.state!.results.intentUnderstanding = intentUnderstanding
    
    return {
      results: intentUnderstanding,
      needsInput: false,
      completed: false
    }
  }

  private async generateResponseWithMCP(state: any): Promise<any> {
    const intentUnderstanding = this.state!.results.intentUnderstanding
    
    const response = await aiService.generateUserResponse(
      intentUnderstanding,
      this.state!.input.user.preferences
    )

    this.state!.results.response = response
    
    return {
      results: response,
      needsInput: false,
      completed: false
    }
  }

  private async personalizeContentWithMCP(state: any): Promise<any> {
    const response = this.state!.results.response
    
    const personalization = await aiService.personalizeUserContent(
      response,
      this.state!.input.user,
      this.state!.input.requirements.personalization
    )

    this.state!.results.personalization = personalization
    
    return {
      results: personalization,
      needsInput: false,
      completed: false
    }
  }

  private async collectFeedbackWithMCP(state: any): Promise<any> {
    const personalization = this.state!.results.personalization
    
    const feedback = await aiService.collectUserFeedback(
      personalization,
      this.state!.input.user.preferences
    )

    this.state!.results.feedback = feedback
    
    return {
      results: feedback,
      needsInput: false,
      completed: false
    }
  }

  private async learnPreferencesWithMCP(state: any): Promise<any> {
    const feedback = this.state!.results.feedback
    
    const learning = await aiService.learnUserPreferences(
      feedback,
      this.state!.input.user.history
    )

    this.state!.results.learning = learning
    
    return {
      results: learning,
      needsInput: false,
      completed: false
    }
  }

  private async provideExplanationsWithMCP(state: any): Promise<any> {
    const learning = this.state!.results.learning
    
    const explanations = await aiService.provideUserExplanations(
      learning,
      this.state!.input.requirements.quality
    )

    this.state!.results.explanations = explanations
    
    return {
      results: explanations,
      needsInput: false,
      completed: false
    }
  }

  private async suggestImprovementsWithMCP(state: any): Promise<any> {
    const explanations = this.state!.results.explanations
    
    const improvements = await aiService.suggestUserImprovements(
      explanations,
      this.state!.input.user
    )

    this.state!.results.improvements = improvements
    
    return {
      results: improvements,
      needsInput: false,
      completed: false
    }
  }

  private async updateProfileWithMCP(state: any): Promise<any> {
    const improvements = this.state!.results.improvements
    
    const profileUpdate = await aiService.updateUserProfile(
      improvements,
      this.state!.input.task
    )

    this.state!.results.profileUpdate = profileUpdate
    
    return {
      results: profileUpdate,
      needsInput: false,
      completed: true // Final step
    }
  }
}

// Export for easy access
export { UserInteractionAgent as default }
