<!DOCTYPE html>
<html>
<head>
    <title>Test Framework Fix</title>
</head>
<body>
    <h1>Testing Framework Fixes</h1>
    <div id="results"></div>
    
    <script>
        async function testFramework() {
            const results = document.getElementById('results');
            
            try {
                // Test framework status
                console.log('Testing framework status...');
                const statusResponse = await fetch('/api/framework?action=status');
                const statusData = await statusResponse.json();
                
                results.innerHTML += `<p>✅ Status: ${JSON.stringify(statusData)}</p>`;
                
                // Test framework analytics
                console.log('Testing framework analytics...');
                const analyticsResponse = await fetch('/api/framework?action=analytics');
                const analyticsData = await analyticsResponse.json();
                
                results.innerHTML += `<p>✅ Analytics: Success=${analyticsData.success}</p>`;
                
                // Test framework agents
                console.log('Testing framework agents...');
                const agentsResponse = await fetch('/api/framework?action=agents');
                const agentsData = await agentsResponse.json();
                
                results.innerHTML += `<p>✅ Agents: ${agentsData.data?.length || 0} available</p>`;
                
                // Test project planning
                console.log('Testing project planning...');
                const planResponse = await fetch('/api/framework', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'plan_project',
                        projectName: 'Calculator App',
                        projectDescription: 'calculator app, but glowing neon cyberpunk style',
                        projectType: 'web-application',
                        features: ['calculator', 'ui_effects']
                    })
                });
                
                const planData = await planResponse.json();
                
                if (planData.success) {
                    results.innerHTML += `<p>✅ Planning: Success! Execution time: ${planData.executionTime}ms</p>`;
                    results.innerHTML += `<p>📊 Project: ${planData.data?.projectName}</p>`;
                    results.innerHTML += `<p>🤖 Agents used: ${planData.agentsUsed?.length || 0}</p>`;
                } else {
                    results.innerHTML += `<p>❌ Planning failed: ${planData.error}</p>`;
                }
                
                results.innerHTML += `<h2>🎉 All tests completed!</h2>`;
                
            } catch (error) {
                results.innerHTML += `<p>❌ Test failed: ${error.message}</p>`;
            }
        }
        
        // Run tests when page loads
        window.onload = testFramework;
    </script>
</body>
</html>
