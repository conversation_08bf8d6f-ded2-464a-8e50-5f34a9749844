/**
 * AG3NT Framework - Core API Definitions
 * 
 * Comprehensive framework interfaces and abstractions that define the complete
 * AG3NT Framework API surface. This provides a unified, type-safe interface
 * for all framework operations and makes it easy to extend and integrate.
 * 
 * Features:
 * - Unified API interfaces for all framework components
 * - Type-safe operations with comprehensive error handling
 * - Plugin and extension system interfaces
 * - Configuration and lifecycle management
 * - Monitoring and observability APIs
 * - Integration and interoperability interfaces
 */

import { EventEmitter } from "events"
import { AgentState, AgentCapabilities, AgentConfig } from "./base-agent"
import { RegisteredAgent, AgentQuery } from "./agent-registry"
import { AgentMessage, MessageType, CommunicationChannel } from "./agent-communication"
import { WorkflowDefinition, WorkflowExecution } from "./workflow-coordinator"

// ============================================================================
// CORE FRAMEWORK INTERFACES
// ============================================================================

/**
 * Main Framework API - Unified interface for all framework operations
 */
export interface IFrameworkAPI {
  // Lifecycle Management
  initialize(config?: FrameworkInitConfig): Promise<void>
  shutdown(): Promise<void>
  isInitialized(): boolean
  getVersion(): string

  // Agent Management
  agents: IAgentAPI
  
  // Communication
  communication: ICommunicationAPI
  
  // Workflows
  workflows: IWorkflowAPI
  
  // Context & State
  context: IContextAPI
  
  // Monitoring & Observability
  monitoring: IMonitoringAPI
  
  // Configuration
  config: IConfigurationAPI
  
  // Extensions & Plugins
  extensions: IExtensionAPI
}

/**
 * Agent Management API
 */
export interface IAgentAPI {
  // Registration & Discovery
  register(agent: IAgent, metadata?: AgentRegistrationOptions): Promise<string>
  unregister(agentId: string): Promise<void>
  find(query: AgentQuery): Promise<RegisteredAgent[]>
  get(agentId: string): Promise<RegisteredAgent | null>
  list(): Promise<RegisteredAgent[]>
  
  // Execution
  execute(agentType: string, input: any, options?: ExecutionOptions): Promise<ExecutionResult>
  executeById(agentId: string, input: any, options?: ExecutionOptions): Promise<ExecutionResult>
  
  // Lifecycle
  start(agentId: string): Promise<void>
  stop(agentId: string): Promise<void>
  restart(agentId: string): Promise<void>
  
  // Health & Status
  getHealth(agentId: string): Promise<AgentHealthStatus>
  getStatus(agentId: string): Promise<AgentStatus>
  getMetrics(agentId: string): Promise<AgentMetrics>
}

/**
 * Communication API
 */
export interface ICommunicationAPI {
  // Messaging
  sendMessage(from: string, to: string, type: MessageType, payload: any, options?: MessageOptions): Promise<string>
  broadcast(from: string, type: MessageType, payload: any, options?: BroadcastOptions): Promise<string>
  
  // Channels
  createChannel(type: string, options?: ChannelOptions): Promise<string>
  joinChannel(channelId: string, agentId: string): Promise<void>
  leaveChannel(channelId: string, agentId: string): Promise<void>
  getChannel(channelId: string): Promise<CommunicationChannel | null>
  listChannels(): Promise<CommunicationChannel[]>
  
  // Presence & Discovery
  getPresence(agentId?: string): Promise<AgentPresence | AgentPresence[]>
  updatePresence(agentId: string, updates: Partial<AgentPresence>): Promise<void>
  
  // Message Handling
  onMessage(type: MessageType, handler: MessageHandler): void
  offMessage(type: MessageType, handler: MessageHandler): void
  
  // Statistics
  getStats(): Promise<CommunicationStats>
}

/**
 * Workflow API
 */
export interface IWorkflowAPI {
  // Workflow Management
  register(workflow: WorkflowDefinition): Promise<void>
  unregister(workflowId: string): Promise<void>
  get(workflowId: string): Promise<WorkflowDefinition | null>
  list(): Promise<WorkflowDefinition[]>
  
  // Execution
  execute(workflowId: string, input: any, options?: WorkflowExecutionOptions): Promise<string>
  cancel(executionId: string): Promise<void>
  pause(executionId: string): Promise<void>
  resume(executionId: string): Promise<void>
  
  // Monitoring
  getExecution(executionId: string): Promise<WorkflowExecution | null>
  listExecutions(filter?: ExecutionFilter): Promise<WorkflowExecution[]>
  getExecutionLogs(executionId: string): Promise<ExecutionLog[]>
  
  // Statistics
  getStats(): Promise<WorkflowStats>
}

/**
 * Context API
 */
export interface IContextAPI {
  // Context Management
  get(key: string): Promise<any>
  set(key: string, value: any, options?: ContextOptions): Promise<void>
  delete(key: string): Promise<void>
  exists(key: string): Promise<boolean>
  
  // Shared State
  getSharedState(scope: string): Promise<any>
  setSharedState(scope: string, state: any): Promise<void>
  
  // Context Enhancement
  enhance(context: any, options?: EnhancementOptions): Promise<EnhancedContext>
  enrich(data: any, enrichmentType: string): Promise<any>
  
  // Memory & Persistence
  remember(key: string, value: any, ttl?: number): Promise<void>
  recall(key: string): Promise<any>
  forget(key: string): Promise<void>
}

/**
 * Monitoring API
 */
export interface IMonitoringAPI {
  // Health Checks
  getHealth(): Promise<FrameworkHealth>
  getComponentHealth(component: string): Promise<ComponentHealth>
  
  // Metrics
  getMetrics(): Promise<FrameworkMetrics>
  getCustomMetrics(namespace: string): Promise<Record<string, any>>
  recordMetric(name: string, value: number, tags?: Record<string, string>): Promise<void>
  
  // Events & Logs
  getEvents(filter?: EventFilter): Promise<FrameworkEvent[]>
  getLogs(filter?: LogFilter): Promise<LogEntry[]>
  
  // Alerts
  createAlert(alert: AlertDefinition): Promise<string>
  deleteAlert(alertId: string): Promise<void>
  getAlerts(): Promise<AlertDefinition[]>
  
  // Performance
  getPerformanceStats(): Promise<PerformanceStats>
  startProfiling(options?: ProfilingOptions): Promise<string>
  stopProfiling(sessionId: string): Promise<ProfilingResult>
}

/**
 * Configuration API
 */
export interface IConfigurationAPI {
  // Configuration Management
  get(key: string): any
  set(key: string, value: any): void
  has(key: string): boolean
  delete(key: string): void
  
  // Environment
  getEnvironment(): string
  isProduction(): boolean
  isDevelopment(): boolean
  
  // Feature Flags
  isFeatureEnabled(feature: string): boolean
  enableFeature(feature: string): void
  disableFeature(feature: string): void
  
  // Validation
  validate(): ValidationResult
  validateComponent(component: string): ValidationResult
}

/**
 * Extension API
 */
export interface IExtensionAPI {
  // Plugin Management
  register(plugin: IPlugin): Promise<void>
  unregister(pluginId: string): Promise<void>
  get(pluginId: string): Promise<IPlugin | null>
  list(): Promise<IPlugin[]>
  
  // Hooks & Middleware
  addHook(event: string, handler: HookHandler): void
  removeHook(event: string, handler: HookHandler): void
  addMiddleware(middleware: IMiddleware): void
  removeMiddleware(middlewareId: string): void
  
  // Custom Providers
  registerProvider(type: string, provider: IProvider): void
  getProvider(type: string): IProvider | null
  
  // Integration
  integrate(integration: IIntegration): Promise<void>
  getIntegrations(): Promise<IIntegration[]>
}

// ============================================================================
// CORE INTERFACES & TYPES
// ============================================================================

/**
 * Base Agent Interface - Contract for all agents
 */
export interface IAgent {
  readonly id: string
  readonly type: string
  readonly capabilities: AgentCapabilities
  readonly config: AgentConfig
  readonly initialized: boolean
  readonly currentState: AgentState | null
  
  // Lifecycle
  initialize(contextEngine?: any): Promise<void>
  shutdown(): Promise<void>
  
  // Execution
  execute(input: any, options?: ExecutionOptions): Promise<AgentState>
  
  // Health & Status
  getHealth(): Promise<AgentHealthStatus>
  getMetrics(): Promise<AgentMetrics>
  
  // Events
  on(event: string, listener: (...args: any[]) => void): this
  off(event: string, listener: (...args: any[]) => void): this
  emit(event: string, ...args: any[]): boolean
}

/**
 * Plugin Interface - Contract for framework extensions
 */
export interface IPlugin {
  readonly id: string
  readonly name: string
  readonly version: string
  readonly description: string
  readonly dependencies: string[]
  
  // Lifecycle
  initialize(framework: IFrameworkAPI): Promise<void>
  shutdown(): Promise<void>
  
  // Configuration
  configure(config: any): void
  getConfig(): any
  
  // Health
  getHealth(): Promise<PluginHealth>
}

/**
 * Middleware Interface - Request/response processing
 */
export interface IMiddleware {
  readonly id: string
  readonly priority: number
  
  process(context: MiddlewareContext, next: () => Promise<any>): Promise<any>
}

/**
 * Provider Interface - Custom service providers
 */
export interface IProvider {
  readonly type: string
  readonly name: string
  
  initialize(config: any): Promise<void>
  provide(request: ProviderRequest): Promise<ProviderResponse>
  shutdown(): Promise<void>
}

/**
 * Integration Interface - External system integrations
 */
export interface IIntegration {
  readonly id: string
  readonly name: string
  readonly type: string
  
  connect(config: any): Promise<void>
  disconnect(): Promise<void>
  isConnected(): boolean
  
  sync(data: any): Promise<any>
  getStatus(): Promise<IntegrationStatus>
}

// ============================================================================
// CONFIGURATION & OPTIONS
// ============================================================================

export interface FrameworkInitConfig {
  agents?: {
    autoRegisterBuiltins?: boolean
    maxConcurrentSessions?: number
    defaultTimeout?: number
  }
  communication?: {
    enableRealTime?: boolean
    messageRetention?: number
    heartbeatInterval?: number
  }
  workflows?: {
    enablePersistence?: boolean
    maxConcurrentExecutions?: number
    defaultTimeout?: number
  }
  context?: {
    enableMCP?: boolean
    enableRAG?: boolean
    enableEnrichment?: boolean
  }
  monitoring?: {
    enableMetrics?: boolean
    enableTracing?: boolean
    enableProfiling?: boolean
  }
  extensions?: {
    autoLoadPlugins?: boolean
    pluginDirectory?: string
  }
}

export interface AgentRegistrationOptions {
  description?: string
  tags?: string[]
  priority?: number
  maxConcurrentSessions?: number
  autoStart?: boolean
}

export interface ExecutionOptions {
  sessionId?: string
  priority?: 'low' | 'medium' | 'high' | 'critical'
  timeout?: number
  tags?: string[]
  context?: Record<string, any>
}

export interface MessageOptions {
  priority?: 'low' | 'normal' | 'high' | 'critical'
  sessionId?: string
  correlationId?: string
  requiresAck?: boolean
  ttl?: number
  encrypted?: boolean
}

export interface BroadcastOptions extends MessageOptions {
  channelId?: string
  excludeAgents?: string[]
}

export interface ChannelOptions {
  channelId?: string
  purpose?: string
  participants?: string[]
  persistent?: boolean
  encrypted?: boolean
  maxParticipants?: number
}

export interface WorkflowExecutionOptions {
  sessionId?: string
  priority?: 'low' | 'medium' | 'high' | 'critical'
  tags?: string[]
  context?: Record<string, any>
  parentExecution?: string
}

export interface ContextOptions {
  ttl?: number
  scope?: 'global' | 'session' | 'agent'
  persistent?: boolean
  encrypted?: boolean
}

export interface EnhancementOptions {
  enableMCP?: boolean
  enableRAG?: boolean
  enableEnrichment?: boolean
  maxEnrichments?: number
}

// ============================================================================
// RESULT & STATUS TYPES
// ============================================================================

export interface ExecutionResult {
  sessionId: string
  agentId: string
  agentType: string
  success: boolean
  result?: AgentState
  error?: string
  duration: number
  metadata: {
    startTime: string
    endTime: string
    stepsCompleted: number
    totalSteps: number
  }
}

export interface AgentHealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy'
  lastCheck: string
  responseTime: number
  errorCount: number
  uptime: number
  details?: Record<string, any>
}

export interface AgentMetrics {
  totalExecutions: number
  successRate: number
  averageResponseTime: number
  currentLoad: number
  memoryUsage: number
  cpuUsage: number
  customMetrics?: Record<string, number>
}

export interface AgentStatus {
  state: 'idle' | 'busy' | 'error' | 'offline'
  currentSession?: string
  lastActivity: string
  activeSessions: number
  totalExecutions: number
  successRate: number
}

export interface AgentPresence {
  agentId: string
  agentType: string
  status: 'online' | 'busy' | 'idle' | 'offline' | 'error'
  capabilities: string[]
  currentSessions: string[]
  lastSeen: string
  metadata: {
    version: string
    location: string
    load: number
    availability: number
  }
}

export interface CommunicationStats {
  agents: number
  channels: number
  pendingMessages: number
  totalMessages: number
  activeChannels: number
  messageRate: number
}

export interface WorkflowStats {
  workflows: number
  activeExecutions: number
  completedExecutions: number
  failedExecutions: number
  activeSteps: number
  averageExecutionTime: number
}

export interface FrameworkHealth {
  status: 'healthy' | 'degraded' | 'unhealthy'
  components: Record<string, ComponentHealth>
  uptime: number
  version: string
  timestamp: string
}

export interface ComponentHealth {
  status: 'healthy' | 'degraded' | 'unhealthy'
  lastCheck: string
  responseTime: number
  errorCount: number
  details?: Record<string, any>
}

export interface FrameworkMetrics {
  agents: {
    total: number
    active: number
    healthy: number
  }
  communication: {
    messagesPerSecond: number
    activeChannels: number
    averageLatency: number
  }
  workflows: {
    activeExecutions: number
    completionRate: number
    averageExecutionTime: number
  }
  system: {
    memoryUsage: number
    cpuUsage: number
    uptime: number
  }
}

export interface ValidationResult {
  valid: boolean
  errors: string[]
  warnings: string[]
  details?: Record<string, any>
}

export interface PluginHealth {
  status: 'healthy' | 'degraded' | 'unhealthy'
  lastCheck: string
  errorCount: number
  details?: Record<string, any>
}

// ============================================================================
// EVENT & LOGGING TYPES
// ============================================================================

export interface FrameworkEvent {
  id: string
  type: string
  source: string
  timestamp: string
  data: any
  severity: 'info' | 'warning' | 'error' | 'critical'
}

export interface LogEntry {
  timestamp: string
  level: 'debug' | 'info' | 'warn' | 'error'
  message: string
  source: string
  data?: any
}

export interface AlertDefinition {
  id: string
  name: string
  condition: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  enabled: boolean
  actions: AlertAction[]
}

export interface AlertAction {
  type: 'email' | 'webhook' | 'log'
  config: Record<string, any>
}

export interface PerformanceStats {
  responseTime: {
    p50: number
    p95: number
    p99: number
  }
  throughput: {
    requestsPerSecond: number
    messagesPerSecond: number
  }
  resources: {
    memoryUsage: number
    cpuUsage: number
    diskUsage: number
  }
}

export interface ProfilingOptions {
  duration?: number
  sampleRate?: number
  includeMemory?: boolean
  includeCPU?: boolean
}

export interface ProfilingResult {
  sessionId: string
  duration: number
  samples: number
  memoryProfile?: any
  cpuProfile?: any
  summary: Record<string, any>
}

// ============================================================================
// FILTER & QUERY TYPES
// ============================================================================

export interface ExecutionFilter {
  status?: string[]
  agentType?: string
  startTime?: string
  endTime?: string
  tags?: string[]
}

export interface EventFilter {
  type?: string[]
  source?: string[]
  severity?: string[]
  startTime?: string
  endTime?: string
}

export interface LogFilter {
  level?: string[]
  source?: string[]
  startTime?: string
  endTime?: string
  message?: string
}

// ============================================================================
// HANDLER TYPES
// ============================================================================

export type MessageHandler = (message: AgentMessage) => Promise<AgentMessage | void>
export type HookHandler = (...args: any[]) => Promise<any>

export interface MiddlewareContext {
  request: any
  response?: any
  agent?: IAgent
  session?: string
  metadata: Record<string, any>
}

export interface ProviderRequest {
  type: string
  data: any
  options?: Record<string, any>
}

export interface ProviderResponse {
  success: boolean
  data?: any
  error?: string
}

export interface IntegrationStatus {
  connected: boolean
  lastSync?: string
  errorCount: number
  health: 'healthy' | 'degraded' | 'unhealthy'
}

export interface ExecutionLog {
  timestamp: string
  level: 'debug' | 'info' | 'warn' | 'error'
  message: string
  stepId?: string
  agentId?: string
  data?: any
}

export interface EnhancedContext {
  context: any
  enrichments: any[]
  confidence: number
  recommendations: string[]
  metadata: Record<string, any>
}
