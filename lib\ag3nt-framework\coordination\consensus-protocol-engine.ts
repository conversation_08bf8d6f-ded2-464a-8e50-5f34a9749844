/**
 * AG3NT Framework - Consensus Protocol Engine
 * 
 * Democratic decision-making mechanisms with voting, quorum, and conflict resolution
 * for sophisticated multi-agent coordination.
 */

import { EventEmitter } from "events"

export interface ConsensusConfig {
  defaultProtocol: 'majority' | 'supermajority' | 'unanimous' | 'weighted' | 'byzantine'
  quorumThreshold: number // 0-1
  votingTimeout: number
  enableDelegatedVoting: boolean
  enableVetoRights: boolean
  conflictResolutionStrategy: 'escalation' | 'mediation' | 'fallback' | 'random'
}

export interface ConsensusProposal {
  proposalId: string
  proposerId: string
  title: string
  description: string
  type: 'decision' | 'resource_allocation' | 'task_assignment' | 'policy_change' | 'emergency'
  options: ProposalOption[]
  votingRules: VotingRules
  context: ProposalContext
  status: 'draft' | 'active' | 'completed' | 'failed' | 'cancelled'
  createdAt: number
  votingDeadline: number
  result?: ConsensusResult
}

export interface ProposalOption {
  optionId: string
  title: string
  description: string
  impact: OptionImpact
  cost: number
  risk: 'low' | 'medium' | 'high'
  feasibility: number // 0-1
  supportingData: any
}

export interface OptionImpact {
  scope: string[]
  magnitude: 'minor' | 'moderate' | 'major' | 'critical'
  reversibility: boolean
  timeframe: string
  dependencies: string[]
}

export interface VotingRules {
  protocol: 'majority' | 'supermajority' | 'unanimous' | 'weighted' | 'byzantine'
  quorum: number
  allowAbstention: boolean
  allowDelegation: boolean
  weightingCriteria?: WeightingCriteria
  vetoRights: VetoRight[]
  tieBreakingMethod: 'random' | 'proposer_decides' | 'escalate' | 'status_quo'
}

export interface WeightingCriteria {
  authorityWeight: number
  expertiseWeight: number
  stakeWeight: number
  trustWeight: number
  experienceWeight: number
}

export interface VetoRight {
  agentId: string
  scope: string[]
  justificationRequired: boolean
  appealable: boolean
}

export interface ProposalContext {
  urgency: 'low' | 'medium' | 'high' | 'critical'
  stakeholders: string[]
  affectedSystems: string[]
  prerequisites: string[]
  constraints: any
  relatedProposals: string[]
}

export interface AgentVote {
  voteId: string
  agentId: string
  proposalId: string
  optionId: string
  voteType: 'support' | 'oppose' | 'abstain' | 'veto'
  weight: number
  confidence: number // 0-1
  reasoning: string
  conditions?: VoteCondition[]
  timestamp: number
  delegatedFrom?: string
}

export interface VoteCondition {
  type: 'conditional' | 'contingent' | 'time_limited'
  condition: string
  expiresAt?: number
}

export interface ConsensusResult {
  decision: string
  winningOption: string
  votingSummary: VotingSummary
  consensusLevel: number // 0-1
  dissent: DissentAnalysis
  implementation: ImplementationPlan
  appeals: Appeal[]
}

export interface VotingSummary {
  totalEligibleVoters: number
  totalVotes: number
  participationRate: number
  optionResults: OptionResult[]
  vetoesReceived: number
  delegatedVotes: number
}

export interface OptionResult {
  optionId: string
  votes: number
  weightedVotes: number
  percentage: number
  support: AgentVote[]
}

export interface DissentAnalysis {
  dissentLevel: number // 0-1
  majorConcerns: string[]
  dissentingAgents: string[]
  compromisePossible: boolean
  recommendedActions: string[]
}

export interface ImplementationPlan {
  phases: ImplementationPhase[]
  timeline: string
  responsibilities: AgentResponsibility[]
  successMetrics: SuccessMetric[]
  rollbackPlan: RollbackPlan
}

export interface ImplementationPhase {
  phaseId: string
  name: string
  description: string
  startConditions: string[]
  deliverables: string[]
  duration: number
  dependencies: string[]
}

export interface AgentResponsibility {
  agentId: string
  role: string
  tasks: string[]
  authority: string[]
  accountability: string[]
}

export interface SuccessMetric {
  name: string
  target: number
  measurement: string
  timeline: string
}

export interface RollbackPlan {
  triggers: string[]
  steps: string[]
  timeframe: number
  approvalRequired: boolean
}

export interface Appeal {
  appealId: string
  appealerId: string
  grounds: string
  evidence: any
  status: 'pending' | 'accepted' | 'rejected'
  resolution?: string
}

export interface ConsensusMetrics {
  totalProposals: number
  successRate: number
  averageConsensusTime: number
  participationRate: number
  conflictRate: number
  appealRate: number
  topContributors: AgentContribution[]
}

export interface AgentContribution {
  agentId: string
  proposalsSubmitted: number
  votesParticipated: number
  consensusContribution: number
  conflictResolutions: number
}

/**
 * Consensus Protocol Engine
 */
export class ConsensusProtocolEngine extends EventEmitter {
  private config: ConsensusConfig
  private activeProposals: Map<string, ConsensusProposal> = new Map()
  private proposalHistory: ConsensusProposal[] = []
  private agentVotingRights: Map<string, VotingRights> = new Map()
  private consensusMetrics: ConsensusMetrics

  constructor(config: Partial<ConsensusConfig> = {}) {
    super()
    this.config = {
      defaultProtocol: 'majority',
      quorumThreshold: 0.6,
      votingTimeout: 300000, // 5 minutes
      enableDelegatedVoting: true,
      enableVetoRights: false,
      conflictResolutionStrategy: 'mediation',
      ...config
    }

    this.consensusMetrics = this.initializeMetrics()
  }

  /**
   * Register agent with voting rights
   */
  registerVoter(agentId: string, rights: Partial<VotingRights>): void {
    const votingRights: VotingRights = {
      agentId,
      weight: rights.weight || 1,
      expertise: rights.expertise || [],
      authority: rights.authority || 1,
      canPropose: rights.canPropose !== false,
      canVeto: rights.canVeto || false,
      canDelegate: rights.canDelegate !== false,
      trustScore: rights.trustScore || 0.8,
      ...rights
    }

    this.agentVotingRights.set(agentId, votingRights)
    this.emit('voter_registered', { agentId, rights: votingRights })
  }

  /**
   * Submit proposal for consensus
   */
  async submitProposal(proposal: Partial<ConsensusProposal>): Promise<ConsensusProposal> {
    console.log(`📋 Submitting proposal: ${proposal.title}`)

    const fullProposal: ConsensusProposal = {
      proposalId: `prop-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      proposerId: proposal.proposerId!,
      title: proposal.title!,
      description: proposal.description!,
      type: proposal.type || 'decision',
      options: proposal.options || [],
      votingRules: proposal.votingRules || this.getDefaultVotingRules(),
      context: proposal.context || this.getDefaultContext(),
      status: 'active',
      createdAt: Date.now(),
      votingDeadline: Date.now() + this.config.votingTimeout
    }

    // Validate proposal
    this.validateProposal(fullProposal)

    // Store proposal
    this.activeProposals.set(fullProposal.proposalId, fullProposal)

    // Notify eligible voters
    await this.notifyEligibleVoters(fullProposal)

    // Start voting timeout
    this.startVotingTimeout(fullProposal.proposalId)

    this.emit('proposal_submitted', fullProposal)
    console.log(`✅ Proposal ${fullProposal.proposalId} submitted for voting`)

    return fullProposal
  }

  /**
   * Cast vote on proposal
   */
  async castVote(vote: Partial<AgentVote>): Promise<AgentVote> {
    const proposal = this.activeProposals.get(vote.proposalId!)
    if (!proposal) {
      throw new Error(`Proposal ${vote.proposalId} not found`)
    }

    if (proposal.status !== 'active') {
      throw new Error(`Proposal ${vote.proposalId} is not active`)
    }

    if (Date.now() > proposal.votingDeadline) {
      throw new Error(`Voting deadline has passed for proposal ${vote.proposalId}`)
    }

    // Validate voting rights
    const votingRights = this.agentVotingRights.get(vote.agentId!)
    if (!votingRights) {
      throw new Error(`Agent ${vote.agentId} does not have voting rights`)
    }

    const fullVote: AgentVote = {
      voteId: `vote-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      agentId: vote.agentId!,
      proposalId: vote.proposalId!,
      optionId: vote.optionId!,
      voteType: vote.voteType || 'support',
      weight: this.calculateVoteWeight(votingRights, proposal),
      confidence: vote.confidence || 0.8,
      reasoning: vote.reasoning || '',
      conditions: vote.conditions,
      timestamp: Date.now(),
      delegatedFrom: vote.delegatedFrom
    }

    // Store vote
    if (!proposal.votes) {
      proposal.votes = []
    }
    proposal.votes.push(fullVote)

    // Check if consensus reached
    if (await this.checkConsensusReached(proposal)) {
      await this.finalizeConsensus(proposal)
    }

    this.emit('vote_cast', fullVote)
    console.log(`🗳️ Vote cast by ${vote.agentId} on proposal ${vote.proposalId}`)

    return fullVote
  }

  /**
   * Delegate voting rights
   */
  async delegateVote(fromAgent: string, toAgent: string, proposalId: string, conditions?: any): Promise<void> {
    if (!this.config.enableDelegatedVoting) {
      throw new Error('Delegated voting is not enabled')
    }

    const fromRights = this.agentVotingRights.get(fromAgent)
    const toRights = this.agentVotingRights.get(toAgent)

    if (!fromRights || !toRights) {
      throw new Error('Invalid delegation: agent voting rights not found')
    }

    if (!fromRights.canDelegate) {
      throw new Error(`Agent ${fromAgent} cannot delegate votes`)
    }

    // Create delegation record
    const delegation = {
      fromAgent,
      toAgent,
      proposalId,
      conditions,
      timestamp: Date.now()
    }

    this.emit('vote_delegated', delegation)
    console.log(`🤝 Vote delegated from ${fromAgent} to ${toAgent}`)
  }

  /**
   * Exercise veto right
   */
  async exerciseVeto(agentId: string, proposalId: string, justification: string): Promise<void> {
    if (!this.config.enableVetoRights) {
      throw new Error('Veto rights are not enabled')
    }

    const proposal = this.activeProposals.get(proposalId)
    if (!proposal) {
      throw new Error(`Proposal ${proposalId} not found`)
    }

    const votingRights = this.agentVotingRights.get(agentId)
    if (!votingRights || !votingRights.canVeto) {
      throw new Error(`Agent ${agentId} does not have veto rights`)
    }

    // Check if veto is valid for this proposal
    const vetoRight = proposal.votingRules.vetoRights.find(v => v.agentId === agentId)
    if (!vetoRight) {
      throw new Error(`Agent ${agentId} does not have veto rights for this proposal`)
    }

    // Exercise veto
    const veto: AgentVote = {
      voteId: `veto-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      agentId,
      proposalId,
      optionId: 'veto',
      voteType: 'veto',
      weight: 1,
      confidence: 1,
      reasoning: justification,
      timestamp: Date.now()
    }

    if (!proposal.votes) {
      proposal.votes = []
    }
    proposal.votes.push(veto)

    // Veto immediately stops the proposal
    proposal.status = 'failed'
    proposal.result = {
      decision: 'vetoed',
      winningOption: 'none',
      votingSummary: this.calculateVotingSummary(proposal),
      consensusLevel: 0,
      dissent: { dissentLevel: 1, majorConcerns: [justification], dissentingAgents: [agentId], compromisePossible: false, recommendedActions: [] },
      implementation: { phases: [], timeline: '', responsibilities: [], successMetrics: [], rollbackPlan: { triggers: [], steps: [], timeframe: 0, approvalRequired: false } },
      appeals: []
    }

    this.moveToHistory(proposal)
    this.emit('proposal_vetoed', { proposal, veto })
    console.log(`❌ Proposal ${proposalId} vetoed by ${agentId}`)
  }

  /**
   * Get consensus metrics
   */
  getConsensusMetrics(): ConsensusMetrics {
    return { ...this.consensusMetrics }
  }

  /**
   * Private helper methods
   */
  private validateProposal(proposal: ConsensusProposal): void {
    if (!proposal.title || !proposal.description) {
      throw new Error('Proposal must have title and description')
    }

    if (proposal.options.length < 2) {
      throw new Error('Proposal must have at least 2 options')
    }

    const proposerRights = this.agentVotingRights.get(proposal.proposerId)
    if (!proposerRights || !proposerRights.canPropose) {
      throw new Error(`Agent ${proposal.proposerId} cannot submit proposals`)
    }
  }

  private getDefaultVotingRules(): VotingRules {
    return {
      protocol: this.config.defaultProtocol,
      quorum: this.config.quorumThreshold,
      allowAbstention: true,
      allowDelegation: this.config.enableDelegatedVoting,
      vetoRights: [],
      tieBreakingMethod: 'random'
    }
  }

  private getDefaultContext(): ProposalContext {
    return {
      urgency: 'medium',
      stakeholders: [],
      affectedSystems: [],
      prerequisites: [],
      constraints: {},
      relatedProposals: []
    }
  }

  private async notifyEligibleVoters(proposal: ConsensusProposal): Promise<void> {
    for (const [agentId] of this.agentVotingRights.entries()) {
      this.emit('voting_notification', { agentId, proposal })
    }
  }

  private startVotingTimeout(proposalId: string): void {
    setTimeout(async () => {
      const proposal = this.activeProposals.get(proposalId)
      if (proposal && proposal.status === 'active') {
        await this.finalizeConsensus(proposal)
      }
    }, this.config.votingTimeout)
  }

  private calculateVoteWeight(votingRights: VotingRights, proposal: ConsensusProposal): number {
    if (proposal.votingRules.protocol === 'weighted') {
      const criteria = proposal.votingRules.weightingCriteria!
      return (
        votingRights.authority * criteria.authorityWeight +
        votingRights.trustScore * criteria.trustWeight +
        votingRights.weight * 0.5
      )
    }
    return votingRights.weight
  }

  private async checkConsensusReached(proposal: ConsensusProposal): Promise<boolean> {
    if (!proposal.votes) return false

    const summary = this.calculateVotingSummary(proposal)
    
    // Check quorum
    if (summary.participationRate < proposal.votingRules.quorum) {
      return false
    }

    // Check if any option has enough support
    const winningOption = summary.optionResults.reduce((prev, current) => 
      current.percentage > prev.percentage ? current : prev
    )

    switch (proposal.votingRules.protocol) {
      case 'majority':
        return winningOption.percentage > 0.5
      case 'supermajority':
        return winningOption.percentage > 0.67
      case 'unanimous':
        return winningOption.percentage === 1.0
      default:
        return winningOption.percentage > 0.5
    }
  }

  private calculateVotingSummary(proposal: ConsensusProposal): VotingSummary {
    const votes = proposal.votes || []
    const totalEligible = this.agentVotingRights.size
    const optionCounts = new Map<string, { votes: number, weightedVotes: number, support: AgentVote[] }>()

    // Initialize option counts
    for (const option of proposal.options) {
      optionCounts.set(option.optionId, { votes: 0, weightedVotes: 0, support: [] })
    }

    // Count votes
    let totalVotes = 0
    let delegatedVotes = 0
    let vetoesReceived = 0

    for (const vote of votes) {
      if (vote.voteType === 'veto') {
        vetoesReceived++
        continue
      }

      if (vote.voteType === 'abstain') continue

      totalVotes++
      if (vote.delegatedFrom) delegatedVotes++

      const optionCount = optionCounts.get(vote.optionId)
      if (optionCount) {
        optionCount.votes++
        optionCount.weightedVotes += vote.weight
        optionCount.support.push(vote)
      }
    }

    // Calculate results
    const optionResults: OptionResult[] = []
    for (const [optionId, count] of optionCounts.entries()) {
      optionResults.push({
        optionId,
        votes: count.votes,
        weightedVotes: count.weightedVotes,
        percentage: totalVotes > 0 ? count.votes / totalVotes : 0,
        support: count.support
      })
    }

    return {
      totalEligibleVoters: totalEligible,
      totalVotes,
      participationRate: totalVotes / totalEligible,
      optionResults,
      vetoesReceived,
      delegatedVotes
    }
  }

  private async finalizeConsensus(proposal: ConsensusProposal): Promise<void> {
    const summary = this.calculateVotingSummary(proposal)
    
    // Determine winning option
    const winningOption = summary.optionResults.reduce((prev, current) => 
      current.percentage > prev.percentage ? current : prev
    )

    // Calculate consensus level
    const consensusLevel = winningOption.percentage

    // Analyze dissent
    const dissent = this.analyzeDissent(proposal, winningOption)

    // Create implementation plan
    const implementation = this.createImplementationPlan(proposal, winningOption)

    proposal.result = {
      decision: winningOption.optionId,
      winningOption: winningOption.optionId,
      votingSummary: summary,
      consensusLevel,
      dissent,
      implementation,
      appeals: []
    }

    proposal.status = 'completed'
    this.moveToHistory(proposal)

    this.emit('consensus_reached', proposal)
    console.log(`✅ Consensus reached on proposal ${proposal.proposalId}: ${winningOption.optionId}`)
  }

  private analyzeDissent(proposal: ConsensusProposal, winningOption: OptionResult): DissentAnalysis {
    const votes = proposal.votes || []
    const dissentingVotes = votes.filter(v => v.optionId !== winningOption.optionId && v.voteType !== 'abstain')
    
    return {
      dissentLevel: dissentingVotes.length / Math.max(votes.length, 1),
      majorConcerns: dissentingVotes.map(v => v.reasoning).filter(r => r),
      dissentingAgents: dissentingVotes.map(v => v.agentId),
      compromisePossible: dissentingVotes.length < votes.length * 0.3,
      recommendedActions: dissentingVotes.length > 0 ? ['Address concerns', 'Monitor implementation'] : []
    }
  }

  private createImplementationPlan(proposal: ConsensusProposal, winningOption: OptionResult): ImplementationPlan {
    return {
      phases: [
        {
          phaseId: 'implementation',
          name: 'Implementation Phase',
          description: 'Execute the decided option',
          startConditions: ['consensus_reached'],
          deliverables: ['option_implemented'],
          duration: 3600000, // 1 hour
          dependencies: []
        }
      ],
      timeline: '1 hour',
      responsibilities: winningOption.support.map(vote => ({
        agentId: vote.agentId,
        role: 'implementer',
        tasks: ['execute_decision'],
        authority: ['implementation'],
        accountability: ['results']
      })),
      successMetrics: [
        {
          name: 'implementation_success',
          target: 1,
          measurement: 'boolean',
          timeline: '1 hour'
        }
      ],
      rollbackPlan: {
        triggers: ['implementation_failure'],
        steps: ['revert_changes', 'notify_stakeholders'],
        timeframe: 1800000, // 30 minutes
        approvalRequired: true
      }
    }
  }

  private moveToHistory(proposal: ConsensusProposal): void {
    this.proposalHistory.push(proposal)
    this.activeProposals.delete(proposal.proposalId)
    this.updateMetrics(proposal)
  }

  private updateMetrics(proposal: ConsensusProposal): void {
    this.consensusMetrics.totalProposals++
    
    if (proposal.status === 'completed') {
      this.consensusMetrics.successRate = 
        (this.consensusMetrics.successRate * (this.consensusMetrics.totalProposals - 1) + 1) / 
        this.consensusMetrics.totalProposals
    }

    if (proposal.result) {
      this.consensusMetrics.participationRate = 
        (this.consensusMetrics.participationRate + proposal.result.votingSummary.participationRate) / 2
    }
  }

  private initializeMetrics(): ConsensusMetrics {
    return {
      totalProposals: 0,
      successRate: 0,
      averageConsensusTime: 0,
      participationRate: 0,
      conflictRate: 0,
      appealRate: 0,
      topContributors: []
    }
  }
}

interface VotingRights {
  agentId: string
  weight: number
  expertise: string[]
  authority: number
  canPropose: boolean
  canVeto: boolean
  canDelegate: boolean
  trustScore: number
}

export default ConsensusProtocolEngine
