/**
 * AG3NT Framework - Integration Agent
 * 
 * Specialized agent for connecting with external APIs and ensuring
 * service interoperability across the system.
 * 
 * Features:
 * - External API integration
 * - Service interoperability
 * - Data transformation and mapping
 * - Protocol adaptation
 * - Integration testing
 * - API gateway management
 */

import { BaseAgent, AgentState, AgentConfig } from "../core/base-agent"
import { aiService } from "../../ai-service"

export interface IntegrationInput {
  task: IntegrationTask
  services: ServiceInfo[]
  apis: APIInfo[]
  requirements: IntegrationRequirements
}

export interface IntegrationTask {
  taskId: string
  type: 'api_integration' | 'service_connection' | 'data_sync' | 'protocol_adaptation' | 'gateway_setup'
  title: string
  description: string
  priority: 'critical' | 'high' | 'medium' | 'low'
  scope: IntegrationScope
  deadline?: string
}

export interface IntegrationScope {
  services: string[]
  apis: string[]
  protocols: string[]
  dataFormats: string[]
  includeAuth: boolean
  includeMonitoring: boolean
  includeTesting: boolean
}

export interface ServiceInfo {
  name: string
  type: 'internal' | 'external' | 'third_party'
  url: string
  protocol: 'http' | 'https' | 'grpc' | 'graphql' | 'websocket' | 'tcp' | 'udp'
  authentication: AuthenticationInfo
  rateLimit: RateLimitInfo
  documentation: string
  status: 'active' | 'inactive' | 'deprecated'
  dependencies: string[]
  consumers: string[]
}

export interface AuthenticationInfo {
  type: 'none' | 'api_key' | 'bearer' | 'oauth' | 'basic' | 'certificate'
  configuration: any
  credentials: CredentialInfo
  scopes?: string[]
}

export interface CredentialInfo {
  location: 'header' | 'query' | 'body' | 'cookie'
  name: string
  format: string
  rotation: boolean
  expiry?: string
}

export interface RateLimitInfo {
  requests: number
  window: number
  strategy: 'fixed' | 'sliding' | 'token_bucket'
  headers: string[]
  retry: RetryInfo
}

export interface RetryInfo {
  enabled: boolean
  maxAttempts: number
  backoff: 'linear' | 'exponential' | 'fixed'
  delay: number
}

export interface APIInfo {
  name: string
  version: string
  baseUrl: string
  specification: APISpecification
  endpoints: EndpointInfo[]
  schemas: SchemaInfo[]
  authentication: AuthenticationInfo
  monitoring: MonitoringInfo
}

export interface APISpecification {
  type: 'openapi' | 'graphql' | 'grpc' | 'rest' | 'soap'
  version: string
  content: any
  documentation: string
}

export interface EndpointInfo {
  path: string
  method: string
  description: string
  parameters: ParameterInfo[]
  requestBody?: RequestBodyInfo
  responses: ResponseInfo[]
  examples: ExampleInfo[]
}

export interface ParameterInfo {
  name: string
  location: 'path' | 'query' | 'header' | 'cookie'
  type: string
  required: boolean
  description: string
  schema?: any
}

export interface RequestBodyInfo {
  description: string
  required: boolean
  contentType: string
  schema: any
  examples: any[]
}

export interface ResponseInfo {
  status: number
  description: string
  contentType: string
  schema?: any
  headers?: HeaderInfo[]
}

export interface HeaderInfo {
  name: string
  type: string
  description: string
  required: boolean
}

export interface ExampleInfo {
  name: string
  description: string
  request: any
  response: any
}

export interface SchemaInfo {
  name: string
  type: string
  properties: PropertyInfo[]
  required: string[]
  examples: any[]
}

export interface PropertyInfo {
  name: string
  type: string
  description: string
  format?: string
  enum?: any[]
  validation?: ValidationInfo
}

export interface ValidationInfo {
  min?: number
  max?: number
  pattern?: string
  format?: string
}

export interface MonitoringInfo {
  metrics: string[]
  alerts: AlertInfo[]
  logging: LoggingInfo
  tracing: TracingInfo
}

export interface AlertInfo {
  name: string
  condition: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  channels: string[]
}

export interface LoggingInfo {
  level: string
  format: string
  destination: string
  retention: number
}

export interface TracingInfo {
  enabled: boolean
  sampling: number
  headers: string[]
  correlation: boolean
}

export interface IntegrationRequirements {
  connectivity: ConnectivityRequirements
  data: DataRequirements
  security: SecurityRequirements
  performance: PerformanceRequirements
  reliability: ReliabilityRequirements
}

export interface ConnectivityRequirements {
  protocols: string[]
  formats: string[]
  compression: boolean
  encryption: boolean
  timeout: number
  keepAlive: boolean
}

export interface DataRequirements {
  transformation: TransformationRequirement[]
  validation: ValidationRequirement[]
  mapping: MappingRequirement[]
  synchronization: SyncRequirement
}

export interface TransformationRequirement {
  from: string
  to: string
  rules: TransformationRule[]
  bidirectional: boolean
}

export interface TransformationRule {
  field: string
  operation: string
  parameters: any
  condition?: string
}

export interface ValidationRequirement {
  schema: string
  rules: ValidationRule[]
  strict: boolean
  errorHandling: string
}

export interface ValidationRule {
  field: string
  type: string
  constraints: any
  message: string
}

export interface MappingRequirement {
  source: string
  target: string
  mappings: FieldMapping[]
  defaultValues: any
}

export interface FieldMapping {
  sourceField: string
  targetField: string
  transformation?: string
  condition?: string
}

export interface SyncRequirement {
  strategy: 'real_time' | 'batch' | 'event_driven' | 'scheduled'
  frequency?: string
  conflict: 'source_wins' | 'target_wins' | 'merge' | 'manual'
  ordering: boolean
}

export interface SecurityRequirements {
  authentication: boolean
  authorization: boolean
  encryption: boolean
  signing: boolean
  audit: boolean
}

export interface PerformanceRequirements {
  latency: number
  throughput: number
  concurrency: number
  caching: boolean
  compression: boolean
}

export interface ReliabilityRequirements {
  availability: number
  retry: boolean
  circuit: boolean
  fallback: boolean
  monitoring: boolean
}

export interface IntegrationResult {
  taskId: string
  status: 'completed' | 'failed' | 'partial'
  integrations: IntegrationImplementation[]
  configurations: ConfigurationFile[]
  tests: IntegrationTest[]
  documentation: IntegrationDocumentation[]
  monitoring: MonitoringSetup[]
  metrics: IntegrationMetrics
}

export interface IntegrationImplementation {
  name: string
  type: 'adapter' | 'gateway' | 'proxy' | 'connector' | 'transformer'
  services: string[]
  configuration: any
  code: CodeArtifact[]
  deployment: DeploymentInfo
}

export interface CodeArtifact {
  name: string
  type: 'class' | 'interface' | 'function' | 'configuration'
  language: string
  content: string
  dependencies: string[]
}

export interface DeploymentInfo {
  environment: string
  resources: ResourceRequirement[]
  configuration: any
  monitoring: boolean
}

export interface ResourceRequirement {
  type: string
  specification: any
  scaling: ScalingInfo
}

export interface ScalingInfo {
  min: number
  max: number
  target: number
  metric: string
}

export interface ConfigurationFile {
  name: string
  type: 'yaml' | 'json' | 'properties' | 'env'
  content: string
  environment: string
  sensitive: boolean
}

export interface IntegrationTest {
  name: string
  type: 'unit' | 'integration' | 'contract' | 'e2e'
  description: string
  setup: TestSetup
  scenarios: TestScenario[]
  assertions: TestAssertion[]
}

export interface TestSetup {
  prerequisites: string[]
  data: TestData[]
  mocks: MockService[]
  environment: any
}

export interface TestData {
  name: string
  type: string
  content: any
  cleanup: boolean
}

export interface MockService {
  name: string
  url: string
  responses: MockResponse[]
  behavior: MockBehavior
}

export interface MockResponse {
  request: any
  response: any
  delay?: number
  error?: any
}

export interface MockBehavior {
  latency: number
  errorRate: number
  availability: number
}

export interface TestScenario {
  name: string
  description: string
  steps: TestStep[]
  expected: any
}

export interface TestStep {
  action: string
  parameters: any
  validation?: any
}

export interface TestAssertion {
  type: string
  condition: string
  message: string
}

export interface IntegrationDocumentation {
  type: 'api' | 'integration' | 'troubleshooting' | 'deployment'
  title: string
  content: string
  examples: DocumentationExample[]
}

export interface DocumentationExample {
  title: string
  description: string
  code: string
  language: string
}

export interface MonitoringSetup {
  service: string
  metrics: MetricDefinition[]
  alerts: AlertDefinition[]
  dashboards: DashboardDefinition[]
}

export interface MetricDefinition {
  name: string
  type: 'counter' | 'gauge' | 'histogram' | 'summary'
  description: string
  labels: string[]
  unit: string
}

export interface AlertDefinition {
  name: string
  metric: string
  condition: string
  threshold: number
  severity: 'low' | 'medium' | 'high' | 'critical'
  actions: string[]
}

export interface DashboardDefinition {
  name: string
  description: string
  panels: PanelDefinition[]
  refresh: number
}

export interface PanelDefinition {
  title: string
  type: 'graph' | 'table' | 'stat' | 'gauge'
  query: string
  visualization: any
}

export interface IntegrationMetrics {
  connectionsEstablished: number
  dataTransformed: number
  errorsHandled: number
  latencyImproved: number
  throughputAchieved: number
  reliabilityScore: number
}

/**
 * Integration Agent - External API and service integration
 */
export class IntegrationAgent extends BaseAgent {
  private readonly integrationSteps = [
    'analyze_services', 'design_integration', 'implement_adapters',
    'setup_authentication', 'configure_data_flow', 'implement_monitoring',
    'create_tests', 'deploy_integration', 'validate_connectivity'
  ]

  constructor(config: Partial<AgentConfig> = {}) {
    super('integration', {
      capabilities: {
        requiredCapabilities: [
          'api_integration',
          'service_connectivity',
          'data_transformation',
          'protocol_adaptation',
          'authentication_setup',
          'monitoring_configuration',
          'integration_testing'
        ],
        contextFilters: ['integration', 'api', 'services', 'connectivity', 'data'],
        mcpEnhanced: true,
        sequentialThinking: true,
        contextEnrichment: true,
        ragIntegration: true
      },
      ...config
    })
  }

  /**
   * Execute integration workflow
   */
  protected async executeWorkflow(state: AgentState): Promise<AgentState> {
    const input = state.input as IntegrationInput
    
    console.log(`🔗 Starting integration workflow: ${input.task.title}`)

    // Execute integration steps sequentially
    for (const stepId of this.integrationSteps) {
      console.log(`🔄 Executing step: ${stepId}`)
      
      const stepResult = await this.executeStepWithContext(stepId, input)
      
      if (stepResult.needsInput) {
        state.needsInput = true
        state.question = stepResult.question
        break
      }

      // Update progress
      state.metadata.completedSteps++
      state.metadata.lastUpdated = new Date().toISOString()
      
      this.emit('step_completed', {
        agentId: this.agentId,
        stepId,
        progress: state.metadata.completedSteps / state.metadata.totalSteps
      })
    }

    // Mark as completed if no input needed
    if (!state.needsInput) {
      state.completed = true
      console.log(`✅ Integration workflow completed: ${input.task.title}`)
    }

    return state
  }

  /**
   * Execute individual integration step with context enhancement
   */
  private async executeStepWithContext(stepId: string, input?: any): Promise<any> {
    // Get enhanced context from context engine
    const enhancedState = await this.getEnhancedContext(stepId)

    // Execute step with MCP enhancement
    switch (stepId) {
      case 'analyze_services':
        return await this.analyzeServicesWithMCP(enhancedState, input)
      case 'design_integration':
        return await this.designIntegrationWithMCP(enhancedState)
      case 'implement_adapters':
        return await this.implementAdaptersWithMCP(enhancedState)
      case 'setup_authentication':
        return await this.setupAuthenticationWithMCP(enhancedState)
      case 'configure_data_flow':
        return await this.configureDataFlowWithMCP(enhancedState)
      case 'implement_monitoring':
        return await this.implementMonitoringWithMCP(enhancedState)
      case 'create_tests':
        return await this.createTestsWithMCP(enhancedState)
      case 'deploy_integration':
        return await this.deployIntegrationWithMCP(enhancedState)
      case 'validate_connectivity':
        return await this.validateConnectivityWithMCP(enhancedState)
      default:
        throw new Error(`Unknown integration step: ${stepId}`)
    }
  }

  /**
   * Get total steps for progress tracking
   */
  protected getTotalSteps(): number {
    return this.integrationSteps.length
  }

  /**
   * Get relevant documentation for integration
   */
  protected async getRelevantDocumentation(): Promise<Record<string, any>> {
    return {
      integration: 'API integration patterns and best practices',
      connectivity: 'Service connectivity and interoperability',
      authentication: 'Authentication and authorization patterns',
      dataTransformation: 'Data transformation and mapping techniques',
      monitoring: 'Integration monitoring and observability',
      testing: 'Integration testing strategies and patterns'
    }
  }

  // ============================================================================
  // STEP IMPLEMENTATIONS WITH MCP ENHANCEMENT
  // ============================================================================

  private async analyzeServicesWithMCP(state: any, input: IntegrationInput): Promise<any> {
    const analysis = await aiService.analyzeServicesForIntegration(
      input.services,
      input.apis,
      input.task.scope
    )

    this.state!.results.serviceAnalysis = analysis
    
    return {
      results: analysis,
      needsInput: false,
      completed: false
    }
  }

  private async designIntegrationWithMCP(state: any): Promise<any> {
    const serviceAnalysis = this.state!.results.serviceAnalysis
    
    const design = await aiService.designIntegrationArchitecture(
      serviceAnalysis,
      this.state!.input.requirements
    )

    this.state!.results.integrationDesign = design
    
    return {
      results: design,
      needsInput: false,
      completed: false
    }
  }

  private async implementAdaptersWithMCP(state: any): Promise<any> {
    const design = this.state!.results.integrationDesign
    
    const adapters = await aiService.implementIntegrationAdapters(design)

    this.state!.results.adapters = adapters
    
    return {
      results: adapters,
      needsInput: false,
      completed: false
    }
  }

  private async setupAuthenticationWithMCP(state: any): Promise<any> {
    const adapters = this.state!.results.adapters
    
    const authentication = await aiService.setupIntegrationAuthentication(
      adapters,
      this.state!.input.requirements.security
    )

    this.state!.results.authentication = authentication
    
    return {
      results: authentication,
      needsInput: false,
      completed: false
    }
  }

  private async configureDataFlowWithMCP(state: any): Promise<any> {
    const authentication = this.state!.results.authentication
    
    const dataFlow = await aiService.configureIntegrationDataFlow(
      authentication,
      this.state!.input.requirements.data
    )

    this.state!.results.dataFlow = dataFlow
    
    return {
      results: dataFlow,
      needsInput: false,
      completed: false
    }
  }

  private async implementMonitoringWithMCP(state: any): Promise<any> {
    const dataFlow = this.state!.results.dataFlow
    
    const monitoring = await aiService.implementIntegrationMonitoring(dataFlow)

    this.state!.results.monitoring = monitoring
    
    return {
      results: monitoring,
      needsInput: false,
      completed: false
    }
  }

  private async createTestsWithMCP(state: any): Promise<any> {
    const monitoring = this.state!.results.monitoring
    
    const tests = await aiService.createIntegrationTests(
      monitoring,
      this.state!.input.requirements
    )

    this.state!.results.tests = tests
    
    return {
      results: tests,
      needsInput: false,
      completed: false
    }
  }

  private async deployIntegrationWithMCP(state: any): Promise<any> {
    const tests = this.state!.results.tests
    
    const deployment = await aiService.deployIntegration(tests)

    this.state!.results.deployment = deployment
    
    return {
      results: deployment,
      needsInput: false,
      completed: false
    }
  }

  private async validateConnectivityWithMCP(state: any): Promise<any> {
    const deployment = this.state!.results.deployment
    
    const validation = await aiService.validateIntegrationConnectivity(
      deployment,
      this.state!.input.task
    )

    this.state!.results.validation = validation
    
    return {
      results: validation,
      needsInput: false,
      completed: true // Final step
    }
  }
}

// Export for easy access
export { IntegrationAgent as default }
