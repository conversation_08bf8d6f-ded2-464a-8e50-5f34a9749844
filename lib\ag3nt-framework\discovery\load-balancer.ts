/**
 * AG3NT Framework - Intelligent Load Balancer
 * 
 * Advanced load balancing system with multiple algorithms, health-aware routing,
 * and adaptive performance optimization for agent workload distribution.
 */

import { EventEmitter } from "events"
import { AgentInstance, AgentDiscoveryService } from "./agent-discovery-service"

export interface LoadBalancerConfig {
  algorithm: 'round_robin' | 'weighted_round_robin' | 'least_connections' | 'least_response_time' | 'adaptive' | 'consistent_hash'
  enableHealthChecks: boolean
  enableCircuitBreaker: boolean
  enableStickySessions: boolean
  maxRetries: number
  retryDelay: number
  circuitBreakerThreshold: number
  circuitBreakerTimeout: number
  adaptiveWindowSize: number
}

export interface LoadBalancingRequest {
  requestId: string
  agentType: string
  capabilities?: string[]
  sessionId?: string
  priority: 'low' | 'medium' | 'high' | 'critical'
  estimatedDuration?: number
  resourceRequirements?: {
    cpu: number
    memory: number
    network: number
  }
  metadata?: Record<string, any>
}

export interface LoadBalancingResult {
  selectedAgent: AgentInstance
  algorithm: string
  selectionTime: number
  reasoning: string[]
  alternativeAgents: AgentInstance[]
  loadDistribution: LoadDistribution
}

export interface LoadDistribution {
  totalAgents: number
  availableAgents: number
  averageLoad: number
  loadVariance: number
  hotspots: AgentHotspot[]
}

export interface AgentHotspot {
  agentId: string
  loadPercentage: number
  severity: 'warning' | 'critical'
  recommendation: string
}

export interface CircuitBreakerState {
  agentId: string
  state: 'closed' | 'open' | 'half_open'
  failureCount: number
  lastFailureTime: number
  nextRetryTime: number
}

export interface StickySession {
  sessionId: string
  agentId: string
  createdAt: number
  lastUsed: number
  requestCount: number
}

export interface LoadBalancingMetrics {
  totalRequests: number
  successfulRoutes: number
  failedRoutes: number
  averageSelectionTime: number
  algorithmUsage: Map<string, number>
  agentUtilization: Map<string, number>
  circuitBreakerTrips: number
  stickySessionHits: number
}

/**
 * Intelligent Load Balancer
 */
export class LoadBalancer extends EventEmitter {
  private config: LoadBalancerConfig
  private discoveryService: AgentDiscoveryService
  private circuitBreakers: Map<string, CircuitBreakerState> = new Map()
  private stickySessions: Map<string, StickySession> = new Map()
  private requestHistory: LoadBalancingRequest[] = []
  private metrics: LoadBalancingMetrics
  private roundRobinIndex: number = 0
  private adaptiveWeights: Map<string, number> = new Map()

  constructor(
    discoveryService: AgentDiscoveryService,
    config: Partial<LoadBalancerConfig> = {}
  ) {
    super()
    
    this.discoveryService = discoveryService
    this.config = {
      algorithm: 'adaptive',
      enableHealthChecks: true,
      enableCircuitBreaker: true,
      enableStickySessions: false,
      maxRetries: 3,
      retryDelay: 1000,
      circuitBreakerThreshold: 5,
      circuitBreakerTimeout: 30000,
      adaptiveWindowSize: 100,
      ...config
    }

    this.metrics = {
      totalRequests: 0,
      successfulRoutes: 0,
      failedRoutes: 0,
      averageSelectionTime: 0,
      algorithmUsage: new Map(),
      agentUtilization: new Map(),
      circuitBreakerTrips: 0,
      stickySessionHits: 0
    }

    this.setupEventHandlers()
  }

  /**
   * Route request to optimal agent
   */
  async routeRequest(request: LoadBalancingRequest): Promise<LoadBalancingResult> {
    const startTime = Date.now()
    this.metrics.totalRequests++

    console.log(`⚖️ Routing request ${request.requestId} (${request.agentType})`)

    try {
      // Check for sticky session
      if (this.config.enableStickySessions && request.sessionId) {
        const stickyResult = this.checkStickySession(request)
        if (stickyResult) {
          this.metrics.stickySessionHits++
          return this.createResult(stickyResult, 'sticky_session', startTime, ['Sticky session match'])
        }
      }

      // Get available agents
      const availableAgents = await this.getAvailableAgents(request)
      if (availableAgents.length === 0) {
        throw new Error('No available agents found')
      }

      // Apply load balancing algorithm
      const selectedAgent = await this.selectAgent(availableAgents, request)
      
      // Update circuit breaker and metrics
      this.updateCircuitBreaker(selectedAgent.agentId, true)
      this.updateStickySession(request, selectedAgent)
      
      const result = this.createResult(selectedAgent, this.config.algorithm, startTime, 
        this.generateSelectionReasoning(selectedAgent, availableAgents, request))

      this.metrics.successfulRoutes++
      this.updateMetrics(selectedAgent.agentId, this.config.algorithm, Date.now() - startTime)

      this.emit('request_routed', { request, result })
      return result

    } catch (error) {
      this.metrics.failedRoutes++
      this.emit('routing_failed', { request, error })
      throw error
    }
  }

  /**
   * Get load distribution statistics
   */
  getLoadDistribution(): LoadDistribution {
    const agents = this.discoveryService.getAllAgents()
    const availableAgents = agents.filter(a => a.status === 'healthy')
    
    const loads = availableAgents.map(a => a.performance.currentLoad / a.performance.maxLoad)
    const averageLoad = loads.reduce((sum, load) => sum + load, 0) / loads.length
    const loadVariance = loads.reduce((sum, load) => sum + Math.pow(load - averageLoad, 2), 0) / loads.length

    const hotspots: AgentHotspot[] = []
    for (const agent of availableAgents) {
      const loadPercentage = (agent.performance.currentLoad / agent.performance.maxLoad) * 100
      if (loadPercentage > 80) {
        hotspots.push({
          agentId: agent.agentId,
          loadPercentage,
          severity: loadPercentage > 95 ? 'critical' : 'warning',
          recommendation: loadPercentage > 95 
            ? 'Immediate load redistribution required'
            : 'Consider load redistribution'
        })
      }
    }

    return {
      totalAgents: agents.length,
      availableAgents: availableAgents.length,
      averageLoad,
      loadVariance,
      hotspots
    }
  }

  /**
   * Get load balancing metrics
   */
  getMetrics(): LoadBalancingMetrics {
    return { ...this.metrics }
  }

  /**
   * Update load balancer configuration
   */
  updateConfig(config: Partial<LoadBalancerConfig>): void {
    this.config = { ...this.config, ...config }
    this.emit('config_updated', this.config)
  }

  /**
   * Reset circuit breaker for agent
   */
  resetCircuitBreaker(agentId: string): void {
    this.circuitBreakers.delete(agentId)
    this.emit('circuit_breaker_reset', { agentId })
  }

  /**
   * Clear sticky session
   */
  clearStickySession(sessionId: string): void {
    this.stickySessions.delete(sessionId)
    this.emit('sticky_session_cleared', { sessionId })
  }

  /**
   * Private helper methods
   */
  private async getAvailableAgents(request: LoadBalancingRequest): Promise<AgentInstance[]> {
    const discovery = await this.discoveryService.discoverAgents({
      agentType: request.agentType,
      capabilities: request.capabilities
    })

    // Filter out agents with open circuit breakers
    return discovery.agents.filter(agent => {
      if (!this.config.enableCircuitBreaker) return true
      
      const circuitBreaker = this.circuitBreakers.get(agent.agentId)
      if (!circuitBreaker) return true
      
      if (circuitBreaker.state === 'open') {
        if (Date.now() > circuitBreaker.nextRetryTime) {
          circuitBreaker.state = 'half_open'
          return true
        }
        return false
      }
      
      return true
    })
  }

  private async selectAgent(agents: AgentInstance[], request: LoadBalancingRequest): Promise<AgentInstance> {
    switch (this.config.algorithm) {
      case 'round_robin':
        return this.roundRobinSelection(agents)
      
      case 'weighted_round_robin':
        return this.weightedRoundRobinSelection(agents)
      
      case 'least_connections':
        return this.leastConnectionsSelection(agents)
      
      case 'least_response_time':
        return this.leastResponseTimeSelection(agents)
      
      case 'consistent_hash':
        return this.consistentHashSelection(agents, request)
      
      case 'adaptive':
        return this.adaptiveSelection(agents, request)
      
      default:
        return this.roundRobinSelection(agents)
    }
  }

  private roundRobinSelection(agents: AgentInstance[]): AgentInstance {
    const agent = agents[this.roundRobinIndex % agents.length]
    this.roundRobinIndex++
    return agent
  }

  private weightedRoundRobinSelection(agents: AgentInstance[]): AgentInstance {
    // Weight based on inverse of current load
    const weights = agents.map(agent => {
      const loadRatio = agent.performance.currentLoad / agent.performance.maxLoad
      return Math.max(0.1, 1 - loadRatio) // Minimum weight of 0.1
    })

    const totalWeight = weights.reduce((sum, weight) => sum + weight, 0)
    let random = Math.random() * totalWeight
    
    for (let i = 0; i < agents.length; i++) {
      random -= weights[i]
      if (random <= 0) {
        return agents[i]
      }
    }
    
    return agents[0] // Fallback
  }

  private leastConnectionsSelection(agents: AgentInstance[]): AgentInstance {
    return agents.reduce((best, current) => 
      current.performance.currentLoad < best.performance.currentLoad ? current : best
    )
  }

  private leastResponseTimeSelection(agents: AgentInstance[]): AgentInstance {
    return agents.reduce((best, current) => 
      current.performance.averageResponseTime < best.performance.averageResponseTime ? current : best
    )
  }

  private consistentHashSelection(agents: AgentInstance[], request: LoadBalancingRequest): AgentInstance {
    // Simple hash-based selection for consistent routing
    const hash = this.hashString(request.sessionId || request.requestId)
    const index = hash % agents.length
    return agents[index]
  }

  private adaptiveSelection(agents: AgentInstance[], request: LoadBalancingRequest): AgentInstance {
    // Adaptive algorithm that learns from performance
    const scores = agents.map(agent => this.calculateAdaptiveScore(agent, request))
    const maxScore = Math.max(...scores)
    const bestIndex = scores.indexOf(maxScore)
    
    // Update adaptive weights based on selection
    this.updateAdaptiveWeights(agents[bestIndex], request)
    
    return agents[bestIndex]
  }

  private calculateAdaptiveScore(agent: AgentInstance, request: LoadBalancingRequest): number {
    let score = 0

    // Performance factors (40%)
    const performanceScore = (
      agent.performance.successRate * 0.4 +
      (1 - agent.performance.errorRate) * 0.3 +
      (1 - agent.performance.currentLoad / agent.performance.maxLoad) * 0.3
    )
    score += performanceScore * 0.4

    // Response time factor (30%)
    const responseTimeScore = Math.max(0, 1 - agent.performance.averageResponseTime / 10000)
    score += responseTimeScore * 0.3

    // Adaptive weight factor (20%)
    const adaptiveWeight = this.adaptiveWeights.get(agent.agentId) || 0.5
    score += adaptiveWeight * 0.2

    // Priority factor (10%)
    const priorityBoost = request.priority === 'critical' ? 0.2 : 
                         request.priority === 'high' ? 0.1 : 0
    score += priorityBoost * 0.1

    return score
  }

  private updateAdaptiveWeights(selectedAgent: AgentInstance, request: LoadBalancingRequest): void {
    // Update weights based on selection success (simplified)
    const currentWeight = this.adaptiveWeights.get(selectedAgent.agentId) || 0.5
    const newWeight = Math.min(1, currentWeight + 0.01) // Gradual increase
    this.adaptiveWeights.set(selectedAgent.agentId, newWeight)
  }

  private checkStickySession(request: LoadBalancingRequest): AgentInstance | null {
    if (!request.sessionId) return null
    
    const session = this.stickySessions.get(request.sessionId)
    if (!session) return null
    
    const agent = this.discoveryService.getAgent(session.agentId)
    if (!agent || agent.status !== 'healthy') {
      this.stickySessions.delete(request.sessionId)
      return null
    }
    
    // Update session
    session.lastUsed = Date.now()
    session.requestCount++
    
    return agent
  }

  private updateStickySession(request: LoadBalancingRequest, selectedAgent: AgentInstance): void {
    if (!this.config.enableStickySessions || !request.sessionId) return
    
    this.stickySessions.set(request.sessionId, {
      sessionId: request.sessionId,
      agentId: selectedAgent.agentId,
      createdAt: Date.now(),
      lastUsed: Date.now(),
      requestCount: 1
    })
  }

  private updateCircuitBreaker(agentId: string, success: boolean): void {
    if (!this.config.enableCircuitBreaker) return
    
    let circuitBreaker = this.circuitBreakers.get(agentId)
    if (!circuitBreaker) {
      circuitBreaker = {
        agentId,
        state: 'closed',
        failureCount: 0,
        lastFailureTime: 0,
        nextRetryTime: 0
      }
      this.circuitBreakers.set(agentId, circuitBreaker)
    }
    
    if (success) {
      if (circuitBreaker.state === 'half_open') {
        circuitBreaker.state = 'closed'
        circuitBreaker.failureCount = 0
        this.emit('circuit_breaker_closed', { agentId })
      }
    } else {
      circuitBreaker.failureCount++
      circuitBreaker.lastFailureTime = Date.now()
      
      if (circuitBreaker.failureCount >= this.config.circuitBreakerThreshold) {
        circuitBreaker.state = 'open'
        circuitBreaker.nextRetryTime = Date.now() + this.config.circuitBreakerTimeout
        this.metrics.circuitBreakerTrips++
        this.emit('circuit_breaker_opened', { agentId })
      }
    }
  }

  private createResult(
    selectedAgent: AgentInstance,
    algorithm: string,
    startTime: number,
    reasoning: string[]
  ): LoadBalancingResult {
    const availableAgents = this.discoveryService.getAllAgents()
      .filter(a => a.status === 'healthy' && a.agentId !== selectedAgent.agentId)
      .slice(0, 3) // Top 3 alternatives

    return {
      selectedAgent,
      algorithm,
      selectionTime: Date.now() - startTime,
      reasoning,
      alternativeAgents: availableAgents,
      loadDistribution: this.getLoadDistribution()
    }
  }

  private generateSelectionReasoning(
    selectedAgent: AgentInstance,
    availableAgents: AgentInstance[],
    request: LoadBalancingRequest
  ): string[] {
    const reasoning: string[] = []
    
    reasoning.push(`Selected using ${this.config.algorithm} algorithm`)
    
    if (selectedAgent.performance.currentLoad / selectedAgent.performance.maxLoad < 0.5) {
      reasoning.push('Low current load')
    }
    
    if (selectedAgent.performance.successRate > 0.95) {
      reasoning.push('High success rate')
    }
    
    if (selectedAgent.performance.averageResponseTime < 1000) {
      reasoning.push('Fast response time')
    }
    
    const loadRank = availableAgents
      .sort((a, b) => a.performance.currentLoad - b.performance.currentLoad)
      .findIndex(a => a.agentId === selectedAgent.agentId) + 1
    
    reasoning.push(`Ranked #${loadRank} by load among ${availableAgents.length} agents`)
    
    return reasoning
  }

  private updateMetrics(agentId: string, algorithm: string, selectionTime: number): void {
    // Update algorithm usage
    const currentUsage = this.metrics.algorithmUsage.get(algorithm) || 0
    this.metrics.algorithmUsage.set(algorithm, currentUsage + 1)
    
    // Update agent utilization
    const currentUtilization = this.metrics.agentUtilization.get(agentId) || 0
    this.metrics.agentUtilization.set(agentId, currentUtilization + 1)
    
    // Update average selection time
    this.metrics.averageSelectionTime = 
      (this.metrics.averageSelectionTime * (this.metrics.totalRequests - 1) + selectionTime) / 
      this.metrics.totalRequests
  }

  private hashString(str: string): number {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return Math.abs(hash)
  }

  private setupEventHandlers(): void {
    // Listen for agent status changes
    this.discoveryService.on('agent_status_changed', (event) => {
      if (event.newStatus === 'unhealthy') {
        this.updateCircuitBreaker(event.agentId, false)
      }
    })
    
    // Cleanup old sticky sessions
    setInterval(() => {
      const now = Date.now()
      const maxAge = 3600000 // 1 hour
      
      for (const [sessionId, session] of this.stickySessions.entries()) {
        if (now - session.lastUsed > maxAge) {
          this.stickySessions.delete(sessionId)
        }
      }
    }, 300000) // Check every 5 minutes
  }

  /**
   * Shutdown load balancer
   */
  shutdown(): void {
    this.circuitBreakers.clear()
    this.stickySessions.clear()
    this.requestHistory = []
    this.adaptiveWeights.clear()
    this.removeAllListeners()
    
    console.log('⚖️ Load Balancer shutdown complete')
  }
}

export default LoadBalancer
