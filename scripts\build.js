#!/usr/bin/env node

/**
 * Comprehensive build script for AG3NT Framework
 * Creates optimized bundles for different environments and use cases
 */

const esbuild = require('esbuild')
const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

const BUILD_CONFIG = {
  entryPoints: {
    main: 'lib/ag3nt-framework/index.ts',
    core: 'lib/ag3nt-framework/core/index.ts',
    agents: 'lib/ag3nt-framework/agents/index.ts',
    advanced: 'lib/ag3nt-framework/advanced/index.ts',
    cli: 'lib/cli/index.ts'
  },
  external: [
    // Node.js built-ins
    'fs', 'path', 'crypto', 'events', 'util', 'stream',
    // Large dependencies that should remain external
    'axios', 'ws', 'inquirer', 'commander'
  ],
  target: 'node18',
  platform: 'node'
}

async function buildFramework() {
  console.log('🚀 Building AG3NT Framework...')

  try {
    // Clean dist directory
    const distDir = path.join(process.cwd(), 'dist')
    if (fs.existsSync(distDir)) {
      fs.rmSync(distDir, { recursive: true, force: true })
    }
    fs.mkdirSync(distDir, { recursive: true })

    // Build TypeScript declarations
    console.log('📝 Generating TypeScript declarations...')
    await buildTypeScriptDeclarations()

    // Build CommonJS bundles
    console.log('📦 Building CommonJS bundles...')
    await buildCommonJS()

    // Build ESM bundles
    console.log('🎯 Building ESM bundles...')
    await buildESM()

    // Build CLI
    console.log('⚡ Building CLI...')
    await buildCLI()

    // Copy additional files
    console.log('📋 Copying additional files...')
    await copyAdditionalFiles()

    // Generate package manifests
    console.log('📄 Generating package manifests...')
    await generatePackageManifests()

    // Validate build
    console.log('✅ Validating build...')
    await validateBuild()

    console.log('🎉 Build completed successfully!')
    console.log('📊 Build summary:')
    await printBuildSummary()

  } catch (error) {
    console.error('❌ Build failed:', error)
    process.exit(1)
  }
}

async function buildTypeScriptDeclarations() {
  try {
    execSync('npx tsc -p tsconfig.build.json', { stdio: 'inherit' })
  } catch (error) {
    throw new Error('TypeScript compilation failed')
  }
}

async function buildCommonJS() {
  const builds = []

  for (const [name, entryPoint] of Object.entries(BUILD_CONFIG.entryPoints)) {
    if (name === 'cli') continue // CLI handled separately

    builds.push(
      esbuild.build({
        entryPoints: [entryPoint],
        bundle: true,
        platform: BUILD_CONFIG.platform,
        target: BUILD_CONFIG.target,
        format: 'cjs',
        outfile: `dist/lib/ag3nt-framework/${name === 'main' ? 'index' : name}.js`,
        external: BUILD_CONFIG.external,
        minify: true,
        sourcemap: true,
        metafile: true,
        define: {
          'process.env.NODE_ENV': '"production"'
        }
      })
    )
  }

  await Promise.all(builds)
}

async function buildESM() {
  const builds = []

  for (const [name, entryPoint] of Object.entries(BUILD_CONFIG.entryPoints)) {
    if (name === 'cli') continue // CLI handled separately

    builds.push(
      esbuild.build({
        entryPoints: [entryPoint],
        bundle: true,
        platform: BUILD_CONFIG.platform,
        target: BUILD_CONFIG.target,
        format: 'esm',
        outfile: `dist/lib/ag3nt-framework/${name === 'main' ? 'index' : name}.esm.js`,
        external: BUILD_CONFIG.external,
        minify: true,
        sourcemap: true,
        metafile: true,
        define: {
          'process.env.NODE_ENV': '"production"'
        }
      })
    )
  }

  await Promise.all(builds)
}

async function buildCLI() {
  await esbuild.build({
    entryPoints: [BUILD_CONFIG.entryPoints.cli],
    bundle: true,
    platform: BUILD_CONFIG.platform,
    target: BUILD_CONFIG.target,
    format: 'cjs',
    outfile: 'dist/cli/index.js',
    external: BUILD_CONFIG.external,
    banner: {
      js: '#!/usr/bin/env node'
    },
    minify: true,
    sourcemap: true,
    metafile: true,
    define: {
      'process.env.NODE_ENV': '"production"'
    }
  })

  // Make CLI executable
  const cliPath = path.join('dist', 'cli', 'index.js')
  if (fs.existsSync(cliPath)) {
    fs.chmodSync(cliPath, '755')
  }
}

async function copyAdditionalFiles() {
  const filesToCopy = [
    { src: 'README.md', dest: 'dist/README.md' },
    { src: 'LICENSE.md', dest: 'dist/LICENSE.md' },
    { src: 'CHANGELOG.md', dest: 'dist/CHANGELOG.md', optional: true }
  ]

  for (const file of filesToCopy) {
    if (fs.existsSync(file.src)) {
      fs.copyFileSync(file.src, file.dest)
    } else if (!file.optional) {
      console.warn(`Warning: ${file.src} not found`)
    }
  }

  // Copy docs if they exist
  const docsDir = path.join('docs')
  const distDocsDir = path.join('dist', 'docs')
  if (fs.existsSync(docsDir)) {
    fs.cpSync(docsDir, distDocsDir, { recursive: true })
  }
}

async function generatePackageManifests() {
  // Generate main package.json for distribution
  const originalPackage = JSON.parse(fs.readFileSync('package.json', 'utf8'))
  
  const distPackage = {
    name: originalPackage.name,
    version: originalPackage.version,
    description: originalPackage.description,
    keywords: originalPackage.keywords,
    homepage: originalPackage.homepage,
    repository: originalPackage.repository,
    bugs: originalPackage.bugs,
    license: originalPackage.license,
    author: originalPackage.author,
    main: originalPackage.main,
    module: originalPackage.module,
    types: originalPackage.types,
    bin: originalPackage.bin,
    files: originalPackage.files,
    exports: originalPackage.exports,
    dependencies: originalPackage.dependencies,
    peerDependencies: originalPackage.peerDependencies,
    engines: originalPackage.engines,
    publishConfig: originalPackage.publishConfig,
    ag3nt: originalPackage.ag3nt
  }

  fs.writeFileSync(
    path.join('dist', 'package.json'),
    JSON.stringify(distPackage, null, 2)
  )

  // Generate sub-package manifests for modular imports
  const subPackages = ['core', 'agents', 'advanced']
  
  for (const subPackage of subPackages) {
    const subPackageJson = {
      name: `${originalPackage.name}/${subPackage}`,
      version: originalPackage.version,
      description: `AG3NT Framework - ${subPackage} module`,
      main: `${subPackage}.js`,
      module: `${subPackage}.esm.js`,
      types: `${subPackage}.d.ts`,
      sideEffects: false
    }

    const subDir = path.join('dist', 'lib', 'ag3nt-framework')
    fs.writeFileSync(
      path.join(subDir, `${subPackage}.package.json`),
      JSON.stringify(subPackageJson, null, 2)
    )
  }
}

async function validateBuild() {
  const requiredFiles = [
    'dist/lib/ag3nt-framework/index.js',
    'dist/lib/ag3nt-framework/index.esm.js',
    'dist/lib/ag3nt-framework/index.d.ts',
    'dist/cli/index.js',
    'dist/package.json'
  ]

  for (const file of requiredFiles) {
    if (!fs.existsSync(file)) {
      throw new Error(`Required build file missing: ${file}`)
    }
  }

  // Validate that bundles can be required/imported
  try {
    require(path.resolve('dist/lib/ag3nt-framework/index.js'))
    console.log('✅ CommonJS bundle validation passed')
  } catch (error) {
    throw new Error(`CommonJS bundle validation failed: ${error.message}`)
  }
}

async function printBuildSummary() {
  const distDir = path.join('dist')
  const getFileSize = (filePath) => {
    try {
      const stats = fs.statSync(filePath)
      return (stats.size / 1024).toFixed(2) + ' KB'
    } catch {
      return 'N/A'
    }
  }

  console.log('📊 Bundle sizes:')
  console.log(`  Main (CJS): ${getFileSize('dist/lib/ag3nt-framework/index.js')}`)
  console.log(`  Main (ESM): ${getFileSize('dist/lib/ag3nt-framework/index.esm.js')}`)
  console.log(`  Core (CJS): ${getFileSize('dist/lib/ag3nt-framework/core.js')}`)
  console.log(`  Agents (CJS): ${getFileSize('dist/lib/ag3nt-framework/agents.js')}`)
  console.log(`  Advanced (CJS): ${getFileSize('dist/lib/ag3nt-framework/advanced.js')}`)
  console.log(`  CLI: ${getFileSize('dist/cli/index.js')}`)

  // Count total files
  const countFiles = (dir) => {
    let count = 0
    const items = fs.readdirSync(dir, { withFileTypes: true })
    for (const item of items) {
      if (item.isFile()) {
        count++
      } else if (item.isDirectory()) {
        count += countFiles(path.join(dir, item.name))
      }
    }
    return count
  }

  console.log(`📁 Total files: ${countFiles(distDir)}`)
}

// Run build if called directly
if (require.main === module) {
  buildFramework()
}

module.exports = { buildFramework }
