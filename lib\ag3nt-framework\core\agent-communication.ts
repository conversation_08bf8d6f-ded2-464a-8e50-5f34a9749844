/**
 * AG3NT Framework - Agent Communication Protocol
 * 
 * Sophisticated inter-agent messaging and coordination system.
 * Extracted from unified-context-engine.ts patterns and enhanced for multi-agent workflows.
 * 
 * Features:
 * - Real-time agent-to-agent messaging
 * - Event-driven coordination
 * - Message routing and delivery guarantees
 * - Context sharing and synchronization
 * - Workflow orchestration support
 * - Distributed state management
 * - Message persistence and replay
 * - Agent discovery and presence
 */

import { EventEmitter } from "events"
import { AgentState } from "./base-agent"

// Message Types
export interface AgentMessage {
  messageId: string
  fromAgentId: string
  toAgentId?: string // undefined for broadcast
  messageType: MessageType
  payload: any
  timestamp: string
  sessionId?: string
  correlationId?: string
  priority: MessagePriority
  ttl?: number // Time to live in milliseconds
  metadata: MessageMetadata
}

export type MessageType =
  | 'request' | 'response' | 'notification' | 'broadcast'
  | 'context_update' | 'state_sync' | 'workflow_event'
  | 'coordination' | 'handoff' | 'error' | 'heartbeat'
  | 'delegation' | 'consensus' | 'vote' | 'approval'

export type MessagePriority = 'low' | 'normal' | 'high' | 'critical'

export interface MessageMetadata {
  retryCount: number
  maxRetries: number
  deliveryAttempts: string[]
  requiresAck: boolean
  encrypted: boolean
  compressed: boolean
}

// Communication Channels
export interface CommunicationChannel {
  channelId: string
  channelType: ChannelType
  participants: Set<string>
  metadata: ChannelMetadata
  messageHistory: AgentMessage[]
  isActive: boolean
}

export type ChannelType = 'direct' | 'broadcast' | 'workflow' | 'context' | 'coordination'

export interface ChannelMetadata {
  createdAt: string
  createdBy: string
  purpose: string
  tags: string[]
  maxParticipants?: number
  persistent: boolean
  encrypted: boolean
}

// Agent Presence and Discovery
export interface AgentPresence {
  agentId: string
  agentType: string
  status: PresenceStatus
  capabilities: string[]
  currentSessions: string[]
  lastSeen: string
  metadata: {
    version: string
    location: string
    load: number
    availability: number
  }
}

export type PresenceStatus = 'online' | 'busy' | 'idle' | 'offline' | 'error'

// Coordination Primitives
export interface CoordinationRequest {
  requestId: string
  requestType: CoordinationType
  fromAgentId: string
  targetAgents: string[]
  payload: any
  timeout: number
  requiresConsensus: boolean
}

export type CoordinationType =
  | 'task_delegation' | 'resource_request' | 'context_sync'
  | 'workflow_handoff' | 'consensus' | 'election' | 'barrier'

// Advanced Coordination Patterns
export interface TaskDelegation {
  delegationId: string
  fromAgent: string
  toAgent: string
  task: any
  context: any
  deadline?: string
  priority: MessagePriority
  requirements: string[]
  callbacks: DelegationCallbacks
  status: 'pending' | 'accepted' | 'rejected' | 'in_progress' | 'completed' | 'failed'
}

export interface DelegationCallbacks {
  onProgress?: (progress: number, details?: any) => void
  onComplete?: (result: any) => void
  onError?: (error: any) => void
  onTimeout?: () => void
}

export interface ConsensusRequest {
  proposalId: string
  proposer: string
  participants: string[]
  proposal: any
  votingDeadline: string
  consensusThreshold: number
  votingStrategy: 'majority' | 'unanimous' | 'weighted' | 'quorum'
  status: 'voting' | 'decided' | 'failed' | 'timeout'
}

export interface ConsensusVote {
  proposalId: string
  voter: string
  vote: 'approve' | 'reject' | 'abstain'
  weight?: number
  reasoning?: string
  conditions?: string[]
  timestamp: string
}

export interface WorkflowHandoff {
  handoffId: string
  fromAgent: string
  toAgent: string
  workflowStep: string
  context: any
  state: any
  requirements: string[]
  validationRules: string[]
  status: 'pending' | 'accepted' | 'rejected' | 'completed'
}

export interface AgentLoadInfo {
  agentId: string
  currentLoad: number // 0-1
  capacity: number
  queueSize: number
  averageResponseTime: number
  successRate: number
  capabilities: string[]
  availability: 'available' | 'busy' | 'offline'
}

// Message Handlers
export type MessageHandler = (message: AgentMessage) => Promise<AgentMessage | void>
export type ChannelHandler = (channel: CommunicationChannel, message: AgentMessage) => Promise<void>

/**
 * Agent Communication Protocol - Core messaging and coordination system
 */
export class AgentCommunicationProtocol extends EventEmitter {
  private channels: Map<string, CommunicationChannel> = new Map()
  private messageHandlers: Map<MessageType, Set<MessageHandler>> = new Map()
  private channelHandlers: Map<string, ChannelHandler> = new Map()
  private agentPresence: Map<string, AgentPresence> = new Map()
  private messageQueue: Map<string, AgentMessage[]> = new Map() // Per-agent queues
  private pendingMessages: Map<string, AgentMessage> = new Map()
  private coordinationRequests: Map<string, CoordinationRequest> = new Map()
  private isInitialized: boolean = false
  private heartbeatInterval: NodeJS.Timeout | null = null

  // Advanced coordination state
  private activeDelegations: Map<string, TaskDelegation> = new Map()
  private activeConsensus: Map<string, ConsensusRequest> = new Map()
  private consensusVotes: Map<string, ConsensusVote[]> = new Map()
  private activeHandoffs: Map<string, WorkflowHandoff> = new Map()
  private agentLoadInfo: Map<string, AgentLoadInfo> = new Map()

  constructor() {
    super()
    this.setupDefaultHandlers()
  }

  /**
   * Initialize the communication protocol
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return

    // Start heartbeat monitoring
    this.startHeartbeatMonitoring()

    // Create default channels
    await this.createDefaultChannels()

    this.isInitialized = true
    this.emit('protocol_initialized')
    console.log('📡 AG3NT Agent Communication Protocol initialized')
  }

  /**
   * Register an agent with the communication system
   */
  async registerAgent(
    agentId: string,
    agentType: string,
    capabilities: string[] = []
  ): Promise<void> {
    const presence: AgentPresence = {
      agentId,
      agentType,
      status: 'online',
      capabilities,
      currentSessions: [],
      lastSeen: new Date().toISOString(),
      metadata: {
        version: '1.0.0',
        location: 'local',
        load: 0,
        availability: 1.0
      }
    }

    this.agentPresence.set(agentId, presence)
    this.messageQueue.set(agentId, [])

    // Announce agent registration
    await this.broadcast({
      messageType: 'notification',
      payload: {
        event: 'agent_registered',
        agentId,
        agentType,
        capabilities
      },
      priority: 'normal'
    }, agentId)

    this.emit('agent_registered', { agentId, agentType })
    console.log(`📡 Agent registered in communication protocol: ${agentType} (${agentId})`)
  }

  /**
   * Send a direct message to another agent
   */
  async sendMessage(
    fromAgentId: string,
    toAgentId: string,
    messageType: MessageType,
    payload: any,
    options: {
      priority?: MessagePriority
      sessionId?: string
      correlationId?: string
      requiresAck?: boolean
      ttl?: number
    } = {}
  ): Promise<string> {
    const message: AgentMessage = {
      messageId: `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      fromAgentId,
      toAgentId,
      messageType,
      payload,
      timestamp: new Date().toISOString(),
      sessionId: options.sessionId,
      correlationId: options.correlationId,
      priority: options.priority || 'normal',
      ttl: options.ttl,
      metadata: {
        retryCount: 0,
        maxRetries: 3,
        deliveryAttempts: [],
        requiresAck: options.requiresAck || false,
        encrypted: false,
        compressed: false
      }
    }

    await this.deliverMessage(message)
    return message.messageId
  }

  /**
   * Broadcast a message to all agents or specific channel
   */
  async broadcast(
    messageData: {
      messageType: MessageType
      payload: any
      priority?: MessagePriority
      sessionId?: string
      channelId?: string
    },
    fromAgentId: string
  ): Promise<string> {
    const message: AgentMessage = {
      messageId: `broadcast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      fromAgentId,
      messageType: messageData.messageType,
      payload: messageData.payload,
      timestamp: new Date().toISOString(),
      sessionId: messageData.sessionId,
      priority: messageData.priority || 'normal',
      metadata: {
        retryCount: 0,
        maxRetries: 1, // Broadcasts don't retry
        deliveryAttempts: [],
        requiresAck: false,
        encrypted: false,
        compressed: false
      }
    }

    if (messageData.channelId) {
      await this.deliverToChannel(messageData.channelId, message)
    } else {
      // Broadcast to all agents
      const agents = Array.from(this.agentPresence.keys()).filter(id => id !== fromAgentId)
      for (const agentId of agents) {
        const agentMessage = { ...message, toAgentId: agentId }
        await this.deliverMessage(agentMessage)
      }
    }

    return message.messageId
  }

  /**
   * Create a communication channel
   */
  async createChannel(
    channelType: ChannelType,
    createdBy: string,
    options: {
      channelId?: string
      purpose?: string
      participants?: string[]
      persistent?: boolean
      encrypted?: boolean
      maxParticipants?: number
    } = {}
  ): Promise<string> {
    const channelId = options.channelId || `channel-${channelType}-${Date.now()}`
    
    const channel: CommunicationChannel = {
      channelId,
      channelType,
      participants: new Set(options.participants || [createdBy]),
      metadata: {
        createdAt: new Date().toISOString(),
        createdBy,
        purpose: options.purpose || `${channelType} channel`,
        tags: [],
        maxParticipants: options.maxParticipants,
        persistent: options.persistent || false,
        encrypted: options.encrypted || false
      },
      messageHistory: [],
      isActive: true
    }

    this.channels.set(channelId, channel)
    
    this.emit('channel_created', { channelId, channelType, createdBy })
    console.log(`📡 Communication channel created: ${channelType} (${channelId})`)
    
    return channelId
  }

  /**
   * Join a communication channel
   */
  async joinChannel(channelId: string, agentId: string): Promise<void> {
    const channel = this.channels.get(channelId)
    if (!channel) {
      throw new Error(`Channel not found: ${channelId}`)
    }

    if (channel.metadata.maxParticipants && 
        channel.participants.size >= channel.metadata.maxParticipants) {
      throw new Error(`Channel ${channelId} is at maximum capacity`)
    }

    channel.participants.add(agentId)
    
    // Notify other participants
    await this.deliverToChannel(channelId, {
      messageId: `join-${Date.now()}`,
      fromAgentId: 'system',
      messageType: 'notification',
      payload: {
        event: 'agent_joined',
        agentId,
        channelId
      },
      timestamp: new Date().toISOString(),
      priority: 'normal',
      metadata: {
        retryCount: 0,
        maxRetries: 1,
        deliveryAttempts: [],
        requiresAck: false,
        encrypted: false,
        compressed: false
      }
    })

    this.emit('agent_joined_channel', { channelId, agentId })
  }

  /**
   * Leave a communication channel
   */
  async leaveChannel(channelId: string, agentId: string): Promise<void> {
    const channel = this.channels.get(channelId)
    if (!channel) return

    channel.participants.delete(agentId)

    // Notify other participants
    if (channel.participants.size > 0) {
      await this.deliverToChannel(channelId, {
        messageId: `leave-${Date.now()}`,
        fromAgentId: 'system',
        messageType: 'notification',
        payload: {
          event: 'agent_left',
          agentId,
          channelId
        },
        timestamp: new Date().toISOString(),
        priority: 'normal',
        metadata: {
          retryCount: 0,
          maxRetries: 1,
          deliveryAttempts: [],
          requiresAck: false,
          encrypted: false,
          compressed: false
        }
      })
    }

    // Clean up empty non-persistent channels
    if (channel.participants.size === 0 && !channel.metadata.persistent) {
      this.channels.delete(channelId)
      this.emit('channel_deleted', { channelId })
    }

    this.emit('agent_left_channel', { channelId, agentId })
  }

  /**
   * Register a message handler for specific message types
   */
  registerMessageHandler(messageType: MessageType, handler: MessageHandler): void {
    if (!this.messageHandlers.has(messageType)) {
      this.messageHandlers.set(messageType, new Set())
    }
    this.messageHandlers.get(messageType)!.add(handler)
  }

  /**
   * Register a channel handler for specific channels
   */
  registerChannelHandler(channelId: string, handler: ChannelHandler): void {
    this.channelHandlers.set(channelId, handler)
  }

  /**
   * Get agent presence information
   */
  getAgentPresence(agentId?: string): AgentPresence | AgentPresence[] {
    if (agentId) {
      return this.agentPresence.get(agentId) || null
    }
    return Array.from(this.agentPresence.values())
  }

  /**
   * Update agent presence status
   */
  async updatePresence(
    agentId: string, 
    updates: Partial<Pick<AgentPresence, 'status' | 'currentSessions' | 'metadata'>>
  ): Promise<void> {
    const presence = this.agentPresence.get(agentId)
    if (!presence) return

    Object.assign(presence, updates, { lastSeen: new Date().toISOString() })

    // Broadcast presence update
    await this.broadcast({
      messageType: 'notification',
      payload: {
        event: 'presence_updated',
        agentId,
        presence: updates
      },
      priority: 'low'
    }, agentId)
  }

  /**
   * Get pending messages for an agent
   */
  getPendingMessages(agentId: string): AgentMessage[] {
    return this.messageQueue.get(agentId) || []
  }

  /**
   * Acknowledge message receipt
   */
  async acknowledgeMessage(messageId: string, agentId: string): Promise<void> {
    const message = this.pendingMessages.get(messageId)
    if (message && message.toAgentId === agentId) {
      this.pendingMessages.delete(messageId)
      
      // Send acknowledgment back to sender
      if (message.metadata.requiresAck) {
        await this.sendMessage(
          agentId,
          message.fromAgentId,
          'response',
          { 
            type: 'acknowledgment',
            originalMessageId: messageId,
            status: 'received'
          },
          { correlationId: message.correlationId }
        )
      }
    }
  }

  /**
   * Deliver message to specific agent
   */
  private async deliverMessage(message: AgentMessage): Promise<void> {
    if (!message.toAgentId) return

    const presence = this.agentPresence.get(message.toAgentId)
    if (!presence || presence.status === 'offline') {
      // Queue message for later delivery
      const queue = this.messageQueue.get(message.toAgentId) || []
      queue.push(message)
      this.messageQueue.set(message.toAgentId, queue)
      return
    }

    // Check TTL
    if (message.ttl) {
      const messageAge = Date.now() - new Date(message.timestamp).getTime()
      if (messageAge > message.ttl) {
        console.warn(`Message ${message.messageId} expired (TTL: ${message.ttl}ms)`)
        return
      }
    }

    // Track delivery attempt
    message.metadata.deliveryAttempts.push(new Date().toISOString())

    // Store for acknowledgment tracking
    if (message.metadata.requiresAck) {
      this.pendingMessages.set(message.messageId, message)
    }

    // Process message through handlers
    await this.processMessage(message)

    this.emit('message_delivered', {
      messageId: message.messageId,
      fromAgentId: message.fromAgentId,
      toAgentId: message.toAgentId
    })
  }

  /**
   * Deliver message to channel participants
   */
  private async deliverToChannel(channelId: string, message: AgentMessage): Promise<void> {
    const channel = this.channels.get(channelId)
    if (!channel || !channel.isActive) return

    // Add to channel history
    channel.messageHistory.push(message)

    // Limit history size
    if (channel.messageHistory.length > 1000) {
      channel.messageHistory = channel.messageHistory.slice(-1000)
    }

    // Process through channel handler
    const channelHandler = this.channelHandlers.get(channelId)
    if (channelHandler) {
      await channelHandler(channel, message)
    }

    // Deliver to all participants
    for (const participantId of channel.participants) {
      if (participantId !== message.fromAgentId) {
        const participantMessage = { ...message, toAgentId: participantId }
        await this.deliverMessage(participantMessage)
      }
    }
  }

  /**
   * Process message through registered handlers
   */
  private async processMessage(message: AgentMessage): Promise<void> {
    const handlers = this.messageHandlers.get(message.messageType) || new Set()

    for (const handler of handlers) {
      try {
        const response = await handler(message)
        if (response) {
          // Send response back to sender
          await this.deliverMessage(response)
        }
      } catch (error) {
        console.error(`Message handler error for ${message.messageType}:`, error)

        // Send error response if required
        if (message.metadata.requiresAck) {
          await this.sendMessage(
            message.toAgentId!,
            message.fromAgentId,
            'error',
            {
              originalMessageId: message.messageId,
              error: error instanceof Error ? error.message : 'Handler error'
            },
            { correlationId: message.correlationId }
          )
        }
      }
    }
  }

  /**
   * Setup default message handlers
   */
  private setupDefaultHandlers(): void {
    // Heartbeat handler
    this.registerMessageHandler('heartbeat', async (message) => {
      await this.updatePresence(message.fromAgentId, {
        status: 'online',
        metadata: { ...this.agentPresence.get(message.fromAgentId)?.metadata, load: message.payload.load || 0 }
      })

      return {
        messageId: `heartbeat-response-${Date.now()}`,
        fromAgentId: message.toAgentId!,
        toAgentId: message.fromAgentId,
        messageType: 'response',
        payload: { type: 'heartbeat_ack', timestamp: new Date().toISOString() },
        timestamp: new Date().toISOString(),
        priority: 'low',
        metadata: {
          retryCount: 0,
          maxRetries: 1,
          deliveryAttempts: [],
          requiresAck: false,
          encrypted: false,
          compressed: false
        }
      }
    })

    // Context update handler
    this.registerMessageHandler('context_update', async (message) => {
      this.emit('context_updated', {
        fromAgentId: message.fromAgentId,
        contextData: message.payload,
        sessionId: message.sessionId
      })
    })

    // State sync handler
    this.registerMessageHandler('state_sync', async (message) => {
      this.emit('state_sync_requested', {
        fromAgentId: message.fromAgentId,
        stateData: message.payload,
        sessionId: message.sessionId
      })
    })
  }

  /**
   * Create default communication channels
   */
  private async createDefaultChannels(): Promise<void> {
    // Global broadcast channel
    await this.createChannel('broadcast', 'system', {
      channelId: 'global-broadcast',
      purpose: 'Global agent announcements and notifications',
      persistent: true
    })

    // Context synchronization channel
    await this.createChannel('context', 'system', {
      channelId: 'context-sync',
      purpose: 'Context updates and synchronization',
      persistent: true
    })

    // Workflow coordination channel
    await this.createChannel('workflow', 'system', {
      channelId: 'workflow-coordination',
      purpose: 'Workflow orchestration and handoffs',
      persistent: true
    })
  }

  /**
   * Start heartbeat monitoring
   */
  private startHeartbeatMonitoring(): void {
    this.heartbeatInterval = setInterval(async () => {
      const now = Date.now()
      const heartbeatTimeout = 60000 // 1 minute

      for (const [agentId, presence] of this.agentPresence.entries()) {
        const lastSeen = new Date(presence.lastSeen).getTime()

        if (now - lastSeen > heartbeatTimeout && presence.status !== 'offline') {
          // Mark agent as offline
          presence.status = 'offline'

          // Broadcast offline status
          await this.broadcast({
            messageType: 'notification',
            payload: {
              event: 'agent_offline',
              agentId,
              lastSeen: presence.lastSeen
            },
            priority: 'normal'
          }, 'system')

          this.emit('agent_offline', { agentId, lastSeen: presence.lastSeen })
        }
      }
    }, 30000) // Check every 30 seconds
  }

  /**
   * Unregister an agent from the communication system
   */
  async unregisterAgent(agentId: string): Promise<void> {
    // Remove from presence
    this.agentPresence.delete(agentId)

    // Clear message queue
    this.messageQueue.delete(agentId)

    // Remove from all channels
    for (const [channelId, channel] of this.channels.entries()) {
      if (channel.participants.has(agentId)) {
        await this.leaveChannel(channelId, agentId)
      }
    }

    // Announce agent unregistration
    await this.broadcast({
      messageType: 'notification',
      payload: {
        event: 'agent_unregistered',
        agentId
      },
      priority: 'normal'
    }, 'system')

    this.emit('agent_unregistered', { agentId })
    console.log(`📡 Agent unregistered from communication protocol: ${agentId}`)
  }

  /**
   * Get communication statistics
   */
  getStats(): {
    agents: number
    channels: number
    pendingMessages: number
    totalMessages: number
    activeChannels: number
  } {
    const totalMessages = Array.from(this.channels.values())
      .reduce((sum, channel) => sum + channel.messageHistory.length, 0)

    const activeChannels = Array.from(this.channels.values())
      .filter(channel => channel.isActive).length

    return {
      agents: this.agentPresence.size,
      channels: this.channels.size,
      pendingMessages: this.pendingMessages.size,
      totalMessages,
      activeChannels
    }
  }

  // ============================================================================
  // ADVANCED COORDINATION PATTERNS
  // ============================================================================

  /**
   * Delegate a task to another agent
   */
  async delegateTask(delegation: Omit<TaskDelegation, 'delegationId' | 'status'>): Promise<string> {
    const delegationId = `delegation-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

    const taskDelegation: TaskDelegation = {
      ...delegation,
      delegationId,
      status: 'pending'
    }

    this.activeDelegations.set(delegationId, taskDelegation)

    // Send delegation message
    await this.sendMessage({
      messageType: 'delegation',
      toAgentId: delegation.toAgent,
      payload: {
        type: 'task_delegation',
        delegation: taskDelegation
      },
      priority: delegation.priority,
      metadata: {
        retryCount: 0,
        maxRetries: 3,
        deliveryAttempts: [],
        requiresAck: true,
        encrypted: false
      }
    })

    console.log(`🔄 Task delegated: ${delegationId} from ${delegation.fromAgent} to ${delegation.toAgent}`)
    return delegationId
  }

  /**
   * Accept or reject a task delegation
   */
  async respondToDelegation(delegationId: string, response: 'accept' | 'reject', reason?: string): Promise<void> {
    const delegation = this.activeDelegations.get(delegationId)
    if (!delegation) {
      throw new Error(`Delegation ${delegationId} not found`)
    }

    delegation.status = response === 'accept' ? 'accepted' : 'rejected'

    // Send response to delegating agent
    await this.sendMessage({
      messageType: 'response',
      toAgentId: delegation.fromAgent,
      payload: {
        type: 'delegation_response',
        delegationId,
        response,
        reason
      },
      priority: 'high',
      metadata: {
        retryCount: 0,
        maxRetries: 3,
        deliveryAttempts: [],
        requiresAck: true,
        encrypted: false
      }
    })

    console.log(`📝 Delegation ${delegationId} ${response}ed`)
  }

  /**
   * Initiate consensus among agents
   */
  async initiateConsensus(consensus: Omit<ConsensusRequest, 'status'>): Promise<string> {
    const consensusRequest: ConsensusRequest = {
      ...consensus,
      status: 'voting'
    }

    this.activeConsensus.set(consensus.proposalId, consensusRequest)
    this.consensusVotes.set(consensus.proposalId, [])

    // Send consensus request to all participants
    for (const participant of consensus.participants) {
      await this.sendMessage({
        messageType: 'consensus',
        toAgentId: participant,
        payload: {
          type: 'consensus_request',
          consensus: consensusRequest
        },
        priority: 'high',
        metadata: {
          retryCount: 0,
          maxRetries: 3,
          deliveryAttempts: [],
          requiresAck: true,
          encrypted: false
        }
      })
    }

    console.log(`🗳️ Consensus initiated: ${consensus.proposalId} with ${consensus.participants.length} participants`)
    return consensus.proposalId
  }

  /**
   * Submit a vote for consensus
   */
  async submitVote(vote: ConsensusVote): Promise<void> {
    const consensus = this.activeConsensus.get(vote.proposalId)
    if (!consensus) {
      throw new Error(`Consensus ${vote.proposalId} not found`)
    }

    if (consensus.status !== 'voting') {
      throw new Error(`Consensus ${vote.proposalId} is not in voting state`)
    }

    const votes = this.consensusVotes.get(vote.proposalId) || []

    // Remove any existing vote from this voter
    const filteredVotes = votes.filter(v => v.voter !== vote.voter)
    filteredVotes.push(vote)

    this.consensusVotes.set(vote.proposalId, filteredVotes)

    // Check if consensus is reached
    await this.checkConsensusResult(vote.proposalId)

    console.log(`🗳️ Vote submitted: ${vote.voter} voted ${vote.vote} on ${vote.proposalId}`)
  }

  /**
   * Initiate workflow handoff
   */
  async initiateHandoff(handoff: Omit<WorkflowHandoff, 'status'>): Promise<string> {
    const workflowHandoff: WorkflowHandoff = {
      ...handoff,
      status: 'pending'
    }

    this.activeHandoffs.set(handoff.handoffId, workflowHandoff)

    // Send handoff message
    await this.sendMessage({
      messageType: 'handoff',
      toAgentId: handoff.toAgent,
      payload: {
        type: 'workflow_handoff',
        handoff: workflowHandoff
      },
      priority: 'high',
      metadata: {
        retryCount: 0,
        maxRetries: 3,
        deliveryAttempts: [],
        requiresAck: true,
        encrypted: false
      }
    })

    console.log(`🔄 Workflow handoff initiated: ${handoff.handoffId} from ${handoff.fromAgent} to ${handoff.toAgent}`)
    return handoff.handoffId
  }

  /**
   * Get optimal agent for task delegation based on load and capabilities
   */
  async getOptimalAgent(requiredCapabilities: string[], excludeAgents: string[] = []): Promise<string | null> {
    let bestAgent: string | null = null
    let bestScore = -1

    for (const [agentId, loadInfo] of this.agentLoadInfo.entries()) {
      if (excludeAgents.includes(agentId)) continue
      if (loadInfo.availability !== 'available') continue

      // Check if agent has required capabilities
      const hasCapabilities = requiredCapabilities.every(cap =>
        loadInfo.capabilities.includes(cap)
      )
      if (!hasCapabilities) continue

      // Calculate score based on load, response time, and success rate
      const loadScore = 1 - loadInfo.currentLoad
      const responseScore = 1 / (loadInfo.averageResponseTime + 1)
      const successScore = loadInfo.successRate

      const totalScore = (loadScore * 0.4) + (responseScore * 0.3) + (successScore * 0.3)

      if (totalScore > bestScore) {
        bestScore = totalScore
        bestAgent = agentId
      }
    }

    return bestAgent
  }

  /**
   * Update agent load information
   */
  updateAgentLoad(agentId: string, loadInfo: Partial<AgentLoadInfo>): void {
    const existing = this.agentLoadInfo.get(agentId) || {
      agentId,
      currentLoad: 0,
      capacity: 100,
      queueSize: 0,
      averageResponseTime: 1000,
      successRate: 1.0,
      capabilities: [],
      availability: 'available'
    }

    this.agentLoadInfo.set(agentId, { ...existing, ...loadInfo })
  }

  /**
   * Check consensus result
   */
  private async checkConsensusResult(proposalId: string): Promise<void> {
    const consensus = this.activeConsensus.get(proposalId)
    const votes = this.consensusVotes.get(proposalId)

    if (!consensus || !votes) return

    const totalVotes = votes.length
    const requiredVotes = Math.ceil(consensus.participants.length * consensus.consensusThreshold)

    if (totalVotes < requiredVotes) return

    // Calculate result based on voting strategy
    let result: 'approved' | 'rejected' = 'rejected'

    switch (consensus.votingStrategy) {
      case 'majority':
        const approveVotes = votes.filter(v => v.vote === 'approve').length
        result = approveVotes > totalVotes / 2 ? 'approved' : 'rejected'
        break
      case 'unanimous':
        result = votes.every(v => v.vote === 'approve') ? 'approved' : 'rejected'
        break
      case 'weighted':
        const totalWeight = votes.reduce((sum, v) => sum + (v.weight || 1), 0)
        const approveWeight = votes.filter(v => v.vote === 'approve').reduce((sum, v) => sum + (v.weight || 1), 0)
        result = approveWeight > totalWeight / 2 ? 'approved' : 'rejected'
        break
      case 'quorum':
        result = totalVotes >= requiredVotes ? 'approved' : 'rejected'
        break
    }

    consensus.status = result === 'approved' ? 'decided' : 'failed'

    // Notify all participants of the result
    for (const participant of consensus.participants) {
      await this.sendMessage({
        messageType: 'notification',
        toAgentId: participant,
        payload: {
          type: 'consensus_result',
          proposalId,
          result,
          votes: votes.length,
          details: votes
        },
        priority: 'high',
        metadata: {
          retryCount: 0,
          maxRetries: 3,
          deliveryAttempts: [],
          requiresAck: false,
          encrypted: false
        }
      })
    }

    console.log(`🗳️ Consensus ${proposalId} ${result} with ${votes.length} votes`)
  }

  /**
   * Shutdown the communication protocol
   */
  async shutdown(): Promise<void> {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }

    // Clear all data
    this.channels.clear()
    this.messageHandlers.clear()
    this.channelHandlers.clear()
    this.agentPresence.clear()
    this.messageQueue.clear()
    this.pendingMessages.clear()
    this.coordinationRequests.clear()

    // Clear advanced coordination state
    this.activeDelegations.clear()
    this.activeConsensus.clear()
    this.consensusVotes.clear()
    this.activeHandoffs.clear()
    this.agentLoadInfo.clear()

    this.isInitialized = false
    this.emit('protocol_shutdown')
    console.log('📡 AG3NT Agent Communication Protocol shutdown complete')
  }
}
