{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./lib", "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "emitDeclarationOnly": false, "composite": false, "incremental": true, "tsBuildInfoFile": "./dist/.tsbuildinfo"}, "include": ["lib/**/*"], "exclude": ["lib/**/*.test.ts", "lib/**/*.spec.ts", "lib/**/__tests__/**", "lib/**/examples/**", "lib/**/benchmarks/**", "node_modules", "dist", "app", "components", "public", "stories", "tests", "examples", "benchmarks"]}