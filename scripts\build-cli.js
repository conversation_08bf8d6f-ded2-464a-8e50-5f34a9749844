#!/usr/bin/env node

/**
 * Build script for AG3NT CLI
 * Creates optimized CLI bundle with proper shebang and permissions
 */

const esbuild = require('esbuild')
const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

async function buildCLI() {
  console.log('🔨 Building AG3NT CLI...')

  try {
    // Ensure dist directory exists
    const distDir = path.join(process.cwd(), 'dist')
    const cliDir = path.join(distDir, 'cli')
    
    if (!fs.existsSync(distDir)) {
      fs.mkdirSync(distDir, { recursive: true })
    }
    if (!fs.existsSync(cliDir)) {
      fs.mkdirSync(cliDir, { recursive: true })
    }

    // Build CLI with esbuild
    await esbuild.build({
      entryPoints: ['lib/cli/index.ts'],
      bundle: true,
      platform: 'node',
      target: 'node18',
      format: 'cjs',
      outfile: 'dist/cli/index.js',
      external: [
        // Keep these as external dependencies
        'inquirer',
        'commander',
        'chalk',
        'ora'
      ],
      banner: {
        js: '#!/usr/bin/env node'
      },
      minify: true,
      sourcemap: true,
      metafile: true
    })

    // Make CLI executable
    const cliPath = path.join(cliDir, 'index.js')
    if (fs.existsSync(cliPath)) {
      fs.chmodSync(cliPath, '755')
    }

    // Copy CLI command files
    const commandsDir = path.join('lib', 'cli', 'commands')
    const distCommandsDir = path.join(cliDir, 'commands')
    
    if (fs.existsSync(commandsDir)) {
      if (!fs.existsSync(distCommandsDir)) {
        fs.mkdirSync(distCommandsDir, { recursive: true })
      }
      
      // Copy command files (they'll be bundled but we keep them for reference)
      const commandFiles = fs.readdirSync(commandsDir)
      for (const file of commandFiles) {
        if (file.endsWith('.ts')) {
          const jsFile = file.replace('.ts', '.js')
          const sourcePath = path.join(commandsDir, file)
          const destPath = path.join(distCommandsDir, jsFile)
          
          // Compile TypeScript file
          try {
            execSync(`npx tsc ${sourcePath} --outDir ${distCommandsDir} --target ES2020 --module commonjs`, {
              stdio: 'ignore'
            })
          } catch (error) {
            console.warn(`Warning: Could not compile ${file}`)
          }
        }
      }
    }

    console.log('✅ CLI build completed successfully!')
    console.log(`📦 CLI bundle: ${cliPath}`)

  } catch (error) {
    console.error('❌ CLI build failed:', error)
    process.exit(1)
  }
}

// Run build if called directly
if (require.main === module) {
  buildCLI()
}

module.exports = { buildCLI }
