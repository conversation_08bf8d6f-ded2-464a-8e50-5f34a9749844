/**
 * AG3NT Framework - Workflow Coordinator
 * 
 * Orchestrates multi-agent workflows using the communication protocol.
 * Extracted from Inngest workflow patterns and enhanced for agent coordination.
 * 
 * Features:
 * - Multi-agent workflow orchestration
 * - Task delegation and handoffs
 * - Parallel and sequential execution
 * - Workflow state management
 * - Error handling and recovery
 * - Progress tracking and monitoring
 * - Dynamic workflow adaptation
 */

import { EventEmitter } from "events"
import { AgentCommunicationProtocol, MessageType } from "./agent-communication"
import { AgentRegistry, RegisteredAgent } from "./agent-registry"
import { AgentState } from "./base-agent"

// Workflow Definitions
export interface WorkflowDefinition {
  workflowId: string
  name: string
  description: string
  version: string
  steps: WorkflowStep[]
  dependencies: WorkflowDependency[]
  errorHandling: WorkflowErrorHandling
  timeout: number
  maxRetries: number
}

export interface WorkflowStep {
  stepId: string
  name: string
  agentType: string
  requiredCapabilities: string[]
  input: any
  dependencies: string[]
  parallel: boolean
  optional: boolean
  timeout: number
  maxRetries: number
  onError: 'fail' | 'skip' | 'retry' | 'fallback'
  fallbackStep?: string
}

export interface WorkflowDependency {
  stepId: string
  dependsOn: string[]
  condition?: string
  dataMapping?: Record<string, string>
}

export interface WorkflowErrorHandling {
  strategy: 'fail_fast' | 'continue' | 'retry_all'
  maxRetries: number
  retryDelay: number
  fallbackWorkflow?: string
}

// Workflow Execution
export interface WorkflowExecution {
  executionId: string
  workflowId: string
  sessionId: string
  status: WorkflowStatus
  input: any
  output?: any
  steps: WorkflowStepExecution[]
  startTime: string
  endTime?: string
  duration?: number
  error?: string
  metadata: WorkflowExecutionMetadata
}

export type WorkflowStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled' | 'paused'

export interface WorkflowStepExecution {
  stepId: string
  agentId?: string
  status: WorkflowStepStatus
  input: any
  output?: any
  startTime?: string
  endTime?: string
  duration?: number
  error?: string
  retryCount: number
}

export type WorkflowStepStatus = 'pending' | 'assigned' | 'running' | 'completed' | 'failed' | 'skipped'

export interface WorkflowExecutionMetadata {
  createdBy: string
  priority: 'low' | 'medium' | 'high' | 'critical'
  tags: string[]
  context: Record<string, any>
  parentExecution?: string
  childExecutions: string[]
}

/**
 * Workflow Coordinator - Multi-agent workflow orchestration
 */
export class WorkflowCoordinator extends EventEmitter {
  private communication: AgentCommunicationProtocol
  private registry: AgentRegistry
  private workflows: Map<string, WorkflowDefinition> = new Map()
  private executions: Map<string, WorkflowExecution> = new Map()
  private activeSteps: Map<string, WorkflowStepExecution> = new Map()
  private isInitialized: boolean = false

  constructor(
    communication: AgentCommunicationProtocol,
    registry: AgentRegistry
  ) {
    super()
    this.communication = communication
    this.registry = registry
    this.setupCommunicationHandlers()
  }

  /**
   * Initialize the workflow coordinator
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return

    // Join workflow coordination channel
    await this.communication.joinChannel('workflow-coordination', 'workflow-coordinator')

    this.isInitialized = true
    this.emit('coordinator_initialized')
    console.log('🎭 AG3NT Workflow Coordinator initialized')
  }

  /**
   * Register a workflow definition
   */
  registerWorkflow(workflow: WorkflowDefinition): void {
    this.workflows.set(workflow.workflowId, workflow)
    this.emit('workflow_registered', { workflowId: workflow.workflowId })
    console.log(`🎭 Workflow registered: ${workflow.name} (${workflow.workflowId})`)
  }

  /**
   * Execute a workflow
   */
  async executeWorkflow(
    workflowId: string,
    input: any,
    options: {
      sessionId?: string
      priority?: 'low' | 'medium' | 'high' | 'critical'
      tags?: string[]
      context?: Record<string, any>
      createdBy?: string
    } = {}
  ): Promise<string> {
    const workflow = this.workflows.get(workflowId)
    if (!workflow) {
      throw new Error(`Workflow not found: ${workflowId}`)
    }

    const executionId = `exec-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    const sessionId = options.sessionId || `session-${Date.now()}`

    const execution: WorkflowExecution = {
      executionId,
      workflowId,
      sessionId,
      status: 'pending',
      input,
      steps: workflow.steps.map(step => ({
        stepId: step.stepId,
        status: 'pending',
        input: this.prepareStepInput(step, input),
        retryCount: 0
      })),
      startTime: new Date().toISOString(),
      metadata: {
        createdBy: options.createdBy || 'system',
        priority: options.priority || 'medium',
        tags: options.tags || [],
        context: options.context || {},
        childExecutions: []
      }
    }

    this.executions.set(executionId, execution)

    // Start execution
    await this.startExecution(execution)

    this.emit('workflow_started', { executionId, workflowId, sessionId })
    console.log(`🎭 Workflow execution started: ${workflow.name} (${executionId})`)

    return executionId
  }

  /**
   * Get workflow execution status
   */
  getExecution(executionId: string): WorkflowExecution | null {
    return this.executions.get(executionId) || null
  }

  /**
   * Cancel workflow execution
   */
  async cancelExecution(executionId: string): Promise<void> {
    const execution = this.executions.get(executionId)
    if (!execution || execution.status === 'completed' || execution.status === 'failed') {
      return
    }

    execution.status = 'cancelled'
    execution.endTime = new Date().toISOString()
    execution.duration = new Date(execution.endTime).getTime() - new Date(execution.startTime).getTime()

    // Cancel active steps
    for (const step of execution.steps) {
      if (step.status === 'running' || step.status === 'assigned') {
        step.status = 'skipped'
        if (step.agentId) {
          await this.communication.sendMessage(
            'workflow-coordinator',
            step.agentId,
            'workflow_event',
            {
              event: 'step_cancelled',
              executionId,
              stepId: step.stepId
            },
            { sessionId: execution.sessionId }
          )
        }
      }
    }

    this.emit('workflow_cancelled', { executionId })
    console.log(`🎭 Workflow execution cancelled: ${executionId}`)
  }

  /**
   * Start workflow execution
   */
  private async startExecution(execution: WorkflowExecution): Promise<void> {
    execution.status = 'running'

    const workflow = this.workflows.get(execution.workflowId)!
    
    // Find steps that can be executed immediately (no dependencies)
    const readySteps = execution.steps.filter(step => {
      const stepDef = workflow.steps.find(s => s.stepId === step.stepId)!
      return step.status === 'pending' && this.areDependenciesMet(stepDef, execution)
    })

    // Execute ready steps
    for (const step of readySteps) {
      await this.executeStep(execution, step)
    }
  }

  /**
   * Execute a workflow step
   */
  private async executeStep(execution: WorkflowExecution, step: WorkflowStepExecution): Promise<void> {
    const workflow = this.workflows.get(execution.workflowId)!
    const stepDef = workflow.steps.find(s => s.stepId === step.stepId)!

    // Find best agent for the step
    const agent = this.registry.getBestAgent({
      agentType: stepDef.agentType,
      requiredCapabilities: stepDef.requiredCapabilities,
      priority: execution.metadata.priority === 'critical' ? 10 : 
                execution.metadata.priority === 'high' ? 7 :
                execution.metadata.priority === 'medium' ? 5 : 1
    })

    if (!agent) {
      step.status = 'failed'
      step.error = `No available agent found for type: ${stepDef.agentType}`
      await this.handleStepFailure(execution, step, stepDef)
      return
    }

    step.agentId = agent.agentId
    step.status = 'assigned'
    step.startTime = new Date().toISOString()

    this.activeSteps.set(`${execution.executionId}:${step.stepId}`, step)

    // Send step execution request to agent
    await this.communication.sendMessage(
      'workflow-coordinator',
      agent.agentId,
      'request',
      {
        type: 'execute_step',
        executionId: execution.executionId,
        stepId: step.stepId,
        stepDefinition: stepDef,
        input: step.input,
        context: execution.metadata.context
      },
      {
        sessionId: execution.sessionId,
        requiresAck: true,
        correlationId: `${execution.executionId}:${step.stepId}`,
        ttl: stepDef.timeout
      }
    )

    step.status = 'running'
    this.emit('step_started', { executionId: execution.executionId, stepId: step.stepId, agentId: agent.agentId })
  }

  /**
   * Handle step completion
   */
  private async handleStepCompletion(
    executionId: string,
    stepId: string,
    result: any,
    agentId: string
  ): Promise<void> {
    const execution = this.executions.get(executionId)
    if (!execution) return

    const step = execution.steps.find(s => s.stepId === stepId)
    if (!step) return

    step.status = 'completed'
    step.output = result
    step.endTime = new Date().toISOString()
    step.duration = new Date(step.endTime).getTime() - new Date(step.startTime!).getTime()

    this.activeSteps.delete(`${executionId}:${stepId}`)

    this.emit('step_completed', { executionId, stepId, agentId, result })

    // Check if workflow is complete
    await this.checkWorkflowCompletion(execution)

    // Execute next ready steps
    await this.executeNextSteps(execution)
  }

  /**
   * Handle step failure
   */
  private async handleStepFailure(
    execution: WorkflowExecution,
    step: WorkflowStepExecution,
    stepDef: WorkflowStep
  ): Promise<void> {
    const workflow = this.workflows.get(execution.workflowId)!

    step.retryCount++

    // Check if we should retry
    if (step.retryCount < stepDef.maxRetries && stepDef.onError === 'retry') {
      console.log(`🔄 Retrying step ${step.stepId} (attempt ${step.retryCount + 1})`)
      step.status = 'pending'
      setTimeout(() => this.executeStep(execution, step), 1000 * step.retryCount)
      return
    }

    // Handle based on error strategy
    switch (stepDef.onError) {
      case 'skip':
        step.status = 'skipped'
        await this.executeNextSteps(execution)
        break

      case 'fallback':
        if (stepDef.fallbackStep) {
          const fallbackStep = execution.steps.find(s => s.stepId === stepDef.fallbackStep)
          if (fallbackStep && fallbackStep.status === 'pending') {
            await this.executeStep(execution, fallbackStep)
          }
        }
        break

      case 'fail':
      default:
        execution.status = 'failed'
        execution.error = step.error
        execution.endTime = new Date().toISOString()
        execution.duration = new Date(execution.endTime).getTime() - new Date(execution.startTime).getTime()
        
        this.emit('workflow_failed', { executionId: execution.executionId, error: step.error })
        break
    }
  }

  /**
   * Check if workflow is complete
   */
  private async checkWorkflowCompletion(execution: WorkflowExecution): Promise<void> {
    const allStepsComplete = execution.steps.every(step => 
      step.status === 'completed' || step.status === 'skipped' || step.status === 'failed'
    )

    if (allStepsComplete && execution.status === 'running') {
      const hasFailures = execution.steps.some(step => step.status === 'failed')
      
      if (hasFailures) {
        execution.status = 'failed'
        execution.error = 'One or more steps failed'
      } else {
        execution.status = 'completed'
        execution.output = this.collectWorkflowOutput(execution)
      }

      execution.endTime = new Date().toISOString()
      execution.duration = new Date(execution.endTime).getTime() - new Date(execution.startTime).getTime()

      this.emit('workflow_completed', { 
        executionId: execution.executionId, 
        status: execution.status,
        output: execution.output 
      })

      console.log(`🎭 Workflow execution ${execution.status}: ${execution.executionId}`)
    }
  }

  /**
   * Execute next ready steps
   */
  private async executeNextSteps(execution: WorkflowExecution): Promise<void> {
    if (execution.status !== 'running') return

    const workflow = this.workflows.get(execution.workflowId)!
    
    const readySteps = execution.steps.filter(step => {
      const stepDef = workflow.steps.find(s => s.stepId === step.stepId)!
      return step.status === 'pending' && this.areDependenciesMet(stepDef, execution)
    })

    for (const step of readySteps) {
      await this.executeStep(execution, step)
    }
  }

  /**
   * Check if step dependencies are met
   */
  private areDependenciesMet(stepDef: WorkflowStep, execution: WorkflowExecution): boolean {
    return stepDef.dependencies.every(depStepId => {
      const depStep = execution.steps.find(s => s.stepId === depStepId)
      return depStep && (depStep.status === 'completed' || depStep.status === 'skipped')
    })
  }

  /**
   * Prepare step input from workflow input and previous step outputs
   */
  private prepareStepInput(stepDef: WorkflowStep, workflowInput: any): any {
    // For now, just pass the workflow input
    // TODO: Implement data mapping from previous step outputs
    return { ...workflowInput, stepId: stepDef.stepId }
  }

  /**
   * Collect workflow output from completed steps
   */
  private collectWorkflowOutput(execution: WorkflowExecution): any {
    const output: Record<string, any> = {}
    
    for (const step of execution.steps) {
      if (step.status === 'completed' && step.output) {
        output[step.stepId] = step.output
      }
    }

    return output
  }

  /**
   * Setup communication handlers for workflow coordination
   */
  private setupCommunicationHandlers(): void {
    this.communication.registerMessageHandler('response', async (message) => {
      if (message.payload.type === 'step_completed') {
        await this.handleStepCompletion(
          message.payload.executionId,
          message.payload.stepId,
          message.payload.result,
          message.fromAgentId
        )
      } else if (message.payload.type === 'step_failed') {
        const execution = this.executions.get(message.payload.executionId)
        const step = execution?.steps.find(s => s.stepId === message.payload.stepId)
        
        if (execution && step) {
          step.status = 'failed'
          step.error = message.payload.error
          step.endTime = new Date().toISOString()
          step.duration = step.startTime ? 
            new Date(step.endTime).getTime() - new Date(step.startTime).getTime() : 0

          const workflow = this.workflows.get(execution.workflowId)!
          const stepDef = workflow.steps.find(s => s.stepId === step.stepId)!
          
          await this.handleStepFailure(execution, step, stepDef)
        }
      }
    })
  }

  /**
   * Get coordinator statistics
   */
  getStats(): {
    workflows: number
    activeExecutions: number
    completedExecutions: number
    failedExecutions: number
    activeSteps: number
  } {
    const executions = Array.from(this.executions.values())
    
    return {
      workflows: this.workflows.size,
      activeExecutions: executions.filter(e => e.status === 'running').length,
      completedExecutions: executions.filter(e => e.status === 'completed').length,
      failedExecutions: executions.filter(e => e.status === 'failed').length,
      activeSteps: this.activeSteps.size
    }
  }

  /**
   * Shutdown the workflow coordinator
   */
  async shutdown(): Promise<void> {
    // Cancel all active executions
    const activeExecutions = Array.from(this.executions.values())
      .filter(e => e.status === 'running')

    for (const execution of activeExecutions) {
      await this.cancelExecution(execution.executionId)
    }

    // Clear all data
    this.workflows.clear()
    this.executions.clear()
    this.activeSteps.clear()

    this.isInitialized = false
    this.emit('coordinator_shutdown')
    console.log('🎭 AG3NT Workflow Coordinator shutdown complete')
  }
}
