"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import {
  Activity,
  Brain,
  Code,
  Database,
  GitBranch,
  Play,
  Pause,
  Settings,
  Users,
  Zap,
  CheckCircle,
  Clock,
  AlertTriangle,
  ArrowLeft,
} from "lucide-react"

interface Agent {
  id: string
  name: string
  type: string
  status: "active" | "idle" | "error"
  currentTask?: string
  completedTasks: number
  icon: any
}

interface Project {
  id: string
  name: string
  status: "planning" | "development" | "testing" | "completed"
  progress: number
  tasksTotal: number
  tasksCompleted: number
  agents: string[]
}

interface AdminDashboardProps {
  onBack: () => void
}

export default function AdminDashboard({ onBack }: AdminDashboardProps) {
  const [agents] = useState<Agent[]>([
    {
      id: "1",
      name: "Project Planner",
      type: "Planning",
      status: "active",
      currentTask: "Analyzing requirements for E-commerce Platform",
      completedTasks: 12,
      icon: Brain,
    },
    {
      id: "2", 
      name: "Frontend Developer",
      type: "Coding",
      status: "active",
      currentTask: "Building React components",
      completedTasks: 8,
      icon: Code,
    },
    {
      id: "3",
      name: "Backend Developer", 
      type: "Coding",
      status: "idle",
      completedTasks: 15,
      icon: Database,
    },
    {
      id: "4",
      name: "QA Tester",
      type: "Testing",
      status: "active", 
      currentTask: "Running integration tests",
      completedTasks: 6,
      icon: CheckCircle,
    },
  ])

  const [projects] = useState<Project[]>([
    {
      id: "1",
      name: "E-commerce Platform",
      status: "development",
      progress: 65,
      tasksTotal: 24,
      tasksCompleted: 16,
      agents: ["1", "2", "4"],
    },
    {
      id: "2",
      name: "Analytics Dashboard", 
      status: "planning",
      progress: 15,
      tasksTotal: 18,
      tasksCompleted: 3,
      agents: ["1"],
    },
    {
      id: "3",
      name: "Mobile App",
      status: "testing",
      progress: 90,
      tasksTotal: 12,
      tasksCompleted: 11,
      agents: ["4"],
    },
  ])

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-500"
      case "idle":
        return "bg-yellow-500"
      case "error":
        return "bg-red-500"
      case "planning":
        return "bg-blue-500"
      case "development":
        return "bg-purple-500"
      case "testing":
        return "bg-orange-500"
      case "completed":
        return "bg-green-500"
      default:
        return "bg-gray-500"
    }
  }

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "active":
      case "development":
        return "default"
      case "idle":
      case "planning":
        return "secondary"
      case "error":
        return "destructive"
      case "testing":
        return "outline"
      case "completed":
        return "default"
      default:
        return "secondary"
    }
  }

  return (
    <div className="h-screen w-full bg-[#000000] text-[#e5e5e5] font-sans text-sm flex flex-col overflow-hidden">
      {/* Header */}
      <header className="h-14 bg-[#0a0a0a] border-b border-[#1a1a1a] flex items-center justify-between px-6">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="text-[#666] hover:text-white hover:bg-[#111111]"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Main
          </Button>
          <h1 className="text-lg font-semibold text-white">Admin Dashboard</h1>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-green-400 border-green-400">
            System Online
          </Badge>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex-1 p-6 overflow-auto">
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-4 bg-[#111111] border border-[#1a1a1a]">
            <TabsTrigger value="overview" className="data-[state=active]:bg-[#1a1a1a] data-[state=active]:text-white">
              Overview
            </TabsTrigger>
            <TabsTrigger value="agents" className="data-[state=active]:bg-[#1a1a1a] data-[state=active]:text-white">
              Agents
            </TabsTrigger>
            <TabsTrigger value="projects" className="data-[state=active]:bg-[#1a1a1a] data-[state=active]:text-white">
              Projects
            </TabsTrigger>
            <TabsTrigger value="system" className="data-[state=active]:bg-[#1a1a1a] data-[state=active]:text-white">
              System
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
              <Card className="bg-[#0a0a0a] border-[#1a1a1a]">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-[#e5e5e5]">Active Agents</CardTitle>
                  <Activity className="h-4 w-4 text-[#666]" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-white">3</div>
                  <p className="text-xs text-[#666]">+1 from last hour</p>
                </CardContent>
              </Card>

              <Card className="bg-[#0a0a0a] border-[#1a1a1a]">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-[#e5e5e5]">Active Projects</CardTitle>
                  <GitBranch className="h-4 w-4 text-[#666]" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-white">2</div>
                  <p className="text-xs text-[#666]">1 in development</p>
                </CardContent>
              </Card>

              <Card className="bg-[#0a0a0a] border-[#1a1a1a]">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-[#e5e5e5]">Tasks Completed</CardTitle>
                  <CheckCircle className="h-4 w-4 text-[#666]" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-white">41</div>
                  <p className="text-xs text-[#666]">+12 today</p>
                </CardContent>
              </Card>

              <Card className="bg-[#0a0a0a] border-[#1a1a1a]">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-[#e5e5e5]">System Load</CardTitle>
                  <Zap className="h-4 w-4 text-[#666]" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-white">67%</div>
                  <p className="text-xs text-[#666]">Normal operation</p>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
