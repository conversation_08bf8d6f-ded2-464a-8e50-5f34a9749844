/**
 * AG3NT Framework - Context Engine Agent
 * 
 * The centerpiece agent that provides deep codebase understanding and context.
 * Acts as the "neural network" connecting all agents with intelligent context.
 * 
 * Features:
 * - Codebase ingestion and analysis
 * - Relationship and dependency graph building
 * - Real-time context updates
 * - Cross-agent memory and knowledge sharing
 * - Semantic search and retrieval
 * - Temporal graph capabilities
 */

import { BaseAgent, AgentState, AgentConfig } from "../core/base-agent"
import { aiService } from "../../ai-service"

export interface ContextEngineInput {
  task: ContextTask
  codebase: CodebaseInfo
  query?: ContextQuery
  update?: ContextUpdate
}

export interface ContextTask {
  taskId: string
  type: 'ingest' | 'analyze' | 'query' | 'update' | 'graph_build' | 'search' | 'relate'
  title: string
  description: string
  priority: 'critical' | 'high' | 'medium' | 'low'
  scope: ContextScope
  requirements: string[]
}

export interface ContextScope {
  files: string[]
  directories: string[]
  languages: string[]
  frameworks: string[]
  depth: 'shallow' | 'medium' | 'deep'
  includeTests: boolean
  includeDocs: boolean
}

export interface CodebaseInfo {
  rootPath: string
  structure: FileStructure
  metadata: CodebaseMetadata
  dependencies: DependencyInfo[]
  configuration: ConfigurationInfo
}

export interface FileStructure {
  files: FileInfo[]
  directories: DirectoryInfo[]
  totalFiles: number
  totalLines: number
  languages: LanguageStats[]
}

export interface FileInfo {
  path: string
  name: string
  extension: string
  size: number
  lines: number
  language: string
  lastModified: string
  checksum: string
}

export interface DirectoryInfo {
  path: string
  name: string
  fileCount: number
  subdirectories: string[]
  purpose: string
}

export interface LanguageStats {
  language: string
  fileCount: number
  lineCount: number
  percentage: number
}

export interface CodebaseMetadata {
  name: string
  version: string
  description: string
  framework: string
  architecture: string
  patterns: string[]
  conventions: string[]
}

export interface DependencyInfo {
  name: string
  version: string
  type: 'runtime' | 'development' | 'peer' | 'optional'
  source: string
  vulnerabilities: SecurityVulnerability[]
  usage: DependencyUsage[]
}

export interface SecurityVulnerability {
  id: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  description: string
  fix: string
}

export interface DependencyUsage {
  file: string
  imports: string[]
  usage: string[]
}

export interface ConfigurationInfo {
  buildConfig: any
  environmentConfig: any
  deploymentConfig: any
  testConfig: any
}

export interface ContextQuery {
  queryId: string
  type: 'semantic' | 'structural' | 'dependency' | 'usage' | 'similarity'
  query: string
  filters: QueryFilter[]
  options: QueryOptions
}

export interface QueryFilter {
  field: string
  operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'regex'
  value: any
}

export interface QueryOptions {
  limit: number
  offset: number
  sortBy: string
  sortOrder: 'asc' | 'desc'
  includeContext: boolean
  includeRelations: boolean
}

export interface ContextUpdate {
  updateId: string
  type: 'file_change' | 'dependency_change' | 'structure_change' | 'metadata_change'
  changes: ChangeInfo[]
  timestamp: string
  source: string
}

export interface ChangeInfo {
  path: string
  type: 'added' | 'modified' | 'deleted' | 'moved'
  content?: string
  metadata?: any
}

export interface ContextEngineResult {
  taskId: string
  status: 'completed' | 'failed' | 'partial'
  context: EnhancedContext
  graph: DependencyGraph
  insights: ContextInsight[]
  recommendations: ContextRecommendation[]
  metrics: ContextMetrics
}

export interface EnhancedContext {
  codebase: ProcessedCodebase
  relationships: Relationship[]
  patterns: DetectedPattern[]
  knowledge: KnowledgeBase
  temporal: TemporalData
}

export interface ProcessedCodebase {
  structure: AnalyzedStructure
  symbols: SymbolTable
  dependencies: DependencyGraph
  metrics: CodeMetrics
}

export interface AnalyzedStructure {
  modules: ModuleInfo[]
  classes: ClassInfo[]
  functions: FunctionInfo[]
  interfaces: InterfaceInfo[]
  types: TypeInfo[]
}

export interface ModuleInfo {
  name: string
  path: string
  exports: string[]
  imports: ImportInfo[]
  dependencies: string[]
  complexity: number
  maintainability: number
}

export interface ClassInfo {
  name: string
  module: string
  methods: MethodInfo[]
  properties: PropertyInfo[]
  inheritance: string[]
  interfaces: string[]
  complexity: number
}

export interface FunctionInfo {
  name: string
  module: string
  parameters: ParameterInfo[]
  returnType: string
  complexity: number
  usage: UsageInfo[]
}

export interface InterfaceInfo {
  name: string
  module: string
  properties: PropertyInfo[]
  methods: MethodSignature[]
  extends: string[]
}

export interface TypeInfo {
  name: string
  module: string
  definition: string
  usage: UsageInfo[]
}

export interface ImportInfo {
  module: string
  imports: string[]
  type: 'default' | 'named' | 'namespace' | 'side-effect'
}

export interface MethodInfo {
  name: string
  parameters: ParameterInfo[]
  returnType: string
  visibility: 'public' | 'private' | 'protected'
  static: boolean
  complexity: number
}

export interface PropertyInfo {
  name: string
  type: string
  visibility: 'public' | 'private' | 'protected'
  static: boolean
  readonly: boolean
}

export interface ParameterInfo {
  name: string
  type: string
  optional: boolean
  default?: any
}

export interface MethodSignature {
  name: string
  parameters: ParameterInfo[]
  returnType: string
}

export interface UsageInfo {
  file: string
  line: number
  context: string
  type: 'call' | 'reference' | 'import' | 'extend' | 'implement'
}

export interface SymbolTable {
  symbols: Symbol[]
  scopes: Scope[]
  references: Reference[]
}

export interface Symbol {
  name: string
  type: string
  location: Location
  scope: string
  visibility: string
}

export interface Scope {
  id: string
  type: 'global' | 'module' | 'class' | 'function' | 'block'
  parent?: string
  symbols: string[]
}

export interface Reference {
  symbol: string
  location: Location
  type: 'read' | 'write' | 'call'
}

export interface Location {
  file: string
  line: number
  column: number
}

export interface DependencyGraph {
  nodes: GraphNode[]
  edges: GraphEdge[]
  clusters: GraphCluster[]
  metrics: GraphMetrics
}

export interface GraphNode {
  id: string
  type: 'module' | 'class' | 'function' | 'file' | 'package'
  name: string
  metadata: any
  metrics: NodeMetrics
}

export interface GraphEdge {
  from: string
  to: string
  type: 'depends' | 'imports' | 'calls' | 'extends' | 'implements'
  weight: number
  metadata: any
}

export interface GraphCluster {
  id: string
  name: string
  nodes: string[]
  type: 'module' | 'feature' | 'layer' | 'domain'
}

export interface GraphMetrics {
  nodeCount: number
  edgeCount: number
  density: number
  complexity: number
  modularity: number
}

export interface NodeMetrics {
  inDegree: number
  outDegree: number
  betweenness: number
  closeness: number
  pageRank: number
}

export interface Relationship {
  id: string
  type: 'dependency' | 'inheritance' | 'composition' | 'aggregation' | 'usage'
  source: string
  target: string
  strength: number
  metadata: any
}

export interface DetectedPattern {
  id: string
  type: 'design_pattern' | 'anti_pattern' | 'architectural_pattern' | 'code_smell'
  name: string
  description: string
  locations: Location[]
  confidence: number
  impact: 'positive' | 'negative' | 'neutral'
}

export interface KnowledgeBase {
  facts: KnowledgeFact[]
  rules: KnowledgeRule[]
  inferences: KnowledgeInference[]
}

export interface KnowledgeFact {
  id: string
  subject: string
  predicate: string
  object: string
  confidence: number
  source: string
}

export interface KnowledgeRule {
  id: string
  condition: string
  conclusion: string
  confidence: number
}

export interface KnowledgeInference {
  id: string
  premises: string[]
  conclusion: string
  confidence: number
  reasoning: string
}

export interface TemporalData {
  snapshots: TemporalSnapshot[]
  changes: TemporalChange[]
  trends: TemporalTrend[]
}

export interface TemporalSnapshot {
  timestamp: string
  version: string
  context: any
  metrics: any
}

export interface TemporalChange {
  timestamp: string
  type: string
  description: string
  impact: string
  metadata: any
}

export interface TemporalTrend {
  metric: string
  direction: 'increasing' | 'decreasing' | 'stable'
  rate: number
  confidence: number
}

export interface CodeMetrics {
  complexity: ComplexityMetrics
  quality: QualityMetrics
  maintainability: MaintainabilityMetrics
  testability: TestabilityMetrics
}

export interface ComplexityMetrics {
  cyclomatic: number
  cognitive: number
  halstead: HalsteadMetrics
  nesting: number
}

export interface HalsteadMetrics {
  vocabulary: number
  length: number
  difficulty: number
  effort: number
  bugs: number
}

export interface QualityMetrics {
  duplication: number
  coverage: number
  debt: number
  violations: number
}

export interface MaintainabilityMetrics {
  index: number
  changeability: number
  stability: number
  testability: number
}

export interface TestabilityMetrics {
  coverage: number
  testComplexity: number
  mockability: number
  isolation: number
}

export interface ContextInsight {
  id: string
  type: 'optimization' | 'risk' | 'opportunity' | 'pattern' | 'anomaly'
  title: string
  description: string
  impact: 'high' | 'medium' | 'low'
  confidence: number
  recommendations: string[]
}

export interface ContextRecommendation {
  id: string
  type: 'refactor' | 'optimize' | 'security' | 'performance' | 'maintainability'
  priority: 'high' | 'medium' | 'low'
  description: string
  rationale: string
  implementation: string
  effort: string
  impact: string
}

export interface ContextMetrics {
  ingestionTime: number
  analysisTime: number
  graphBuildTime: number
  memoryUsage: number
  accuracy: number
  completeness: number
}

/**
 * Context Engine Agent - Deep codebase understanding and intelligence
 */
export class ContextEngineAgent extends BaseAgent {
  private readonly contextSteps = [
    'ingest_codebase', 'analyze_structure', 'build_dependency_graph',
    'extract_patterns', 'build_knowledge_base', 'create_relationships',
    'generate_insights', 'optimize_context', 'update_temporal_data'
  ]

  constructor(config: Partial<AgentConfig> = {}) {
    super('context-engine', {
      capabilities: {
        requiredCapabilities: [
          'codebase_analysis',
          'dependency_mapping',
          'pattern_recognition',
          'semantic_search',
          'graph_building',
          'knowledge_extraction',
          'temporal_tracking',
          'context_optimization'
        ],
        contextFilters: ['codebase', 'structure', 'dependencies', 'patterns', 'knowledge'],
        mcpEnhanced: true,
        sequentialThinking: true,
        contextEnrichment: true,
        ragIntegration: true
      },
      ...config
    })
  }

  /**
   * Execute context engine workflow
   */
  protected async executeWorkflow(state: AgentState): Promise<AgentState> {
    const input = state.input as ContextEngineInput
    
    console.log(`🧠 Starting context engine analysis: ${input.task.title}`)

    // Execute context steps sequentially
    for (const stepId of this.contextSteps) {
      console.log(`🔄 Executing step: ${stepId}`)
      
      const stepResult = await this.executeStepWithContext(stepId, input)
      
      if (stepResult.needsInput) {
        state.needsInput = true
        state.question = stepResult.question
        break
      }

      // Update progress
      state.metadata.completedSteps++
      state.metadata.lastUpdated = new Date().toISOString()
      
      this.emit('step_completed', {
        agentId: this.agentId,
        stepId,
        progress: state.metadata.completedSteps / state.metadata.totalSteps
      })
    }

    // Mark as completed if no input needed
    if (!state.needsInput) {
      state.completed = true
      console.log(`✅ Context engine analysis completed: ${input.task.title}`)
    }

    return state
  }

  /**
   * Execute individual context step with enhancement
   */
  private async executeStepWithContext(stepId: string, input?: any): Promise<any> {
    // Get enhanced context from context engine (self-referential)
    const enhancedState = await this.getEnhancedContext(stepId)

    // Execute step with MCP enhancement
    switch (stepId) {
      case 'ingest_codebase':
        return await this.ingestCodebaseWithMCP(enhancedState, input)
      case 'analyze_structure':
        return await this.analyzeStructureWithMCP(enhancedState)
      case 'build_dependency_graph':
        return await this.buildDependencyGraphWithMCP(enhancedState)
      case 'extract_patterns':
        return await this.extractPatternsWithMCP(enhancedState)
      case 'build_knowledge_base':
        return await this.buildKnowledgeBaseWithMCP(enhancedState)
      case 'create_relationships':
        return await this.createRelationshipsWithMCP(enhancedState)
      case 'generate_insights':
        return await this.generateInsightsWithMCP(enhancedState)
      case 'optimize_context':
        return await this.optimizeContextWithMCP(enhancedState)
      case 'update_temporal_data':
        return await this.updateTemporalDataWithMCP(enhancedState)
      default:
        throw new Error(`Unknown context engine step: ${stepId}`)
    }
  }

  /**
   * Get total steps for progress tracking
   */
  protected getTotalSteps(): number {
    return this.contextSteps.length
  }

  /**
   * Get relevant documentation for context engine
   */
  protected async getRelevantDocumentation(): Promise<Record<string, any>> {
    return {
      contextEngine: 'Context engine architecture and deep codebase understanding',
      dependencyAnalysis: 'Dependency graph building and relationship mapping',
      patternRecognition: 'Code pattern detection and architectural analysis',
      semanticSearch: 'Semantic search and intelligent code retrieval',
      knowledgeExtraction: 'Knowledge base building and inference systems',
      temporalTracking: 'Temporal graph capabilities and change tracking'
    }
  }

  // ============================================================================
  // STEP IMPLEMENTATIONS WITH MCP ENHANCEMENT
  // ============================================================================

  private async ingestCodebaseWithMCP(state: any, input: ContextEngineInput): Promise<any> {
    const ingestion = await aiService.ingestCodebase(
      input.codebase,
      input.task.scope
    )

    this.state!.results.ingestion = ingestion
    
    return {
      results: ingestion,
      needsInput: false,
      completed: false
    }
  }

  private async analyzeStructureWithMCP(state: any): Promise<any> {
    const ingestion = this.state!.results.ingestion
    
    const structure = await aiService.analyzeCodebaseStructure(ingestion)

    this.state!.results.structure = structure
    
    return {
      results: structure,
      needsInput: false,
      completed: false
    }
  }

  private async buildDependencyGraphWithMCP(state: any): Promise<any> {
    const structure = this.state!.results.structure
    
    const graph = await aiService.buildDependencyGraph(structure)

    this.state!.results.graph = graph
    
    return {
      results: graph,
      needsInput: false,
      completed: false
    }
  }

  private async extractPatternsWithMCP(state: any): Promise<any> {
    const structure = this.state!.results.structure
    
    const patterns = await aiService.extractCodePatterns(structure)

    this.state!.results.patterns = patterns
    
    return {
      results: patterns,
      needsInput: false,
      completed: false
    }
  }

  private async buildKnowledgeBaseWithMCP(state: any): Promise<any> {
    const structure = this.state!.results.structure
    const patterns = this.state!.results.patterns
    
    const knowledge = await aiService.buildKnowledgeBase(structure, patterns)

    this.state!.results.knowledge = knowledge
    
    return {
      results: knowledge,
      needsInput: false,
      completed: false
    }
  }

  private async createRelationshipsWithMCP(state: any): Promise<any> {
    const graph = this.state!.results.graph
    const knowledge = this.state!.results.knowledge
    
    const relationships = await aiService.createCodeRelationships(graph, knowledge)

    this.state!.results.relationships = relationships
    
    return {
      results: relationships,
      needsInput: false,
      completed: false
    }
  }

  private async generateInsightsWithMCP(state: any): Promise<any> {
    const allResults = {
      structure: this.state!.results.structure,
      graph: this.state!.results.graph,
      patterns: this.state!.results.patterns,
      knowledge: this.state!.results.knowledge,
      relationships: this.state!.results.relationships
    }
    
    const insights = await aiService.generateContextInsights(allResults)

    this.state!.results.insights = insights
    
    return {
      results: insights,
      needsInput: false,
      completed: false
    }
  }

  private async optimizeContextWithMCP(state: any): Promise<any> {
    const allResults = this.state!.results
    
    const optimization = await aiService.optimizeContext(allResults)

    this.state!.results.optimization = optimization
    
    return {
      results: optimization,
      needsInput: false,
      completed: false
    }
  }

  private async updateTemporalDataWithMCP(state: any): Promise<any> {
    const optimization = this.state!.results.optimization
    
    const temporal = await aiService.updateTemporalData(
      optimization,
      this.state!.input.task
    )

    this.state!.results.temporal = temporal
    
    return {
      results: temporal,
      needsInput: false,
      completed: true // Final step
    }
  }
}

// Export for easy access
export { ContextEngineAgent as default }
