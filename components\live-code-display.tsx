/**
 * AG3NT Platform - Live Code Generation Display
 * 
 * Shows real-time code generation and file creation
 */

'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import {
  FileText,
  Folder,
  Code,
  Database,
  TestTube,
  Eye,
  Download,
  Copy,
  Terminal,
  Zap,
  CheckCircle,
  ExternalLink,
  Play,
  RefreshCw,
  Globe
} from 'lucide-react'
import { useCoding } from '@/hooks/use-coding'
import {
  StreamingUpdate,
  SandboxInfo,
  E2BSandboxInfo,
  APIResponse
} from '@/lib/e2b-schema'

interface GeneratedFile {
  path: string
  content: string
  type: 'component' | 'api' | 'schema' | 'test' | 'config'
  size: number
  timestamp: number
  sandboxId?: string
  isLive?: boolean
}

interface E2BSandbox {
  id: string
  url: string
  status: 'creating' | 'ready' | 'error'
  template: string
  taskId?: string
}

export function LiveCodeDisplay() {
  const coding = useCoding()
  const [selectedFile, setSelectedFile] = useState<GeneratedFile | null>(null)
  const [generatedFiles, setGeneratedFiles] = useState<GeneratedFile[]>([])
  const [sandboxes, setSandboxes] = useState<E2BSandbox[]>([])
  const [streamingUpdates, setStreamingUpdates] = useState<StreamingUpdate[]>([])
  const [activeSandbox, setActiveSandbox] = useState<E2BSandbox | null>(null)

  // Enhanced file generation with E2B integration
  useEffect(() => {
    if (!coding.tasks) return

    const newFiles: GeneratedFile[] = []
    const newSandboxes: E2BSandbox[] = []

    coding.tasks.forEach(task => {
      if (task.status === 'completed' && task.output) {
        // Generate files based on task output
        if (task.output.files) {
          task.output.files.forEach((filePath: string) => {
            const file: GeneratedFile = {
              path: filePath,
              content: generateMockFileContent(filePath, task.type),
              type: getFileType(filePath),
              size: Math.floor(Math.random() * 5000) + 500,
              timestamp: task.endTime || Date.now(),
              sandboxId: task.output.sandboxId,
              isLive: !!task.output.sandboxId
            }
            newFiles.push(file)
          })
        }

        // Track E2B sandboxes
        if (task.output.sandboxId && task.output.sandboxUrl) {
          const sandbox: E2BSandbox = {
            id: task.output.sandboxId,
            url: task.output.sandboxUrl,
            status: 'ready',
            template: 'nextjs-developer',
            taskId: task.id
          }
          newSandboxes.push(sandbox)
        }
      }
    })

    setGeneratedFiles(newFiles)
    setSandboxes(newSandboxes)

    // Set active sandbox to the most recent one
    if (newSandboxes.length > 0 && !activeSandbox) {
      setActiveSandbox(newSandboxes[newSandboxes.length - 1])
    }
  }, [coding.tasks, activeSandbox])

  // Poll for streaming updates with cleanup
  useEffect(() => {
    if (!coding.isRunning) return

    let isMounted = true
    const controller = new AbortController()

    const pollUpdates = async () => {
      try {
        const response = await fetch('/api/coding?action=streaming_updates', {
          signal: controller.signal
        })
        const result = await response.json()

        if (isMounted && result.success && result.data.updates) {
          setStreamingUpdates(result.data.updates)
        }
      } catch (error) {
        if (!controller.signal.aborted && isMounted) {
          console.error('Failed to fetch streaming updates:', error)
        }
      }
    }

    const interval = setInterval(pollUpdates, 1000)

    return () => {
      isMounted = false
      controller.abort()
      clearInterval(interval)
    }
  }, [coding.isRunning])

  const generateMockFileContent = (filePath: string, taskType: string): string => {
    const fileName = filePath.split('/').pop() || ''
    
    if (fileName.endsWith('.tsx') || fileName.endsWith('.jsx')) {
      return `import React from 'react'
import { useState } from 'react'

interface ${fileName.replace('.tsx', '')}Props {
  // Component props
}

export function ${fileName.replace('.tsx', '')}({ }: ${fileName.replace('.tsx', '')}Props) {
  const [state, setState] = useState()

  return (
    <div className="cyberpunk-theme">
      <h1 className="neon-glow">Cyberpunk Calculator</h1>
      {/* Component implementation */}
    </div>
  )
}

export default ${fileName.replace('.tsx', '')}`
    }
    
    if (fileName.endsWith('.ts') && filePath.includes('controller')) {
      return `import { Controller, Get, Post, Body } from '@nestjs/common'
import { CalculatorService } from './calculator.service'

@Controller('api/calculator')
export class CalculatorController {
  constructor(private readonly calculatorService: CalculatorService) {}

  @Post('calculate')
  async calculate(@Body() data: any) {
    return this.calculatorService.calculate(data)
  }

  @Get('history')
  async getHistory() {
    return this.calculatorService.getHistory()
  }
}`
    }
    
    if (fileName === 'schema.prisma') {
      return `generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id          String   @id @default(cuid())
  email       String   @unique
  calculations Calculation[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model Calculation {
  id        String   @id @default(cuid())
  expression String
  result     Float
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  createdAt DateTime @default(now())
}`
    }
    
    if (fileName.endsWith('.test.ts') || fileName.endsWith('.spec.ts')) {
      return `import { render, screen } from '@testing-library/react'
import { Calculator } from './Calculator'

describe('Calculator Component', () => {
  test('renders calculator interface', () => {
    render(<Calculator />)
    expect(screen.getByText('Cyberpunk Calculator')).toBeInTheDocument()
  })

  test('performs basic calculations', () => {
    render(<Calculator />)
    // Test implementation
  })
})`
    }
    
    return `// Generated file: ${fileName}
// Task type: ${taskType}
// Timestamp: ${new Date().toISOString()}

export default {
  // File content generated by AG3NT Framework
}`
  }

  const getFileType = (filePath: string): GeneratedFile['type'] => {
    if (filePath.includes('component') || filePath.endsWith('.tsx')) return 'component'
    if (filePath.includes('api') || filePath.includes('controller')) return 'api'
    if (filePath.includes('schema') || filePath.includes('prisma')) return 'schema'
    if (filePath.includes('test') || filePath.includes('spec')) return 'test'
    return 'config'
  }

  const getFileIcon = (type: GeneratedFile['type']) => {
    switch (type) {
      case 'component': return <Code className="h-4 w-4 text-blue-500" />
      case 'api': return <Zap className="h-4 w-4 text-orange-500" />
      case 'schema': return <Database className="h-4 w-4 text-green-500" />
      case 'test': return <TestTube className="h-4 w-4 text-purple-500" />
      default: return <FileText className="h-4 w-4 text-gray-500" />
    }
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return `${bytes} B`
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`
  }

  const openSandbox = (sandbox: E2BSandbox) => {
    if (sandbox.url) {
      window.open(sandbox.url, '_blank')
    }
  }

  const refreshSandbox = async (sandboxId: string) => {
    try {
      const response = await fetch(`/api/sandbox/${sandboxId}/refresh`, {
        method: 'POST'
      })
      const result = await response.json()

      if (result.success) {
        setSandboxes(prev => prev.map(s =>
          s.id === sandboxId ? { ...s, status: 'ready' } : s
        ))
      }
    } catch (error) {
      console.error('Failed to refresh sandbox:', error)
    }
  }

  const getSandboxStatus = (sandbox: E2BSandbox) => {
    switch (sandbox.status) {
      case 'creating':
        return { color: 'yellow', text: 'Creating...' }
      case 'ready':
        return { color: 'green', text: 'Ready' }
      case 'error':
        return { color: 'red', text: 'Error' }
      default:
        return { color: 'gray', text: 'Unknown' }
    }
  }

  const copyToClipboard = (content: string) => {
    navigator.clipboard.writeText(content)
  }

  if (!coding.isRunning && generatedFiles.length === 0) {
    return null
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Terminal className="h-5 w-5 text-green-500" />
            <CardTitle className="text-lg">Live Code Generation</CardTitle>
          </div>
          <Badge variant="outline">
            {generatedFiles.length} files generated
          </Badge>
        </div>
        <CardDescription>
          Real-time view of code being generated by AI agents
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Tabs defaultValue="files" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="files">Files ({generatedFiles.length})</TabsTrigger>
            <TabsTrigger value="preview">Code</TabsTrigger>
            <TabsTrigger value="sandbox">Live Preview ({sandboxes.length})</TabsTrigger>
            <TabsTrigger value="updates">Updates</TabsTrigger>
          </TabsList>

          <TabsContent value="files" className="space-y-4">
            <ScrollArea className="h-64">
              <div className="space-y-2">
                {generatedFiles.map((file, index) => (
                  <div 
                    key={index}
                    className="flex items-center justify-between p-2 rounded-lg border hover:bg-muted/50 cursor-pointer"
                    onClick={() => setSelectedFile(file)}
                  >
                    <div className="flex items-center space-x-3">
                      {getFileIcon(file.type)}
                      <div>
                        <div className="text-sm font-medium">{file.path.split('/').pop()}</div>
                        <div className="text-xs text-muted-foreground">{file.path}</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-xs text-muted-foreground">{formatFileSize(file.size)}</div>
                      <div className="text-xs text-green-600">
                        <CheckCircle className="h-3 w-3 inline mr-1" />
                        Generated
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="structure" className="space-y-4">
            <div className="text-sm font-mono bg-muted p-4 rounded-lg">
              <div className="space-y-1">
                <div className="flex items-center space-x-2">
                  <Folder className="h-4 w-4 text-blue-500" />
                  <span>cyberpunk-calculator/</span>
                </div>
                <div className="ml-6 space-y-1">
                  <div>📁 src/</div>
                  <div className="ml-4">📁 components/</div>
                  <div className="ml-8">📄 Calculator.tsx</div>
                  <div className="ml-8">📄 Display.tsx</div>
                  <div className="ml-8">📄 Button.tsx</div>
                  <div className="ml-4">📁 api/</div>
                  <div className="ml-8">📄 calculator.controller.ts</div>
                  <div className="ml-8">📄 calculator.service.ts</div>
                  <div className="ml-4">📁 prisma/</div>
                  <div className="ml-8">📄 schema.prisma</div>
                  <div className="ml-4">📁 tests/</div>
                  <div className="ml-8">📄 calculator.test.ts</div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="preview" className="space-y-4">
            {selectedFile ? (
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {getFileIcon(selectedFile.type)}
                    <span className="font-medium">{selectedFile.path}</span>
                  </div>
                  <div className="flex space-x-2">
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => copyToClipboard(selectedFile.content)}
                    >
                      <Copy className="h-4 w-4 mr-1" />
                      Copy
                    </Button>
                    <Button size="sm" variant="outline">
                      <Download className="h-4 w-4 mr-1" />
                      Download
                    </Button>
                  </div>
                </div>
                
                <ScrollArea className="h-64">
                  <pre className="text-xs bg-muted p-4 rounded-lg overflow-x-auto">
                    <code>{selectedFile.content}</code>
                  </pre>
                </ScrollArea>
              </div>
            ) : (
              <div className="text-center text-muted-foreground py-8">
                <Eye className="h-8 w-8 mx-auto mb-2" />
                <p>Select a file to preview its content</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="sandbox" className="space-y-4">
            {sandboxes.length > 0 ? (
              <div className="space-y-4">
                {/* Sandbox Selection */}
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium">Live Sandboxes</h3>
                  <Badge variant="outline">{sandboxes.length} active</Badge>
                </div>

                {/* Sandbox List */}
                <div className="space-y-2">
                  {sandboxes.map((sandbox) => {
                    const status = getSandboxStatus(sandbox)
                    return (
                      <div
                        key={sandbox.id}
                        className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                          activeSandbox?.id === sandbox.id
                            ? 'border-green-500 bg-green-50 dark:bg-green-950'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => setActiveSandbox(sandbox)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <Globe className="h-4 w-4 text-blue-500" />
                            <div>
                              <div className="text-sm font-medium">
                                {sandbox.template} - {sandbox.id.slice(0, 8)}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                Task: {sandbox.taskId}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge
                              variant="outline"
                              className={`text-${status.color}-600 border-${status.color}-200`}
                            >
                              {status.text}
                            </Badge>
                            <div className="flex space-x-1">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  refreshSandbox(sandbox.id)
                                }}
                              >
                                <RefreshCw className="h-3 w-3" />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  openSandbox(sandbox)
                                }}
                              >
                                <ExternalLink className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>

                {/* Active Sandbox Preview */}
                {activeSandbox && activeSandbox.status === 'ready' && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-medium">Live Preview</h4>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => openSandbox(activeSandbox)}
                      >
                        <ExternalLink className="h-3 w-3 mr-1" />
                        Open in New Tab
                      </Button>
                    </div>
                    <div className="border rounded-lg overflow-hidden">
                      <iframe
                        src={activeSandbox.url}
                        className="w-full h-64"
                        title="Live Preview"
                        sandbox="allow-scripts allow-same-origin"
                      />
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center text-muted-foreground py-8">
                <Globe className="h-8 w-8 mx-auto mb-2" />
                <p>No live sandboxes available</p>
                <p className="text-xs mt-1">Sandboxes will appear when frontend tasks complete</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="updates" className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium">Streaming Updates</h3>
              <Badge variant="outline">{streamingUpdates.length} updates</Badge>
            </div>

            <ScrollArea className="h-64">
              <div className="space-y-2">
                {streamingUpdates.length > 0 ? (
                  streamingUpdates.slice().reverse().map((update, index) => (
                    <div
                      key={index}
                      className="p-2 rounded-lg border-l-4 border-l-blue-500 bg-muted/50"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          {update.type === 'file' && <FileText className="h-3 w-3 text-blue-500" />}
                          {update.type === 'generation' && <Zap className="h-3 w-3 text-yellow-500" />}
                          {update.type === 'completion' && <CheckCircle className="h-3 w-3 text-green-500" />}
                          {update.type === 'error' && <Terminal className="h-3 w-3 text-red-500" />}
                          <span className="text-xs font-medium capitalize">{update.type}</span>
                        </div>
                        <span className="text-xs text-muted-foreground">
                          {new Date(update.timestamp).toLocaleTimeString()}
                        </span>
                      </div>
                      <p className="text-xs mt-1">{update.message}</p>
                      {update.fileName && (
                        <p className="text-xs text-muted-foreground mt-1">
                          File: {update.fileName}
                        </p>
                      )}
                    </div>
                  ))
                ) : (
                  <div className="text-center text-muted-foreground py-8">
                    <Terminal className="h-8 w-8 mx-auto mb-2" />
                    <p>No updates yet</p>
                    <p className="text-xs mt-1">Real-time updates will appear here</p>
                  </div>
                )}
              </div>
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

export default LiveCodeDisplay
