#!/usr/bin/env node

/**
 * Test Isolated Framework
 */

console.log('🧪 Testing Isolated AG3NT Framework...')

async function testFramework() {
  try {
    // Test isolated framework service
    const { isolatedFrameworkService } = require('./lib/framework-service-isolated.ts')
    
    console.log('📦 Isolated framework service imported successfully')
    
    // Test initialization
    await isolatedFrameworkService.initialize()
    console.log('✅ Framework initialized')
    
    // Test status
    const status = isolatedFrameworkService.getStatus()
    console.log('📊 Status:', status)
    
    // Test analytics
    const analytics = isolatedFrameworkService.getAnalytics()
    console.log('📈 Analytics:', {
      totalProjects: analytics.totalProjects,
      successRate: analytics.successRate,
      agentCount: Object.keys(analytics.agentUtilization).length
    })
    
    // Test agents
    const agents = isolatedFrameworkService.getAvailableAgents()
    console.log('🤖 Agents:', agents.length, 'available')
    
    // Test project planning
    const planResult = await isolatedFrameworkService.planProject({
      projectName: 'Test App',
      projectDescription: 'A test application',
      projectType: 'web-application',
      features: ['auth', 'database', 'api']
    })
    
    console.log('🎯 Planning result:', {
      success: planResult.success,
      executionTime: planResult.executionTime,
      agentsUsed: planResult.agentsUsed?.length
    })
    
    console.log('🎉 All tests passed!')
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

testFramework()
