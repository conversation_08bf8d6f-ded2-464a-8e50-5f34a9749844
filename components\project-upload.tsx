"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Upload, X } from "lucide-react"
import ProjectPlanUpload from "@/components/project-plan-upload"
import { ProjectPlan } from "@/lib/project-plan-schema"

interface ProjectUploadProps {
  showUploadMode: boolean
  setShowUploadMode: (show: boolean) => void
  onPlanUploaded: (plan: ProjectPlan) => void
}

export function ProjectUpload({
  showUploadMode,
  setShowUploadMode,
  onPlanUploaded,
}: ProjectUploadProps) {
  if (!showUploadMode) {
    return (
      <div className="flex justify-center mb-6">
        <Button
          onClick={() => setShowUploadMode(true)}
          variant="outline"
          className="bg-[#1A1A1A] border-gray-600 text-white hover:bg-[#2A2A2A] flex items-center gap-2"
        >
          <Upload className="w-4 h-4" />
          Upload Project Plan
        </Button>
      </div>
    )
  }

  return (
    <Card className="bg-[#1A1A1A] border-gray-700 mb-6">
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white">Upload Project Plan</h3>
          <Button
            onClick={() => setShowUploadMode(false)}
            variant="ghost"
            size="sm"
            className="text-gray-400 hover:text-white"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>
        <ProjectPlanUpload onPlanUploaded={onPlanUploaded} />
      </CardContent>
    </Card>
  )
}
