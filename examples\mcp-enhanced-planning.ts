/**
 * Example: MCP-Enhanced Planning Agent
 * Demonstrates how the planning agent uses MCP for real-time data access
 */

import { UnifiedContextEngine } from "../lib/unified-context-engine-v2"
import { PlanningGraph } from "../lib/planning-graph"
import { MCPIntegration } from "../lib/mcp-integration"

/**
 * MCP-Enhanced Planning Agent Example
 */
export class MCPEnhancedPlanningAgent {
  private contextEngine: UnifiedContextEngine
  private planningGraph: PlanningGraph
  private mcpIntegration: MCPIntegration

  constructor() {
    this.mcpIntegration = new MCPIntegration()
    this.planningGraph = new PlanningGraph()
  }

  /**
   * Plan a project with real-time MCP data
   */
  async planProject(prompt: string, options: any = {}): Promise<any> {
    console.log('🚀 Starting MCP-Enhanced Planning...')

    // Initialize planning with MCP capabilities
    const planningResult = await this.planningGraph.execute({
      prompt,
      isInteractive: options.isInteractive || false,
      userAnswers: options.userAnswers || {}
    })

    // Demonstrate MCP capabilities
    await this.demonstrateMCPCapabilities(planningResult)

    return planningResult
  }

  /**
   * Demonstrate MCP capabilities during planning
   */
  private async demonstrateMCPCapabilities(planningResult: any): Promise<void> {
    console.log('\n📡 MCP Real-Time Data Access:')

    // 1. Real-time tech stack validation
    if (planningResult.results?.techstack) {
      console.log('\n🔧 Tech Stack Validation:')
      try {
        const compatibility = await this.mcpIntegration.executeTool(
          'tech-stack-server',
          'get_tech_compatibility',
          {
            frontend: planningResult.results.techstack.frontend,
            backend: planningResult.results.techstack.backend,
            database: planningResult.results.techstack.database
          }
        )
        
        console.log(`  ✅ Compatibility Score: ${compatibility.compatibility_score}`)
        console.log(`  📋 Recommendations: ${compatibility.recommendations.join(', ')}`)
      } catch (error) {
        console.log(`  ❌ Tech validation failed: ${error}`)
      }
    }

    // 2. Real-time security advisories
    if (planningResult.results?.techstack) {
      console.log('\n🔒 Security Advisory Check:')
      try {
        const technologies = Object.values(planningResult.results.techstack).filter(Boolean)
        const advisories = await this.mcpIntegration.executeTool(
          'tech-stack-server',
          'check_security_advisories',
          { technologies }
        )
        
        console.log(`  🛡️ Security Score: ${advisories.security_score}`)
        console.log(`  📊 Advisories: ${advisories.advisories.length} found`)
      } catch (error) {
        console.log(`  ❌ Security check failed: ${error}`)
      }
    }

    // 3. Industry standards lookup
    const projectType = planningResult.results?.analyze?.projectType
    if (projectType) {
      console.log('\n📋 Industry Standards:')
      try {
        const domain = this.detectDomain(planningResult.prompt)
        const standards = await this.mcpIntegration.executeTool(
          'best-practices-server',
          'get_industry_standards',
          { domain, projectType }
        )
        
        console.log(`  📏 Standards for ${domain}:`)
        Object.entries(standards.standards || {}).forEach(([key, value]: [string, any]) => {
          if (Array.isArray(value)) {
            console.log(`    ${key}: ${value.join(', ')}`)
          }
        })
      } catch (error) {
        console.log(`  ❌ Standards lookup failed: ${error}`)
      }
    }

    // 4. Team preferences
    console.log('\n👥 Team Preferences:')
    try {
      const teamPrefs = await this.mcpIntegration.executeTool(
        'project-context-server',
        'get_team_preferences',
        { teamId: process.env.TEAM_ID || 'demo-team' }
      )
      
      console.log('  🎯 Team Preferences:')
      Object.entries(teamPrefs.preferences || {}).forEach(([key, value]) => {
        console.log(`    ${key}: ${value}`)
      })
    } catch (error) {
      console.log(`  ❌ Team preferences lookup failed: ${error}`)
    }

    // 5. Compliance requirements
    console.log('\n⚖️ Compliance Requirements:')
    try {
      const domain = this.detectDomain(planningResult.prompt)
      const compliance = await this.mcpIntegration.executeTool(
        'best-practices-server',
        'get_compliance_requirements',
        { domain, region: 'global' }
      )
      
      console.log(`  📜 Requirements: ${compliance.requirements.join(', ')}`)
      console.log(`  🌍 Regulations: ${compliance.regulations.join(', ')}`)
    } catch (error) {
      console.log(`  ❌ Compliance lookup failed: ${error}`)
    }

    // 6. MCP Statistics
    console.log('\n📊 MCP Statistics:')
    const stats = this.mcpIntegration.getCacheStats()
    console.log(`  💾 Cache Size: ${stats.size} entries`)
    console.log(`  🖥️ Servers: ${stats.servers} registered`)
    console.log(`  🔗 Connections: ${stats.active_connections} active`)
  }

  /**
   * Simple domain detection
   */
  private detectDomain(prompt: string): string {
    const lowerPrompt = prompt.toLowerCase()
    if (lowerPrompt.includes('ecommerce') || lowerPrompt.includes('shop')) return 'ecommerce'
    if (lowerPrompt.includes('health') || lowerPrompt.includes('medical')) return 'healthcare'
    if (lowerPrompt.includes('finance') || lowerPrompt.includes('bank')) return 'fintech'
    return 'general'
  }

  /**
   * Get available MCP tools
   */
  getAvailableMCPTools(): any {
    return this.mcpIntegration.getAvailableTools()
  }

  /**
   * Execute a specific MCP tool
   */
  async executeMCPTool(serverName: string, toolName: string, params: any): Promise<any> {
    return await this.mcpIntegration.executeTool(serverName, toolName, params)
  }
}

/**
 * Example usage of MCP-Enhanced Planning Agent
 */
export async function demonstrateMCPPlanning(): Promise<void> {
  console.log('=== MCP-Enhanced Planning Agent Demo ===\n')

  const agent = new MCPEnhancedPlanningAgent()

  // Example 1: E-commerce project
  console.log('📦 Planning E-commerce Project:')
  const ecommerceResult = await agent.planProject(
    "Build an e-commerce platform with user authentication, product catalog, shopping cart, and payment processing",
    { isInteractive: false }
  )

  console.log('\n' + '='.repeat(50) + '\n')

  // Example 2: Healthcare project
  console.log('🏥 Planning Healthcare Project:')
  const healthcareResult = await agent.planProject(
    "Create a patient management system with appointment scheduling, medical records, and HIPAA compliance",
    { isInteractive: false }
  )

  console.log('\n' + '='.repeat(50) + '\n')

  // Example 3: Show available MCP tools
  console.log('🛠️ Available MCP Tools:')
  const tools = agent.getAvailableMCPTools()
  tools.forEach((server: any) => {
    console.log(`\n📡 Server: ${server.serverName}`)
    server.tools.forEach((tool: any) => {
      console.log(`  🔧 ${tool.name}: ${tool.description}`)
    })
  })

  console.log('\n=== Demo Complete ===')
}

/**
 * Example: Custom MCP Server Integration
 */
export class CustomMCPServer {
  /**
   * Example of how to add a custom MCP server for specific needs
   */
  static createCustomServer(): any {
    return {
      name: 'custom-domain-server',
      version: '1.0.0',
      capabilities: { tools: true, resources: true },
      tools: [
        {
          name: 'get_company_standards',
          description: 'Get company-specific coding and architecture standards',
          inputSchema: {
            type: 'object',
            properties: {
              company: { type: 'string' },
              department: { type: 'string' }
            },
            required: ['company']
          },
          handler: async (params: any) => {
            // Custom implementation for company standards
            return {
              standards: {
                coding: 'TypeScript with strict mode',
                testing: 'Jest with 80% coverage minimum',
                deployment: 'Docker with Kubernetes',
                monitoring: 'Datadog APM required'
              },
              last_updated: new Date().toISOString()
            }
          }
        },
        {
          name: 'validate_against_existing_systems',
          description: 'Validate new project against existing company systems',
          inputSchema: {
            type: 'object',
            properties: {
              projectType: { type: 'string' },
              integrations: { type: 'array', items: { type: 'string' } }
            },
            required: ['projectType']
          },
          handler: async (params: any) => {
            // Custom validation logic
            return {
              compatible: true,
              integration_points: ['SSO', 'API Gateway', 'Shared Database'],
              recommendations: [
                'Use existing authentication service',
                'Follow API versioning standards',
                'Implement standard logging format'
              ]
            }
          }
        }
      ],
      resources: [
        {
          uri: 'company://architecture-standards',
          name: 'Company Architecture Standards',
          description: 'Internal architecture and coding standards'
        }
      ]
    }
  }
}

// Example usage:
// demonstrateMCPPlanning().catch(console.error)
