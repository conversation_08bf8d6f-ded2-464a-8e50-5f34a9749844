/**
 * AG3NT Framework - Base Agent Class
 * 
 * Extracted from sophisticated patterns in planning-graph.ts, context-engine.ts, and ai-service.ts
 * Provides the foundation for all AG3NT agents with:
 * - MCP-enhanced execution
 * - Sequential thinking capabilities
 * - Context management and propagation
 * - State lifecycle management
 * - Error handling and retry mechanisms
 * - Agent registration and coordination
 */

import { ChatOpenAI } from "@langchain/openai"
import { HumanMessage, SystemMessage } from "@langchain/core/messages"
import { EventEmitter } from "events"

// Core interfaces extracted from planning patterns
export interface AgentState {
  agentId: string
  agentType: string
  sessionId: string
  input: any
  context: Record<string, any>
  currentStep: string
  stepHistory: string[]
  results: Record<string, any>
  needsInput?: boolean
  question?: AgentQuestion
  completed: boolean
  error?: string
  metadata: AgentMetadata
}

export interface AgentQuestion {
  id: string
  question: string
  type: 'text' | 'select' | 'multiselect' | 'boolean'
  options?: string[]
  placeholder?: string
  required?: boolean
  validation?: string
}

export interface AgentMetadata {
  startTime: string
  lastUpdated: string
  totalSteps: number
  completedSteps: number
  estimatedDuration?: number
  priority: 'low' | 'medium' | 'high' | 'critical'
  tags: string[]
}

export interface AgentCapabilities {
  requiredCapabilities: string[]
  contextFilters: string[]
  mcpEnhanced: boolean
  sequentialThinking: boolean
  contextEnrichment: boolean
  ragIntegration: boolean
}

export interface AgentConfig {
  agentType: string
  capabilities: AgentCapabilities
  model?: string
  temperature?: number
  maxTokens?: number
  timeout?: number
  maxRetries?: number
}

export interface MCPContext {
  enhancedContext?: any
  sequentialThought?: any
  relevantDocs?: Record<string, any>
  enrichments?: any[]
  step?: string
}

/**
 * Base Agent Class - Foundation for all AG3NT agents
 * Extracted from PlanningGraph patterns and generalized
 */
export abstract class BaseAgent extends EventEmitter {
  protected agentId: string
  protected agentType: string
  protected config: AgentConfig
  protected model: ChatOpenAI | null = null
  protected contextEngine: any = null // Will be injected by framework
  protected state: AgentState | null = null
  protected isInitialized: boolean = false

  constructor(agentType: string, config: Partial<AgentConfig> = {}) {
    super()
    
    this.agentId = `${agentType}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    this.agentType = agentType
    this.config = {
      agentType,
      capabilities: {
        requiredCapabilities: [],
        contextFilters: ['all'],
        mcpEnhanced: true,
        sequentialThinking: true,
        contextEnrichment: true,
        ragIntegration: true
      },
      model: 'anthropic/claude-sonnet-4',
      temperature: 0.7,
      maxTokens: 4000,
      timeout: 30000,
      maxRetries: 3,
      ...config
    }
  }

  /**
   * Initialize agent with context engine (injected by framework)
   */
  async initialize(contextEngine: any): Promise<void> {
    if (this.isInitialized) return

    this.contextEngine = contextEngine
    this.ensureModel()
    
    // Register with context engine
    if (this.contextEngine && this.contextEngine.registerAgent) {
      await this.contextEngine.registerAgent(
        this.agentType,
        this.agentId,
        this.config.capabilities
      )
    }

    this.isInitialized = true
    this.emit('agent_initialized', { agentId: this.agentId, agentType: this.agentType })
  }

  /**
   * Execute agent with input - main entry point
   */
  async execute(input: any, options: {
    sessionId?: string
    priority?: 'low' | 'medium' | 'high' | 'critical'
    tags?: string[]
  } = {}): Promise<AgentState> {
    if (!this.isInitialized) {
      throw new Error(`Agent ${this.agentId} not initialized. Call initialize() first.`)
    }

    // Initialize state
    const sessionId = options.sessionId || `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    
    this.state = {
      agentId: this.agentId,
      agentType: this.agentType,
      sessionId,
      input,
      context: {},
      currentStep: 'initialize',
      stepHistory: [],
      results: {},
      completed: false,
      metadata: {
        startTime: new Date().toISOString(),
        lastUpdated: new Date().toISOString(),
        totalSteps: this.getTotalSteps(),
        completedSteps: 0,
        priority: options.priority || 'medium',
        tags: options.tags || []
      }
    }

    this.emit('execution_started', { agentId: this.agentId, sessionId, input })

    try {
      // Execute agent-specific workflow
      const result = await this.executeWorkflow(this.state)
      
      this.state.completed = true
      this.state.metadata.lastUpdated = new Date().toISOString()
      
      this.emit('execution_completed', { agentId: this.agentId, sessionId, result })
      return this.state

    } catch (error) {
      this.state.error = error instanceof Error ? error.message : 'Unknown error'
      this.emit('execution_failed', { agentId: this.agentId, sessionId, error: this.state.error })
      throw error
    }
  }

  /**
   * Execute a single step with MCP enhancement (extracted from planning-graph.ts)
   */
  protected async executeStepWithContext(stepId: string, stepInput?: any): Promise<any> {
    if (!this.state) throw new Error('Agent state not initialized')

    console.log(`🧠 Executing ${stepId} with enhanced context...`)

    this.state.currentStep = stepId
    this.state.stepHistory.push(stepId)

    // 1. Get enhanced context with MCP enrichment (if enabled)
    let mcpContext: MCPContext = {}
    
    if (this.config.capabilities.mcpEnhanced && this.contextEngine) {
      const enhancedContext = await this.contextEngine.enhanceWithRAG(stepId, JSON.stringify(this.state.input))
      mcpContext.enhancedContext = enhancedContext
      mcpContext.enrichments = enhancedContext.enrichments || []
      console.log(`📚 Enhanced context includes ${mcpContext.enrichments.length} enrichments`)
    }

    // 2. Apply sequential thinking (if enabled)
    if (this.config.capabilities.sequentialThinking && this.contextEngine) {
      mcpContext.sequentialThought = await this.contextEngine.performSequentialThinking(
        `Executing ${stepId} for ${this.agentType}: ${JSON.stringify(this.state.input)}`,
        { thoughtNumber: 1, totalThoughts: 3 }
      )
      console.log(`🤔 Sequential thinking applied for ${stepId}`)
    }

    // 3. Get relevant documentation (if RAG enabled)
    if (this.config.capabilities.ragIntegration && this.contextEngine) {
      mcpContext.relevantDocs = await this.getRelevantDocumentation(stepId)
      console.log(`📖 Retrieved documentation for ${Object.keys(mcpContext.relevantDocs || {}).length} technologies`)
    }

    // 4. Execute the step with enhanced context
    const executionContext = {
      stepId,
      stepInput,
      mcpContext,
      agentState: this.state,
      metadata: {
        agentId: this.agentId,
        agentType: this.agentType,
        sessionId: this.state.sessionId,
        timestamp: new Date().toISOString()
      }
    }

    const result = await this.executeWithRetry(
      () => this.executeStep(stepId, executionContext),
      this.config.maxRetries || 3
    )

    // 5. Update context engine with results
    if (this.contextEngine && result && result.results) {
      this.contextEngine.updateContext(stepId, result.results)
    }

    // 6. Update state
    if (result.results) {
      this.state.results[stepId] = result.results
    }
    
    this.state.needsInput = result.needsInput || false
    this.state.question = result.question
    this.state.metadata.completedSteps++
    this.state.metadata.lastUpdated = new Date().toISOString()

    this.emit('step_completed', { 
      agentId: this.agentId, 
      sessionId: this.state.sessionId, 
      stepId, 
      result 
    })

    return result
  }

  /**
   * Execute with retry mechanism (extracted from ai-service.ts patterns)
   */
  protected async executeWithRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> {
    let lastError: Error | null = null

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation()
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error')
        
        if (attempt === maxRetries) {
          break
        }

        console.warn(`Attempt ${attempt} failed, retrying in ${delay}ms...`, lastError.message)
        await new Promise(resolve => setTimeout(resolve, delay))
        delay *= 2 // Exponential backoff
      }
    }

    throw lastError
  }

  /**
   * Ensure model is initialized (extracted from planning-graph.ts)
   */
  protected ensureModel(): void {
    if (!this.model) {
      const apiKey = process.env.OPENROUTER_API_KEY
      if (!apiKey) {
        throw new Error('OPENROUTER_API_KEY environment variable is required')
      }

      this.model = new ChatOpenAI({
        modelName: this.config.model || "anthropic/claude-sonnet-4",
        openAIApiKey: apiKey,
        configuration: {
          baseURL: "https://openrouter.ai/api/v1",
          defaultHeaders: {
            "HTTP-Referer": process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3000",
            "X-Title": "AG3NT Framework",
          },
        },
        temperature: this.config.temperature || 0.7,
      })
    }
  }

  // Abstract methods that must be implemented by concrete agents
  protected abstract executeWorkflow(state: AgentState): Promise<AgentState>
  protected abstract executeStep(stepId: string, context: any): Promise<any>
  protected abstract getTotalSteps(): number
  protected abstract getRelevantDocumentation(stepId: string): Promise<Record<string, any>>

  // Getters
  get id(): string { return this.agentId }
  get type(): string { return this.agentType }
  get currentState(): AgentState | null { return this.state }
  get initialized(): boolean { return this.isInitialized }
}
