/**
 * AG3NT Platform - Project Plan Upload Component
 * 
 * Allows users to upload a project plan JSON and skip directly to coding
 */

'use client'

import React, { useState, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { 
  Upload, 
  FileText, 
  CheckCircle, 
  AlertCircle, 
  Download,
  Code,
  Zap,
  Eye,
  X
} from 'lucide-react'
import {
  validateProjectPlan,
  ProjectPlan,
  isAG3NTPlanningResults,
  extractProjectInfoFromAG3NT
} from '@/lib/project-plan-schema'

interface ProjectPlanUploadProps {
  onPlanUploaded?: (plan: ProjectPlan) => void
  onStartCoding?: () => void
  className?: string
}

export default function ProjectPlanUpload({ 
  onPlanUploaded, 
  onStartCoding,
  className = '' 
}: ProjectPlanUploadProps) {
  const [dragActive, setDragActive] = useState(false)
  const [uploadedPlan, setUploadedPlan] = useState<ProjectPlan | null>(null)
  const [jsonInput, setJsonInput] = useState('')
  const [validationError, setValidationError] = useState<string | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [showSample, setShowSample] = useState(false)
  const [samplePlan, setSamplePlan] = useState<ProjectPlan | null>(null)

  // Handle drag events
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }, [])

  // Handle file drop
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0])
    }
  }, [])

  // Handle file input
  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFile(e.target.files[0])
    }
  }

  // Process uploaded file
  const handleFile = async (file: File) => {
    if (file.type !== 'application/json' && !file.name.endsWith('.json')) {
      setValidationError('Please upload a JSON file')
      return
    }

    try {
      const text = await file.text()
      setJsonInput(text)
      validateAndSetPlan(text)
    } catch (error) {
      setValidationError('Failed to read file')
    }
  }

  // Validate and set project plan
  const validateAndSetPlan = (jsonText: string) => {
    setValidationError(null)
    
    try {
      const parsed = JSON.parse(jsonText)
      const validation = validateProjectPlan(parsed)
      
      if (validation.success) {
        setUploadedPlan(validation.data!)
        if (onPlanUploaded) {
          onPlanUploaded(validation.data!)
        }
      } else {
        setValidationError(validation.errors?.join(', ') || 'Invalid project plan')
        setUploadedPlan(null)
      }
    } catch (error) {
      setValidationError('Invalid JSON format')
      setUploadedPlan(null)
    }
  }

  // Handle JSON input change
  const handleJsonInputChange = (value: string) => {
    setJsonInput(value)
    if (value.trim()) {
      validateAndSetPlan(value)
    } else {
      setUploadedPlan(null)
      setValidationError(null)
    }
  }

  // Start coding workflow
  const handleStartCoding = async () => {
    if (!uploadedPlan) return

    setIsUploading(true)
    try {
      const response = await fetch('/api/project-plan', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(uploadedPlan),
      })

      const result = await response.json()

      if (result.success) {
        if (onStartCoding) {
          onStartCoding()
        }
      } else {
        setValidationError(result.error || 'Failed to start coding workflow')
      }
    } catch (error) {
      setValidationError('Failed to upload project plan')
    } finally {
      setIsUploading(false)
    }
  }

  // Load sample project plan
  const loadSamplePlan = async () => {
    try {
      const response = await fetch('/api/project-plan?action=sample')
      const result = await response.json()
      
      if (result.success) {
        setSamplePlan(result.data)
        setJsonInput(JSON.stringify(result.data, null, 2))
        setUploadedPlan(result.data)
        setShowSample(false)
      }
    } catch (error) {
      setValidationError('Failed to load sample plan')
    }
  }

  // Clear current plan
  const clearPlan = () => {
    setUploadedPlan(null)
    setJsonInput('')
    setValidationError(null)
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Upload className="h-5 w-5 text-blue-500" />
            <CardTitle>Upload Project Plan</CardTitle>
          </div>
          <Badge variant="outline">Skip Planning</Badge>
        </div>
        <CardDescription>
          Upload a project plan JSON to skip the planning phase and go directly to autonomous coding
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Upload Area */}
        {!uploadedPlan && (
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              dragActive 
                ? 'border-blue-500 bg-blue-50 dark:bg-blue-950' 
                : 'border-gray-300 hover:border-gray-400'
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Drop your project plan here</h3>
            <p className="text-muted-foreground mb-4">
              Or click to browse for a JSON file
            </p>
            <div className="flex justify-center space-x-2">
              <Button variant="outline" onClick={() => document.getElementById('file-input')?.click()}>
                <Upload className="h-4 w-4 mr-2" />
                Browse Files
              </Button>
              <Button variant="outline" onClick={() => setShowSample(!showSample)}>
                <Eye className="h-4 w-4 mr-2" />
                View Sample
              </Button>
            </div>
            <input
              id="file-input"
              type="file"
              accept=".json"
              onChange={handleFileInput}
              className="hidden"
            />
          </div>
        )}

        {/* Sample Plan */}
        {showSample && (
          <Card className="border-blue-200 bg-blue-50 dark:bg-blue-950">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm">Sample Project Plan</CardTitle>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setShowSample(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-3">
                Here's an example of a valid project plan structure:
              </p>
              <div className="flex space-x-2">
                <Button size="sm" onClick={loadSamplePlan}>
                  <Download className="h-3 w-3 mr-1" />
                  Use Sample
                </Button>
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => window.open('/api/project-plan?action=sample', '_blank')}
                >
                  <Eye className="h-3 w-3 mr-1" />
                  View JSON
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* JSON Input */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Project Plan JSON</label>
          <Textarea
            placeholder="Paste your project plan JSON here..."
            value={jsonInput}
            onChange={(e) => handleJsonInputChange(e.target.value)}
            className="min-h-[200px] font-mono text-sm"
          />
        </div>

        {/* Validation Error */}
        {validationError && (
          <div className="flex items-center space-x-2 p-3 bg-red-50 dark:bg-red-950 border border-red-200 dark:border-red-800 rounded-lg">
            <AlertCircle className="h-4 w-4 text-red-500" />
            <span className="text-sm text-red-700 dark:text-red-300">{validationError}</span>
          </div>
        )}

        {/* Success State */}
        {uploadedPlan && !validationError && (
          <div className="space-y-4">
            <div className="flex items-center space-x-2 p-3 bg-green-50 dark:bg-green-950 border border-green-200 dark:border-green-800 rounded-lg">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span className="text-sm text-green-700 dark:text-green-300">
                Project plan validated successfully!
              </span>
            </div>

            {/* Plan Summary */}
            <Card className="border-green-200 bg-green-50 dark:bg-green-950">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Project Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {(() => {
                  // Handle both AG3NT and legacy formats
                  if (isAG3NTPlanningResults(uploadedPlan)) {
                    const info = extractProjectInfoFromAG3NT(uploadedPlan)
                    const techStack = uploadedPlan.results.techstack || {}
                    const tasksCount = uploadedPlan.results.tasks?.breakdown?.length ||
                                     (Array.isArray(uploadedPlan.results.tasks) ? uploadedPlan.results.tasks.length : 0)

                    return (
                      <>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="font-medium">Name:</span> {info.projectName}
                          </div>
                          <div>
                            <span className="font-medium">Frontend:</span> {techStack.Frontend || 'Not specified'}
                          </div>
                          <div>
                            <span className="font-medium">Backend:</span> {techStack.Backend || 'Not specified'}
                          </div>
                          <div>
                            <span className="font-medium">Tasks:</span> {tasksCount} tasks
                          </div>
                        </div>
                        <p className="text-xs text-muted-foreground mt-2">
                          {info.projectDescription}
                        </p>
                        <div className="text-xs text-blue-600 dark:text-blue-400 mt-2">
                          ✨ AG3NT Planning Results Format
                        </div>
                      </>
                    )
                  } else {
                    // Legacy format
                    const legacy = uploadedPlan as any
                    return (
                      <>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="font-medium">Name:</span> {legacy.projectName}
                          </div>
                          <div>
                            <span className="font-medium">Frontend:</span> {legacy.techStack?.Frontend || 'Not specified'}
                          </div>
                          <div>
                            <span className="font-medium">Backend:</span> {legacy.techStack?.Backend || 'Not specified'}
                          </div>
                          <div>
                            <span className="font-medium">Tasks:</span> {legacy.tasks?.length || 0} tasks
                          </div>
                        </div>
                        <p className="text-xs text-muted-foreground mt-2">
                          {legacy.projectDescription}
                        </p>
                        <div className="text-xs text-gray-600 dark:text-gray-400 mt-2">
                          📄 Legacy Project Plan Format
                        </div>
                      </>
                    )
                  }
                })()}
              </CardContent>
            </Card>

            {/* Action Buttons */}
            <div className="flex space-x-2">
              <Button 
                onClick={handleStartCoding}
                disabled={isUploading}
                className="flex-1"
              >
                {isUploading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Starting Coding...
                  </>
                ) : (
                  <>
                    <Zap className="h-4 w-4 mr-2" />
                    Start Autonomous Coding
                  </>
                )}
              </Button>
              <Button variant="outline" onClick={clearPlan}>
                <X className="h-4 w-4 mr-2" />
                Clear
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
