/**
 * AG3NT Framework - Discovery & Load Balancing Demonstration
 * 
 * Comprehensive demonstration of agent discovery, intelligent load balancing,
 * and automatic failover capabilities.
 */

import {
  AG3NTFramework,
  createPlanningAgent,
  createTaskPlannerAgent,
  createExecutorAgent,
  createFrontendCoderAgent,
  createBackendCoderAgent,
  createTesterAgent,
  AgentDiscoveryService,
  LoadBalancer,
  FailoverManager
} from '../lib/ag3nt-framework'

async function demonstrateDiscoveryAndLoadBalancing() {
  console.log('🔍 AG3NT Framework - Discovery & Load Balancing Demonstration')
  console.log('=' .repeat(70))

  // Initialize framework with discovery and load balancing enabled
  const framework = new AG3NTFramework({
    contextEngine: {
      enableMCP: true,
      enableSequentialThinking: true,
      enableRAG: true
    },
    coordination: {
      enableTaskDelegation: true,
      enableConsensus: true,
      enableWorkflowHandoffs: true,
      enablePatternRegistry: true
    },
    discovery: {
      enableAgentDiscovery: true,
      enableLoadBalancing: true,
      enableFailover: true,
      discoveryInterval: 10000, // 10 seconds for demo
      healthCheckInterval: 5000, // 5 seconds for demo
      loadBalancingAlgorithm: 'adaptive'
    },
    advancedFeatures: {
      adaptiveLearning: { enabled: true },
      temporalDatabase: { enabled: true },
      collaboration: { enabled: true },
      optimization: { enabled: true },
      monitoring: { enabled: true }
    }
  })

  await framework.initialize()

  // Create multiple instances of different agent types for load balancing
  const agents = [
    // Multiple planning agents
    { agent: createPlanningAgent(), type: 'planning', instance: 1 },
    { agent: createPlanningAgent(), type: 'planning', instance: 2 },
    { agent: createPlanningAgent(), type: 'planning', instance: 3 },
    
    // Multiple task planner agents
    { agent: createTaskPlannerAgent(), type: 'task-planner', instance: 1 },
    { agent: createTaskPlannerAgent(), type: 'task-planner', instance: 2 },
    
    // Multiple executor agents
    { agent: createExecutorAgent(), type: 'executor', instance: 1 },
    { agent: createExecutorAgent(), type: 'executor', instance: 2 },
    { agent: createExecutorAgent(), type: 'executor', instance: 3 },
    { agent: createExecutorAgent(), type: 'executor', instance: 4 },
    
    // Multiple coder agents
    { agent: createFrontendCoderAgent(), type: 'frontend-coder', instance: 1 },
    { agent: createFrontendCoderAgent(), type: 'frontend-coder', instance: 2 },
    { agent: createBackendCoderAgent(), type: 'backend-coder', instance: 1 },
    { agent: createBackendCoderAgent(), type: 'backend-coder', instance: 2 },
    
    // Multiple tester agents
    { agent: createTesterAgent(), type: 'tester', instance: 1 },
    { agent: createTesterAgent(), type: 'tester', instance: 2 }
  ]

  // Register all agents with unique IDs
  for (const { agent, type, instance } of agents) {
    agent.id = `${type}-${instance}`
    agent.type = type
    await framework.registerAgent(agent)
  }

  console.log(`\n🤖 Registered ${agents.length} agents across ${new Set(agents.map(a => a.type)).size} types`)

  // Get discovery service and load balancer
  const discoveryService = framework.getDiscoveryService()!
  const loadBalancer = framework.getLoadBalancer()!
  const failoverManager = framework.getFailoverManager()!

  // Demonstration 1: Agent Discovery
  console.log('\n' + '='.repeat(70))
  console.log('🔍 DEMONSTRATION 1: Agent Discovery')
  console.log('='.repeat(70))

  console.log('\n📊 Discovery Statistics:')
  const discoveryStats = discoveryService.getDiscoveryStats()
  console.log(`  Total agents: ${discoveryStats.totalAgents}`)
  console.log(`  Healthy agents: ${discoveryStats.healthyAgents}`)
  console.log(`  Agent types: ${Object.keys(discoveryStats.agentsByType).join(', ')}`)
  console.log(`  Average load: ${discoveryStats.averageLoad.toFixed(2)}`)

  // Discover specific agent types
  console.log('\n🔍 Discovering planning agents:')
  const planningAgents = await discoveryService.discoverAgents({
    agentType: 'planning'
  })
  console.log(`  Found ${planningAgents.totalFound} planning agents`)
  planningAgents.agents.forEach(agent => {
    console.log(`    - ${agent.agentId}: ${agent.status} (load: ${agent.performance.currentLoad}/${agent.performance.maxLoad})`)
  })

  console.log('\n🔍 Discovering executor agents with low load:')
  const lowLoadExecutors = await discoveryService.discoverAgents({
    agentType: 'executor',
    maxLoad: 5
  })
  console.log(`  Found ${lowLoadExecutors.totalFound} low-load executor agents`)
  lowLoadExecutors.agents.forEach(agent => {
    console.log(`    - ${agent.agentId}: load ${agent.performance.currentLoad}/${agent.performance.maxLoad}`)
  })

  // Demonstration 2: Load Balancing
  console.log('\n' + '='.repeat(70))
  console.log('⚖️ DEMONSTRATION 2: Intelligent Load Balancing')
  console.log('='.repeat(70))

  // Simulate multiple requests to see load balancing in action
  const requests = [
    { requestId: 'req-001', agentType: 'planning', priority: 'high' },
    { requestId: 'req-002', agentType: 'planning', priority: 'medium' },
    { requestId: 'req-003', agentType: 'executor', priority: 'critical' },
    { requestId: 'req-004', agentType: 'executor', priority: 'low' },
    { requestId: 'req-005', agentType: 'frontend-coder', priority: 'high' },
    { requestId: 'req-006', agentType: 'backend-coder', priority: 'medium' },
    { requestId: 'req-007', agentType: 'tester', priority: 'high' },
    { requestId: 'req-008', agentType: 'planning', priority: 'critical' }
  ]

  console.log('\n🚀 Routing requests with adaptive load balancing:')
  for (const request of requests) {
    try {
      const result = await loadBalancer.routeRequest(request)
      console.log(`  ${request.requestId} → ${result.selectedAgent.agentId} (${result.algorithm})`)
      console.log(`    Reasoning: ${result.reasoning.join(', ')}`)
      console.log(`    Selection time: ${result.selectionTime}ms`)
      
      // Simulate agent load increase
      discoveryService.updateAgentPerformance(result.selectedAgent.agentId, {
        currentLoad: result.selectedAgent.performance.currentLoad + 1,
        averageResponseTime: result.selectedAgent.performance.averageResponseTime + Math.random() * 100
      })
    } catch (error) {
      console.error(`  ❌ Failed to route ${request.requestId}:`, error)
    }
  }

  // Show load balancing metrics
  console.log('\n📊 Load Balancing Metrics:')
  const lbMetrics = loadBalancer.getMetrics()
  console.log(`  Total requests: ${lbMetrics.totalRequests}`)
  console.log(`  Successful routes: ${lbMetrics.successfulRoutes}`)
  console.log(`  Success rate: ${((lbMetrics.successfulRoutes / lbMetrics.totalRequests) * 100).toFixed(1)}%`)
  console.log(`  Average selection time: ${lbMetrics.averageSelectionTime.toFixed(2)}ms`)

  // Show load distribution
  console.log('\n📈 Load Distribution:')
  const loadDistribution = loadBalancer.getLoadDistribution()
  console.log(`  Available agents: ${loadDistribution.availableAgents}/${loadDistribution.totalAgents}`)
  console.log(`  Average load: ${(loadDistribution.averageLoad * 100).toFixed(1)}%`)
  console.log(`  Load variance: ${loadDistribution.loadVariance.toFixed(3)}`)
  
  if (loadDistribution.hotspots.length > 0) {
    console.log('  🔥 Load hotspots detected:')
    loadDistribution.hotspots.forEach(hotspot => {
      console.log(`    - ${hotspot.agentId}: ${hotspot.loadPercentage.toFixed(1)}% (${hotspot.severity})`)
      console.log(`      Recommendation: ${hotspot.recommendation}`)
    })
  }

  // Demonstration 3: Failover Management
  console.log('\n' + '='.repeat(70))
  console.log('🛡️ DEMONSTRATION 3: Automatic Failover')
  console.log('='.repeat(70))

  // Test failover for a planning agent
  const targetAgent = 'planning-1'
  console.log(`\n🧪 Testing failover for agent: ${targetAgent}`)

  try {
    // Test failover plan
    const testResult = await failoverManager.testFailoverPlan(targetAgent)
    console.log(`  Test result: ${testResult.success ? 'PASSED' : 'FAILED'}`)
    console.log(`  Test time: ${testResult.failoverTime}ms`)
    
    if (testResult.issues.length > 0) {
      console.log('  Issues found:')
      testResult.issues.forEach(issue => console.log(`    - ${issue}`))
    }
    
    if (testResult.recommendations.length > 0) {
      console.log('  Recommendations:')
      testResult.recommendations.forEach(rec => console.log(`    - ${rec}`))
    }

    // Simulate agent failure and trigger failover
    console.log(`\n🚨 Simulating failure of agent: ${targetAgent}`)
    discoveryService.updateAgentStatus(targetAgent, 'unhealthy')
    
    // Wait a moment for auto-failover to trigger
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Simulate agent recovery
    console.log(`\n🔄 Simulating recovery of agent: ${targetAgent}`)
    discoveryService.updateAgentStatus(targetAgent, 'healthy')
    
    await failoverManager.recoverPrimaryAgent(targetAgent)

  } catch (error) {
    console.error('Failover demonstration error:', error)
  }

  // Show failover metrics
  console.log('\n📊 Failover Metrics:')
  const failoverMetrics = failoverManager.getFailoverMetrics()
  console.log(`  Total failovers: ${failoverMetrics.totalFailovers}`)
  console.log(`  Successful failovers: ${failoverMetrics.successfulFailovers}`)
  console.log(`  Success rate: ${((failoverMetrics.successfulFailovers / Math.max(failoverMetrics.totalFailovers, 1)) * 100).toFixed(1)}%`)
  console.log(`  Average failover time: ${failoverMetrics.averageFailoverTime.toFixed(2)}ms`)

  // Demonstration 4: Advanced Discovery Features
  console.log('\n' + '='.repeat(70))
  console.log('🎯 DEMONSTRATION 4: Advanced Discovery Features')
  console.log('='.repeat(70))

  // Find best agent for specific task
  console.log('\n🎯 Finding best agent for critical planning task:')
  const bestPlanningAgent = await discoveryService.findBestAgent({
    agentType: 'planning',
    minProficiency: 0.8,
    maxLoad: 8
  })

  if (bestPlanningAgent) {
    console.log(`  Selected: ${bestPlanningAgent.agentId}`)
    console.log(`  Load: ${bestPlanningAgent.performance.currentLoad}/${bestPlanningAgent.performance.maxLoad}`)
    console.log(`  Success rate: ${(bestPlanningAgent.performance.successRate * 100).toFixed(1)}%`)
    console.log(`  Response time: ${bestPlanningAgent.performance.averageResponseTime}ms`)
  } else {
    console.log('  No suitable agent found')
  }

  // Show comprehensive analytics
  console.log('\n📊 Comprehensive Discovery Analytics:')
  const discoveryAnalytics = framework.getDiscoveryAnalytics()
  
  console.log('\n  Discovery Service:')
  console.log(`    Total agents: ${discoveryAnalytics.discovery?.totalAgents || 0}`)
  console.log(`    Healthy agents: ${discoveryAnalytics.discovery?.healthyAgents || 0}`)
  console.log(`    Average load: ${(discoveryAnalytics.discovery?.averageLoad || 0).toFixed(2)}`)
  
  console.log('\n  Load Balancing:')
  console.log(`    Total requests: ${discoveryAnalytics.loadBalancing?.totalRequests || 0}`)
  console.log(`    Success rate: ${((discoveryAnalytics.loadBalancing?.successfulRoutes || 0) / Math.max(discoveryAnalytics.loadBalancing?.totalRequests || 1, 1) * 100).toFixed(1)}%`)
  console.log(`    Circuit breaker trips: ${discoveryAnalytics.loadBalancing?.circuitBreakerTrips || 0}`)
  
  console.log('\n  Failover Management:')
  console.log(`    Total failovers: ${discoveryAnalytics.failover?.totalFailovers || 0}`)
  console.log(`    MTBF: ${((discoveryAnalytics.failover?.mtbf || 0) / 1000).toFixed(1)}s`)
  console.log(`    MTTR: ${((discoveryAnalytics.failover?.mttr || 0) / 1000).toFixed(1)}s`)

  // Cleanup
  console.log('\n' + '='.repeat(70))
  console.log('🧹 CLEANUP')
  console.log('='.repeat(70))

  await framework.shutdown()

  console.log('✅ All systems shutdown successfully')
  console.log('\n🎉 Discovery & Load Balancing Demonstration Complete!')
  console.log('=' .repeat(70))
}

// Run demonstration
if (require.main === module) {
  demonstrateDiscoveryAndLoadBalancing().catch(console.error)
}

export { demonstrateDiscoveryAndLoadBalancing }
