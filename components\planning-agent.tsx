"use client"

import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { motion } from "framer-motion"
import { Settings } from "lucide-react"
import Image from "next/image"
import Globe from "@/components/Globe"
import E2BPreview from "@/components/e2b-preview"
import { ResultsView } from "@/components/results-view"

// Custom hooks
import { usePlanningState } from "@/hooks/use-planning-state"
import { useCodingState } from "@/hooks/use-coding-state"
import { useSettingsState } from "@/hooks/use-settings-state"
import { useChatState } from "@/hooks/use-chat-state"
import { useUploadState } from "@/hooks/use-upload-state"

// Components
import { SettingsModal } from "@/components/settings-modal"
import { ProjectUpload } from "@/components/project-upload"
import { ChatSidebar } from "@/components/chat-sidebar"
import { PlanningWorkflow } from "@/components/planning-workflow"

// Framework integration
import { useFramework } from "@/hooks/use-framework"
import { useCoding } from "@/hooks/use-coding"
import { ProjectPlan } from "@/lib/project-plan-schema"

export function PlanningAgent() {
  // Framework integration
  const framework = useFramework()
  const coding = useCoding()
  const { generatedFiles, streamingUpdates: codingStreamingUpdates } = coding

  // Custom hooks for state management
  const planningState = usePlanningState()
  const codingState = useCodingState()
  const settingsState = useSettingsState()
  const chatState = useChatState()
  const uploadState = useUploadState()

  // UI state
  const [activeTab, setActiveTab] = useState<"preview" | "code" | "planning" | "graph">("preview")

  // Refs
  const taskListRef = useRef<HTMLDivElement>(null)

  // Sync coding state with framework
  useEffect(() => {
    if (coding.progress?.sandboxes) {
      codingState.setE2bSandboxes(coding.progress.sandboxes)
      if (!codingState.activeSandbox && coding.progress.sandboxes.length > 0) {
        codingState.setActiveSandbox(coding.progress.sandboxes[coding.progress.sandboxes.length - 1])
      }
    }

    if (codingStreamingUpdates) {
      codingState.setStreamingUpdates(codingStreamingUpdates)
    }
  }, [coding.progress, codingState.activeSandbox, codingStreamingUpdates])

  // Handle project plan upload
  const handlePlanUploaded = (plan: ProjectPlan) => {
    uploadState.setUploadedPlan(plan)
    console.log('📋 Project plan uploaded:', plan)

    // Immediately transition to main interface
    planningState.setHasStarted(true)
    uploadState.setShowUploadMode(false)

    // Add upload message to chat
    chatState.addChatMessage({
      type: 'ai',
      content: `🚀 **Project Plan Uploaded!**

Starting autonomous coding workflow for "${plan}":

• **Tech Stack**: Frontend + Backend
• **Tasks**: coding tasks identified
• **E2B Integration**: Live preview will be available once frontend tasks complete

Skipping planning phase and going directly to code generation!`,
    })

    // Start coding workflow immediately
    handleStartCodingFromUpload(plan)
  }

  const handleStartCodingFromUpload = async (plan: ProjectPlan) => {
    try {
      console.log("🚀 Starting coding workflow from uploaded plan...")
      codingState.setIsCoding(true)

      // Generate coding tasks from the uploaded plan
      const tasks = generateCodingTasks(plan)
      codingState.setCodingTasks(tasks)

      // Start the coding workflow
      const result = await coding.startCoding(plan)
      console.log("Coding workflow started:", result)
    } catch (error) {
      console.error("Failed to start coding workflow:", error)
      planningState.setError("Failed to start coding workflow. Please try again.")
    }
  }

  const generateCodingTasks = (plan: any) => {
    // This should be based on the actual plan structure
    return [
      { id: 'setup', title: 'Initialize project structure', status: 'pending' },
      { id: 'frontend', title: 'Generate frontend components', status: 'pending' },
      { id: 'backend', title: 'Create backend API', status: 'pending' },
      { id: 'database', title: 'Setup database schema', status: 'pending' },
      { id: 'integration', title: 'Integrate components', status: 'pending' },
    ]
  }

  const handlePlanningComplete = () => {
    // Auto-start coding workflow when planning completes
    console.log("Planning completed, starting coding workflow...")
    // Implementation would go here
  }

  // Show upload mode if enabled
  if (uploadState.showUploadMode) {
    return (
      <div className="min-h-screen bg-[#000000] text-white flex items-center justify-center p-4">
        <SettingsModal
          isOpen={settingsState.isSettingsOpen}
          onOpenChange={settingsState.setIsSettingsOpen}
          userApiKey={settingsState.userApiKey}
          setUserApiKey={settingsState.setUserApiKey}
          preferredModel={settingsState.preferredModel}
          setPreferredModel={settingsState.setPreferredModel}
          isAutonomousMode={settingsState.isAutonomousMode}
          setIsAutonomousMode={settingsState.setIsAutonomousMode}
        />

        <div className="fixed bottom-6 right-6 text-xs text-gray-500 font-medium z-50">
          Powered by{' '}
          <span className="text-white font-semibold">AP3</span>
          <span className="text-[#ff2d55] font-semibold" style={{ textShadow: '0 0 4px #ff2d55, 0 0 8px #ff2d55' }}>X</span>
        </div>

        <div className="w-full max-w-4xl">
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }}>
            <div className="text-center mb-8">
              <div className="flex items-center justify-center gap-3 mb-4">
                <Image
                  src="/AG3NT.png"
                  alt="AG3NT"
                  width={60}
                  height={20}
                  className="object-contain"
                  style={{ width: 'auto', height: 'auto' }}
                />
              </div>
              <h1 className="text-2xl font-bold mb-2">Upload Project Plan</h1>
              <p className="text-gray-400">Skip planning and go directly to autonomous coding</p>
            </div>

            <ProjectUpload
              showUploadMode={uploadState.showUploadMode}
              setShowUploadMode={uploadState.setShowUploadMode}
              onPlanUploaded={handlePlanUploaded}
            />
          </motion.div>
        </div>
      </div>
    )
  }

  // Show initial planning interface if not started
  if (!planningState.hasStarted) {
    return (
      <div className="min-h-screen bg-[#000000] text-white flex items-center justify-center p-4">
        {/* Settings Modal */}
        <SettingsModal
          isOpen={settingsState.isSettingsOpen}
          onOpenChange={settingsState.setIsSettingsOpen}
          userApiKey={settingsState.userApiKey}
          setUserApiKey={settingsState.setUserApiKey}
          preferredModel={settingsState.preferredModel}
          setPreferredModel={settingsState.setPreferredModel}
          isAutonomousMode={settingsState.isAutonomousMode}
          setIsAutonomousMode={settingsState.setIsAutonomousMode}
        />

        {/* Settings Button */}
        <Button
          onClick={() => settingsState.setIsSettingsOpen(true)}
          variant="ghost"
          size="sm"
          className="fixed bottom-6 left-6 h-8 px-3 text-xs text-[#888] hover:text-white hover:bg-[#1a1a1a] rounded-md"
        >
          <Settings className="w-3 h-3" />
        </Button>

        {/* Powered by AP3X */}
        <div className="fixed bottom-6 right-6 text-xs text-gray-500 font-medium z-50">
          Powered by{' '}
          <span className="text-white font-semibold">AP3</span>
          <span className="text-[#ff2d55] font-semibold" style={{ textShadow: '0 0 4px #ff2d55, 0 0 8px #ff2d55' }}>X</span>
        </div>

        {/* Upload Project Plan Button */}
        <ProjectUpload
          showUploadMode={uploadState.showUploadMode}
          setShowUploadMode={uploadState.setShowUploadMode}
          onPlanUploaded={handlePlanUploaded}
        />

        {/* Main Planning Workflow */}
        <PlanningWorkflow
          {...planningState}
          addChatMessage={chatState.addChatMessage}
          preferredModel={settingsState.preferredModel}
          userApiKey={settingsState.userApiKey}
          isAutonomousMode={settingsState.isAutonomousMode}
          onPlanningComplete={handlePlanningComplete}
        />
      </div>
    )
  }

  // Main application interface
  return (
    <div className="h-screen w-full bg-[#000000] text-[#e5e5e5] font-sans text-sm flex flex-col overflow-hidden">
      {/* Top Navigation Bar */}
      <header className="h-12 md:h-14 bg-[#0a0a0a] border-b border-[#1a1a1a] flex items-center justify-between px-3 md:px-6">
        <div className="flex items-center gap-3 md:gap-6 min-w-0">
          <div className="flex items-center gap-2 md:gap-3">
            <div className="text-lg md:text-xl font-bold">
              <span className="text-white">AP3</span>
              <span className="text-[#ff2d55]" style={{ textShadow: '0 0 4px #ff2d55, 0 0 8px #ff2d55' }}>X</span>
            </div>
          </div>
          <div className="hidden sm:flex items-center gap-1 text-xs text-[#666]">
            <span>Personal</span>
            <span>/</span>
            <span>Project Planning</span>
          </div>
        </div>
        <div className="flex items-center gap-1 md:gap-2">
          <Button
            onClick={() => settingsState.setIsSettingsOpen(true)}
            variant="ghost"
            size="sm"
            className="h-7 md:h-8 px-2 md:px-3 text-xs text-[#888] hover:text-white hover:bg-[#1a1a1a] rounded-md"
          >
            <Settings className="w-3 h-3 md:mr-2" />
            <span className="hidden md:inline">Settings</span>
          </Button>
          <Button
            size="sm"
            className="h-7 md:h-8 px-2 md:px-4 bg-white text-black hover:bg-gray-100 text-xs font-medium rounded-md"
          >
            <span className="hidden sm:inline">Publish</span>
            <span className="sm:hidden">📤</span>
          </Button>
        </div>
      </header>

      {/* Settings Modal */}
      <SettingsModal
        isOpen={settingsState.isSettingsOpen}
        onOpenChange={settingsState.setIsSettingsOpen}
        userApiKey={settingsState.userApiKey}
        setUserApiKey={settingsState.setUserApiKey}
        preferredModel={settingsState.preferredModel}
        setPreferredModel={settingsState.setPreferredModel}
        isAutonomousMode={settingsState.isAutonomousMode}
        setIsAutonomousMode={settingsState.setIsAutonomousMode}
      />

      {/* Main Content Area */}
      <div className="flex flex-1 overflow-hidden p-4 gap-2">
        {/* Left Sidebar - Chat */}
        <ChatSidebar
          sidebarWidth={chatState.sidebarWidth}
          chatMessages={chatState.chatMessages}
          chatInput={chatState.chatInput}
          setChatInput={chatState.setChatInput}
          addChatMessage={chatState.addChatMessage}
          isProcessing={planningState.isProcessing}
          hasStarted={planningState.hasStarted}
          tasks={planningState.tasks}
          currentTaskIndex={planningState.currentTaskIndex}
          setActiveTab={setActiveTab}
          isCoding={codingState.isCoding}
          codingTasks={codingState.codingTasks}
          activeFile={codingState.activeFile}
        />

        {/* Resize Handle */}
        <div
          className="w-1 bg-transparent hover:bg-[#333] cursor-col-resize transition-colors"
          onMouseDown={(e) => {
            chatState.setIsResizing(true)
            const startX = e.clientX
            const startWidth = chatState.sidebarWidth

            const handleMouseMove = (e: MouseEvent) => {
              const newWidth = Math.max(300, Math.min(800, startWidth + (e.clientX - startX)))
              chatState.setSidebarWidth(newWidth)
            }

            const handleMouseUp = () => {
              chatState.setIsResizing(false)
              document.removeEventListener('mousemove', handleMouseMove)
              document.removeEventListener('mouseup', handleMouseUp)
            }

            document.addEventListener('mousemove', handleMouseMove)
            document.addEventListener('mouseup', handleMouseUp)
          }}
        />

        {/* Main Content */}
        <main className="flex-1 bg-[#0a0a0a] rounded-xl overflow-hidden">
          {/* Tab Navigation */}
          <div className="h-12 border-b border-[#1a1a1a] flex items-center px-4 gap-4">
            {["preview", "code", "planning", "graph"].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab as any)}
                className={`px-3 py-1.5 text-sm rounded-md transition-colors ${
                  activeTab === tab
                    ? "bg-[#1a1a1a] text-white"
                    : "text-gray-400 hover:text-white hover:bg-[#151515]"
                }`}
              >
                {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </button>
            ))}
          </div>

          {/* Tab Content */}
          <div className="flex-1 overflow-hidden">
            {activeTab === "preview" && (
              <div className="h-full">
                {codingState.activeSandbox ? (
                  <E2BPreview
                    sandboxes={codingState.e2bSandboxes}
                    activeSandbox={codingState.activeSandbox}
                    onSandboxSelect={codingState.setActiveSandbox}
                    onRefresh={async (sandboxId) => {
                      console.log('Refreshing sandbox:', sandboxId)
                    }}
                    streamingUpdates={codingState.streamingUpdates}
                    className="h-full"
                  />
                ) : (
                  <Globe key="preview-globe" />
                )}
              </div>
            )}

            {activeTab === "code" && (
              <div className="h-full p-4">
                <div className="text-center text-gray-400">
                  Code editor will appear here when coding starts
                </div>
              </div>
            )}

            {activeTab === "planning" && (
              <div className="h-full overflow-auto">
                {planningState.results && Object.keys(planningState.results).length > 0 ? (
                  <ResultsView
                    results={planningState.results}
                    selectedSection={planningState.selectedPlanningSection}
                    onSectionSelect={planningState.setSelectedPlanningSection}
                  />
                ) : (
                  <div className="p-4 text-center text-gray-400">
                    Planning results will appear here
                  </div>
                )}
              </div>
            )}

            {activeTab === "graph" && (
              <div className="h-full p-4">
                <div className="text-center text-gray-400">
                  Context graph visualization will appear here
                </div>
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  )
}


