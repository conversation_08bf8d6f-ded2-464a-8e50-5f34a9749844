/**
 * AG3NT Unified Context Engine v2.0
 * 
 * Combines the best features from all context engine implementations:
 * - Original context-engine.ts: Planning workflow, decision graphs, validation
 * - unified-context-engine.ts: Multi-agent coordination, codebase analysis
 * - Context-Engine/: Standalone service capabilities, hybrid retrieval
 * 
 * This is the single source of truth for all context management in AG3NT.
 */

import neo4j from 'neo4j-driver'
import Redis from 'ioredis'
import { EventEmitter } from 'events'
import { promises as fs } from 'fs'
import path from 'path'

// Core Types
export type AgentType = 'planner' | 'task-planner' | 'executor' | 'workflow' | 'frontend-coder' | 'backend-coder' | 'tester' | 'reviewer'

export interface AgentContextScope {
  agentType: AgentType
  operationId: string
  parentContext?: string
  requiredCapabilities: string[]
  contextFilters: string[]
}

export interface ProjectContext {
  originalPrompt: string
  projectType: string
  features: string[]
  complexity: "Simple" | "Medium" | "Complex"
  confidence: number
  completeness: number
  stepHistory: string[]
  dependencies: Record<string, any>
  validation: {
    isValid: boolean
    issues: string[]
    securityScore: number
  }
}

export interface CodebaseContext {
  projectPath: string
  files: Map<string, FileContext>
  dependencies: Map<string, string[]>
  symbols: Map<string, SymbolContext>
  lastAnalyzed: Date
  architecture?: ArchitecturalInsights
}

export interface FileContext {
  path: string
  content: string
  language: string
  ast?: any
  symbols: string[]
  dependencies: string[]
  lastModified: Date
  size: number
  complexity: number
}

export interface SymbolContext {
  name: string
  type: 'function' | 'class' | 'variable' | 'interface' | 'type'
  file: string
  line: number
  scope: string
  dependencies: string[]
  usages: string[]
}

export interface ArchitecturalInsights {
  patterns: string[]
  layers: string[]
  dataFlow: string[]
  concerns: string[]
  suggestions: string[]
  scalabilityScore: number
}

export interface TemporalNode {
  id: string
  nodeType: string
  properties: Record<string, any>
  validFrom: Date
  validTo?: Date
  transactionTime: Date
  version: string
  previousVersion?: string
  changeType: 'CREATE' | 'UPDATE' | 'DELETE'
  changeReason?: string
  changedBy?: string
}

export interface TemporalRelationship {
  id: string
  fromNodeId: string
  toNodeId: string
  relationshipType: string
  properties: Record<string, any>
  validFrom: Date
  validTo?: Date
  transactionTime: Date
  version: string
  changeType: 'CREATE' | 'UPDATE' | 'DELETE'
}

export interface ContextNode {
  id: string
  dependencies: string[]
  outputs: string[]
  contextRequirements: string[]
  validationRules: string[]
  crossReferences: string[]
  timestamp: string
  version: string
  agentMetadata?: {
    agentId: string
    agentType: AgentType
    operationId: string
  }
}

export interface EnhancedContext {
  context: any
  enrichments: ContextEnrichment[]
  confidence: number
  recommendations: string[]
  agentMetadata: {
    agentId: string
    agentType: AgentType
    operationId: string
    capabilities: string[]
    sharedState: any
  }
  codebase?: CodebaseContext
  externalKnowledge?: any
  bestPractices?: string[]
  templates?: Record<string, any>
}

export interface ContextEnrichment {
  type: 'knowledge' | 'template' | 'best_practice' | 'validation' | 'mcp' | 'rag'
  source: string
  content: any
  confidence: number
  applicableSteps: string[]
  timestamp: string
}

export interface UnifiedContextEngineConfig {
  enableMCP?: boolean
  enableRAG?: boolean
  enableSequentialThinking?: boolean
  enableContextEnrichment?: boolean
  enableCodebaseAnalysis?: boolean
  enableNeo4j?: boolean
  enableRedis?: boolean
  enableTemporalGraph?: boolean
  memoryDepth?: number
  validationLevel?: 'basic' | 'strict' | 'paranoid'
  cacheSize?: number
  ttlDefault?: number
  neo4j?: {
    uri: string
    username: string
    password: string
    database?: string
  }
  redis?: {
    host: string
    port: number
    password?: string
  }
  features?: {
    ragEnabled: boolean
    memoryDepth: number
    validationLevel: "basic" | "strict" | "paranoid"
    codebaseAnalysis: boolean
    realTimeSync: boolean
  }
}

/**
 * Unified Context Engine - Single source of truth for all AG3NT context management
 */
export class UnifiedContextEngine extends EventEmitter {
  private neo4jDriver: neo4j.Driver | null = null
  private redis: Redis | null = null
  private isInitialized = false

  // Core context storage
  private projectContext: ProjectContext | null = null
  private codebaseContext: CodebaseContext | null = null
  private memoryStore: Map<string, any> = new Map()
  private decisionGraph: Map<string, ContextNode> = new Map()
  private contextVersions: Map<string, ProjectContext> = new Map()
  private crossReferences: Map<string, string[]> = new Map()

  // Agent management
  private registeredAgents: Map<string, AgentContextScope> = new Map()
  private sharedState: Map<AgentType, any> = new Map()
  private agentCommunication: Map<string, any> = new Map()

  // Enhanced capabilities
  private mcpIntegration: any = null
  private ragEngine: any = null

  // Temporal graph capabilities
  private temporalNodes: Map<string, TemporalNode[]> = new Map()
  private temporalRelationships: Map<string, TemporalRelationship[]> = new Map()
  private temporalQueries: Map<string, any> = new Map()
  
  // Configuration
  private config: {
    neo4j: {
      uri: string
      username: string
      password: string
    }
    redis: {
      host: string
      port: number
      password?: string
    }
    features: {
      ragEnabled: boolean
      memoryDepth: number
      validationLevel: "basic" | "strict" | "paranoid"
      codebaseAnalysis: boolean
      realTimeSync: boolean
    }
  }

  constructor(config: UnifiedContextEngineConfig = {}) {
    super()
    this.config = {
      neo4j: {
        uri: config.neo4j?.uri || process.env.NEO4J_URI || 'bolt://localhost:7687',
        username: config.neo4j?.username || process.env.NEO4J_USERNAME || 'neo4j',
        password: config.neo4j?.password || process.env.NEO4J_PASSWORD || 'password'
      },
      redis: {
        host: config.redis?.host || process.env.REDIS_HOST || 'localhost',
        port: config.redis?.port || parseInt(process.env.REDIS_PORT || '6379'),
        password: config.redis?.password || process.env.REDIS_PASSWORD
      },
      features: {
        ragEnabled: config.enableRAG ?? config.features?.ragEnabled ?? true,
        memoryDepth: config.memoryDepth ?? config.features?.memoryDepth ?? 50,
        validationLevel: config.validationLevel ?? config.features?.validationLevel ?? "strict",
        codebaseAnalysis: config.enableCodebaseAnalysis ?? config.features?.codebaseAnalysis ?? true,
        realTimeSync: config.enableRedis ?? config.features?.realTimeSync ?? true,
        enableTemporalGraph: config.enableTemporalGraph ?? true
      }
    }
  }

  /**
   * Initialize the unified context engine
   */
  async initialize(): Promise<void> {
    try {
      this.emit('initialization_started')
      
      // Check if external databases are enabled
      const enableNeo4j = this.config.features.enableNeo4j !== false && process.env.NEO4J_URI
      const enableRedis = this.config.features.enableRedis !== false && process.env.REDIS_HOST

      if (enableNeo4j) {
        try {
          // Initialize Neo4j connection
          this.neo4jDriver = neo4j.driver(
            this.config.neo4j.uri,
            neo4j.auth.basic(this.config.neo4j.username, this.config.neo4j.password)
          )

          // Test Neo4j connection
          await this.neo4jDriver.verifyConnectivity()
          console.log('🗄️ Neo4j connection established')
        } catch (error) {
          console.warn('⚠️ Neo4j connection failed, using in-memory storage:', error.message)
          this.neo4jDriver = null
        }
      } else {
        console.log('📝 Using in-memory storage (Neo4j disabled)')
      }

      if (enableRedis) {
        try {
          // Initialize Redis connection
          this.redis = new Redis({
            host: this.config.redis.host,
            port: this.config.redis.port,
            password: this.config.redis.password,
            retryDelayOnFailover: 100,
            maxRetriesPerRequest: 3
          })

          // Test Redis connection
          await this.redis.ping()
          console.log('🔄 Redis connection established')
        } catch (error) {
          console.warn('⚠️ Redis connection failed, using in-memory cache:', error.message)
          this.redis = null
        }
      } else {
        console.log('💾 Using in-memory cache (Redis disabled)')
      }
      
      // Initialize core components
      await this.initializeDecisionGraph()
      await this.initializeSharedState()

      // Initialize enhanced capabilities
      await this.initializeMCPIntegration()
      await this.initializeRAGEngine()

      // Initialize temporal graph capabilities
      if (this.config.features.enableTemporalGraph !== false) {
        await this.initializeTemporalGraphSchema()
        console.log('🕒 Temporal graph capabilities enabled')
      }

      this.isInitialized = true
      this.emit('initialization_completed')
      
    } catch (error) {
      this.emit('initialization_failed', error)
      console.warn('⚠️ Context engine initialization had issues, continuing with limited functionality:', error.message)

      // Continue with minimal functionality
      this.isInitialized = true
      this.emit('initialization_completed')
    }
  }

  /**
   * Initialize decision graph structure
   */
  private async initializeDecisionGraph(): Promise<void> {
    // Create basic decision graph nodes for common planning steps
    const commonSteps = [
      'project_analysis',
      'tech_stack_selection', 
      'architecture_design',
      'component_planning',
      'implementation_strategy',
      'testing_strategy',
      'deployment_planning'
    ]
    
    commonSteps.forEach((step, index) => {
      this.decisionGraph.set(step, {
        id: step,
        dependencies: index > 0 ? [commonSteps[index - 1]] : [],
        outputs: index < commonSteps.length - 1 ? [commonSteps[index + 1]] : [],
        contextRequirements: ['project_context'],
        validationRules: ['completeness_check', 'consistency_check'],
        crossReferences: [],
        timestamp: new Date().toISOString(),
        version: '1.0.0'
      })
    })
  }

  /**
   * Initialize shared state for different agent types
   */
  private async initializeSharedState(): Promise<void> {
    const agentTypes: AgentType[] = ['planner', 'task-planner', 'executor', 'workflow', 'frontend-coder', 'backend-coder', 'tester', 'reviewer']
    
    agentTypes.forEach(agentType => {
      this.sharedState.set(agentType, {
        status: 'idle',
        currentTask: null,
        completedTasks: [],
        capabilities: this.getAgentCapabilities(agentType),
        lastActivity: new Date().toISOString(),
        performance: {
          tasksCompleted: 0,
          averageTime: 0,
          successRate: 1.0
        }
      })
    })
  }

  /**
   * Get default capabilities for each agent type
   */
  private getAgentCapabilities(agentType: AgentType): string[] {
    const capabilities = {
      'planner': ['project_analysis', 'requirement_gathering', 'tech_stack_selection', 'architecture_design'],
      'task-planner': ['task_breakdown', 'dependency_analysis', 'priority_management', 'resource_allocation'],
      'executor': ['task_execution', 'progress_monitoring', 'quality_assurance', 'coordination'],
      'workflow': ['process_orchestration', 'state_management', 'error_handling', 'recovery'],
      'frontend-coder': ['react_development', 'ui_components', 'state_management', 'responsive_design'],
      'backend-coder': ['api_development', 'database_design', 'authentication', 'performance_optimization'],
      'tester': ['unit_testing', 'integration_testing', 'e2e_testing', 'performance_testing'],
      'reviewer': ['code_review', 'security_analysis', 'best_practices', 'documentation_review']
    }
    
    return capabilities[agentType] || []
  }

  /**
   * Initialize MCP integration
   */
  private async initializeMCPIntegration(): Promise<void> {
    try {
      // Import and initialize MCP integration
      const { UniversalMCPIntegration } = await import('./universal-mcp-integration')
      this.mcpIntegration = new UniversalMCPIntegration()
      await this.mcpIntegration.initialize?.()
      console.log('🔌 MCP integration initialized')
    } catch (error) {
      console.warn('MCP integration failed to initialize:', error)
      this.mcpIntegration = null
    }
  }

  /**
   * Initialize RAG engine
   */
  private async initializeRAGEngine(): Promise<void> {
    try {
      // Import and initialize RAG engine
      const { AgenticRAGEngine } = await import('./agentic-rag-engine')
      this.ragEngine = new AgenticRAGEngine()
      await this.ragEngine.initialize?.()
      console.log('🔍 RAG engine initialized')
    } catch (error) {
      console.warn('RAG engine failed to initialize:', error)
      this.ragEngine = null
    }
  }

  /**
   * Register an agent with the context engine
   */
  async registerAgent(agentId: string, scope: AgentContextScope): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Context engine not initialized')
    }
    
    this.registeredAgents.set(agentId, scope)
    
    // Update shared state
    const currentState = this.sharedState.get(scope.agentType) || {}
    this.sharedState.set(scope.agentType, {
      ...currentState,
      status: 'active',
      lastActivity: new Date().toISOString()
    })
    
    this.emit('agent_registered', { agentId, scope })
  }

  /**
   * Get context for a specific agent
   */
  async getContextForAgent(agentId: string, step?: string): Promise<EnhancedContext> {
    if (!this.isInitialized) {
      throw new Error('Context engine not initialized')
    }
    
    const agentScope = this.registeredAgents.get(agentId)
    if (!agentScope) {
      throw new Error(`Agent ${agentId} not registered`)
    }
    
    // Get base context
    const baseContext = this.getRelevantContext(step || 'all')
    
    // Get codebase context if available and agent needs it
    let codebaseContext: CodebaseContext | undefined
    if (this.config.features.codebaseAnalysis && this.codebaseContext) {
      codebaseContext = this.filterCodebaseForAgent(this.codebaseContext, agentScope)
    }
    
    // Filter context for agent-specific needs
    const filteredContext = this.filterContextForAgent(baseContext, agentScope)
    
    return {
      context: filteredContext,
      enrichments: [], // TODO: Implement enrichment pipeline
      confidence: this.calculateContextConfidence(filteredContext),
      recommendations: this.generateRecommendations(agentScope, filteredContext),
      agentMetadata: {
        agentId,
        agentType: agentScope.agentType,
        operationId: agentScope.operationId,
        capabilities: agentScope.requiredCapabilities,
        sharedState: this.getSharedStateForAgent(agentScope.agentType)
      },
      codebase: codebaseContext
    }
  }

  /**
   * Process and analyze a codebase
   */
  async processCodebase(projectPath: string): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Context engine not initialized')
    }

    try {
      this.emit('codebase_analysis_started', { projectPath })

      // Initialize codebase context
      this.codebaseContext = {
        projectPath,
        files: new Map(),
        dependencies: new Map(),
        symbols: new Map(),
        lastAnalyzed: new Date()
      }

      // Process files and build context
      await this.analyzeCodebaseFiles(projectPath)
      await this.buildDependencyGraph()
      await this.extractSymbols()
      await this.analyzeArchitecture()

      // Store in databases
      await this.storeCodebaseInGraph()
      await this.cacheCodebaseContext()

      this.emit('codebase_analysis_completed', {
        projectPath,
        fileCount: this.codebaseContext.files.size,
        symbolCount: this.codebaseContext.symbols.size
      })

    } catch (error) {
      this.emit('codebase_analysis_failed', error)
      throw error
    }
  }

  /**
   * Store step data with enhanced context propagation
   */
  async storeStepData(step: string, data: any, agentId?: string): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Context engine not initialized')
    }

    const versionKey = `${step}_${Date.now()}`

    // Enhanced memory store with full context propagation
    const contextNode = this.decisionGraph.get(step)
    if (contextNode) {
      const enrichedData = this.enrichContextData(step, data, contextNode)

      this.memoryStore.set(`step_${step}`, {
        data: enrichedData,
        originalData: data,
        timestamp: new Date().toISOString(),
        dependencies: contextNode.dependencies,
        upstreamContext: this.gatherUpstreamContext(contextNode.dependencies),
        crossReferences: this.updateCrossReferences(step, enrichedData),
        version: versionKey,
        agentId
      })

      // Update decision graph node
      contextNode.timestamp = new Date().toISOString()
      contextNode.crossReferences = this.findCrossReferences(step, enrichedData)

      if (agentId) {
        contextNode.agentMetadata = {
          agentId,
          agentType: this.registeredAgents.get(agentId)?.agentType || 'executor',
          operationId: this.registeredAgents.get(agentId)?.operationId || 'unknown'
        }
      }
    }

    // Store in Redis for real-time access
    if (this.redis && this.config.features.realTimeSync) {
      await this.redis.setex(`context:step:${step}`, 3600, JSON.stringify({
        data: data,
        timestamp: new Date().toISOString(),
        agentId
      }))
    }

    this.emit('step_data_stored', { step, agentId, timestamp: new Date().toISOString() })
  }

  /**
   * Get relevant context for a step or agent
   */
  private getRelevantContext(step: string): any {
    if (step === 'all') {
      // Return all context for comprehensive view
      const allContext: any = {}

      this.memoryStore.forEach((value, key) => {
        allContext[key] = value.data
      })

      return {
        project: this.projectContext,
        steps: allContext,
        codebase: this.codebaseContext ? {
          path: this.codebaseContext.projectPath,
          fileCount: this.codebaseContext.files.size,
          lastAnalyzed: this.codebaseContext.lastAnalyzed
        } : null
      }
    }

    // Return specific step context with dependencies
    const stepData = this.memoryStore.get(`step_${step}`)
    if (!stepData) {
      return { project: this.projectContext }
    }

    return {
      project: this.projectContext,
      current: stepData.data,
      dependencies: stepData.upstreamContext,
      crossReferences: stepData.crossReferences
    }
  }

  /**
   * Filter context for specific agent needs
   */
  private filterContextForAgent(context: any, agentScope: AgentContextScope): any {
    // Apply context filters based on agent type and capabilities
    const filtered: any = { ...context }

    // Agent-specific filtering logic
    switch (agentScope.agentType) {
      case 'frontend-coder':
        filtered.relevantFiles = this.getRelevantFiles(['tsx', 'jsx', 'ts', 'js', 'css', 'scss'])
        filtered.uiComponents = this.extractUIComponents()
        break

      case 'backend-coder':
        filtered.relevantFiles = this.getRelevantFiles(['ts', 'js', 'py', 'sql'])
        filtered.apiEndpoints = this.extractAPIEndpoints()
        filtered.databaseSchemas = this.extractDatabaseSchemas()
        break

      case 'tester':
        filtered.testableComponents = this.extractTestableComponents()
        filtered.existingTests = this.getRelevantFiles(['test.ts', 'test.js', 'spec.ts', 'spec.js'])
        break

      case 'task-planner':
        filtered.projectStructure = this.getProjectStructure()
        filtered.dependencies = this.getDependencyGraph()
        break
    }

    return filtered
  }

  /**
   * Filter codebase context for specific agent
   */
  private filterCodebaseForAgent(codebase: CodebaseContext, agentScope: AgentContextScope): CodebaseContext {
    const filtered: CodebaseContext = {
      ...codebase,
      files: new Map(),
      symbols: new Map()
    }

    // Filter files based on agent type
    const relevantExtensions = this.getRelevantExtensionsForAgent(agentScope.agentType)

    codebase.files.forEach((file, path) => {
      const extension = path.split('.').pop()?.toLowerCase()
      if (extension && relevantExtensions.includes(extension)) {
        filtered.files.set(path, file)
      }
    })

    // Filter symbols based on filtered files
    codebase.symbols.forEach((symbol, name) => {
      if (filtered.files.has(symbol.file)) {
        filtered.symbols.set(name, symbol)
      }
    })

    return filtered
  }

  /**
   * Get relevant file extensions for agent type
   */
  private getRelevantExtensionsForAgent(agentType: AgentType): string[] {
    const extensionMap = {
      'frontend-coder': ['tsx', 'jsx', 'ts', 'js', 'css', 'scss', 'html'],
      'backend-coder': ['ts', 'js', 'py', 'sql', 'json', 'yaml', 'yml'],
      'tester': ['test.ts', 'test.js', 'spec.ts', 'spec.js', 'e2e.ts'],
      'reviewer': ['ts', 'js', 'tsx', 'jsx', 'py', 'sql', 'md'],
      'planner': ['json', 'yaml', 'yml', 'md', 'txt'],
      'task-planner': ['json', 'yaml', 'yml', 'md'],
      'executor': ['ts', 'js', 'tsx', 'jsx', 'py'],
      'workflow': ['json', 'yaml', 'yml', 'ts', 'js']
    }

    return extensionMap[agentType] || ['ts', 'js', 'tsx', 'jsx']
  }

  /**
   * Calculate context confidence score
   */
  private calculateContextConfidence(context: any): number {
    let confidence = 0.5 // Base confidence

    // Increase confidence based on available data
    if (context.project) confidence += 0.2
    if (context.current) confidence += 0.2
    if (context.dependencies && Object.keys(context.dependencies).length > 0) confidence += 0.1
    if (this.codebaseContext) confidence += 0.2

    return Math.min(confidence, 1.0)
  }

  /**
   * Generate recommendations for agent
   */
  private generateRecommendations(agentScope: AgentContextScope, context: any): string[] {
    const recommendations: string[] = []

    // Agent-specific recommendations
    switch (agentScope.agentType) {
      case 'frontend-coder':
        if (!context.uiComponents || context.uiComponents.length === 0) {
          recommendations.push('Consider creating reusable UI components')
        }
        break

      case 'backend-coder':
        if (!context.apiEndpoints || context.apiEndpoints.length === 0) {
          recommendations.push('Define API endpoints and data models')
        }
        break

      case 'tester':
        if (!context.existingTests || context.existingTests.length === 0) {
          recommendations.push('Start with unit tests for core functionality')
        }
        break
    }

    return recommendations
  }

  /**
   * Get shared state for agent type
   */
  private getSharedStateForAgent(agentType: AgentType): any {
    return this.sharedState.get(agentType) || {}
  }

  /**
   * Set project context
   */
  async setProjectContext(context: ProjectContext): Promise<void> {
    this.projectContext = context

    // Store in Redis
    if (this.redis && this.config.features.realTimeSync) {
      await this.redis.setex('context:project', 3600, JSON.stringify(context))
    }

    this.emit('project_context_updated', context)
  }

  /**
   * Get project context
   */
  getProjectContext(): ProjectContext | null {
    return this.projectContext
  }

  /**
   * Update agent status
   */
  async updateAgentStatus(agentId: string, status: 'idle' | 'active' | 'busy' | 'error', currentTask?: string): Promise<void> {
    const agentScope = this.registeredAgents.get(agentId)
    if (!agentScope) {
      throw new Error(`Agent ${agentId} not registered`)
    }

    const currentState = this.sharedState.get(agentScope.agentType) || {}
    this.sharedState.set(agentScope.agentType, {
      ...currentState,
      status,
      currentTask,
      lastActivity: new Date().toISOString()
    })

    // Store in Redis
    if (this.redis && this.config.features.realTimeSync) {
      await this.redis.setex(`agent:${agentId}:status`, 300, JSON.stringify({
        status,
        currentTask,
        timestamp: new Date().toISOString()
      }))
    }

    this.emit('agent_status_updated', { agentId, status, currentTask })
  }

  /**
   * Get all registered agents
   */
  getRegisteredAgents(): Map<string, AgentContextScope> {
    return new Map(this.registeredAgents)
  }

  /**
   * Update context with new data
   */
  updateContext(step: string, data: any): void {
    try {
      // Store in memory
      this.memoryStore.set(`step_${step}`, {
        data,
        timestamp: Date.now(),
        step
      })

      console.log(`📝 Updated context for step: ${step}`)
    } catch (error) {
      console.warn(`⚠️ Failed to update context for step ${step}:`, error)
    }
  }

  /**
   * Update context with new data
   */
  updateContext(step: string, data: any): void {
    try {
      // Store in memory
      this.memoryStore.set(`step_${step}`, {
        data,
        timestamp: Date.now(),
        step
      })

      console.log(`📝 Updated context for step: ${step}`)
    } catch (error) {
      console.warn(`⚠️ Failed to update context for step ${step}:`, error)
    }
  }

  /**
   * Search documentation (simplified implementation)
   */
  async searchDocumentation(query: string): Promise<any> {
    try {
      // Simplified documentation search
      const mockResults = {
        query,
        results: [
          { title: 'Best Practices Guide', content: 'Modern project structure recommendations' },
          { title: 'Framework Documentation', content: 'Official framework guidelines' }
        ],
        confidence: 0.8
      }

      console.log(`🔍 Documentation search for: ${query}`)
      return mockResults
    } catch (error) {
      console.warn(`⚠️ Documentation search failed for: ${query}`, error)
      return { query, results: [], confidence: 0 }
    }
  }

  /**
   * Get documentation for specific technology/framework
   */
  async getDocumentation(technology: string, context?: string): Promise<any> {
    try {
      // Simplified documentation retrieval
      const techDocs = {
        'React': {
          framework: 'React',
          version: '18.x',
          bestPractices: [
            'Use functional components with hooks',
            'Implement proper state management',
            'Follow component composition patterns',
            'Use TypeScript for type safety'
          ],
          patterns: [
            'Component-based architecture',
            'Unidirectional data flow',
            'Virtual DOM optimization',
            'Hook-based state management'
          ],
          styling: [
            'CSS Modules for scoped styling',
            'Styled-components for dynamic styles',
            'Tailwind CSS for utility-first approach'
          ]
        },
        'Next.js': {
          framework: 'Next.js',
          version: '14.x',
          bestPractices: [
            'Use App Router for new projects',
            'Implement proper SEO optimization',
            'Leverage server-side rendering',
            'Optimize images and fonts'
          ],
          patterns: [
            'File-based routing',
            'Server and client components',
            'API routes for backend logic',
            'Static site generation'
          ]
        },
        'TypeScript': {
          language: 'TypeScript',
          version: '5.x',
          bestPractices: [
            'Use strict type checking',
            'Define proper interfaces',
            'Leverage union types',
            'Implement generic types'
          ]
        }
      }

      const docs = techDocs[technology] || {
        framework: technology,
        bestPractices: [
          'Follow framework conventions',
          'Implement proper error handling',
          'Use modern development patterns',
          'Ensure code maintainability'
        ],
        patterns: [
          'Modular architecture',
          'Separation of concerns',
          'Clean code principles'
        ]
      }

      console.log(`📚 Retrieved documentation for: ${technology}`)
      return {
        technology,
        context,
        documentation: docs,
        confidence: 0.9
      }
    } catch (error) {
      console.warn(`⚠️ Documentation retrieval failed for: ${technology}`, error)
      return {
        technology,
        context,
        documentation: null,
        confidence: 0
      }
    }
  }

  /**
   * Get system health status
   */
  getSystemHealth(): {
    isInitialized: boolean
    neo4jConnected: boolean
    redisConnected: boolean
    registeredAgents: number
    memoryUsage: number
  } {
    return {
      isInitialized: this.isInitialized,
      neo4jConnected: this.neo4jDriver !== null,
      redisConnected: this.redis !== null,
      registeredAgents: this.registeredAgents.size,
      memoryUsage: this.memoryStore.size
    }
  }

  /**
   * Enhance context with RAG and external knowledge
   */
  async enhanceWithRAG(step: string, query: string): Promise<{
    enhancedContext: any
    enrichments: ContextEnrichment[]
    confidence: number
    recommendations: string[]
  }> {
    const enrichments: ContextEnrichment[] = []
    let enhancedContext: any = {}
    let confidence = 0.8

    try {
      // MCP Enhancement
      if (this.mcpIntegration) {
        try {
          const mcpEnrichment = await this.mcpIntegration.enhanceContext(step, query)
          if (mcpEnrichment) {
            enrichments.push({
              type: 'mcp',
              source: 'Model Context Protocol',
              content: mcpEnrichment,
              confidence: 0.9,
              applicableSteps: [step, 'all'],
              timestamp: new Date().toISOString()
            })
            enhancedContext.mcp = mcpEnrichment
          }
        } catch (error) {
          console.warn('MCP enhancement failed:', error)
        }
      }

      // RAG Enhancement
      if (this.ragEngine) {
        try {
          const ragResults = await this.ragEngine.search({
            query,
            intent: 'context_enhancement',
            context: { step },
            options: { maxResults: 5, semanticThreshold: 0.7 }
          })

          if (ragResults?.results?.length > 0) {
            enrichments.push({
              type: 'rag',
              source: 'Retrieval Augmented Generation',
              content: ragResults.results,
              confidence: ragResults.confidence || 0.85,
              applicableSteps: [step, 'all'],
              timestamp: new Date().toISOString()
            })
            enhancedContext.rag = ragResults
          }
        } catch (error) {
          console.warn('RAG enhancement failed:', error)
        }
      }

      // Best Practices Enhancement
      const bestPractices = this.getBestPracticesForStep(step)
      if (bestPractices.length > 0) {
        enrichments.push({
          type: 'best_practice',
          source: 'Best Practices Database',
          content: bestPractices,
          confidence: 0.8,
          applicableSteps: [step],
          timestamp: new Date().toISOString()
        })
        enhancedContext.bestPractices = bestPractices
      }

      // Template Enhancement
      const templates = this.getTemplatesForStep(step)
      if (templates) {
        enrichments.push({
          type: 'template',
          source: 'Template Library',
          content: templates,
          confidence: 0.75,
          applicableSteps: [step],
          timestamp: new Date().toISOString()
        })
        enhancedContext.templates = templates
      }

      // Calculate overall confidence
      if (enrichments.length > 0) {
        confidence = enrichments.reduce((sum, e) => sum + e.confidence, 0) / enrichments.length
      }

      console.log(`🔍 RAG enhancement for step: ${step} - ${enrichments.length} enrichments, confidence: ${confidence}`)

      return {
        enhancedContext,
        enrichments,
        confidence,
        recommendations: this.generateRecommendations(step, enrichments)
      }
    } catch (error) {
      console.error('RAG enhancement failed:', error)
      return {
        enhancedContext: {},
        enrichments: [],
        confidence: 0.5,
        recommendations: [`Error enhancing context for ${step}`]
      }
    }
  }

  /**
   * Perform sequential thinking for complex reasoning
   */
  async performSequentialThinking(thought: string, context: any = {}): Promise<any> {
    if (!this.mcpIntegration) {
      console.warn('Sequential thinking requires MCP integration')
      return null
    }

    try {
      return await this.mcpIntegration.sequentialThinking(thought, {
        ...context,
        timestamp: new Date().toISOString(),
        contextEngine: 'unified-v2'
      })
    } catch (error) {
      console.error('Sequential thinking failed:', error)
      return null
    }
  }

  /**
   * Query context using natural language
   */
  async queryContext(query: string, options: {
    agentId?: string
    contextType?: string
    limit?: number
  } = {}): Promise<any[]> {
    try {
      // Use RAG engine for natural language querying
      if (this.ragEngine) {
        const results = await this.ragEngine.search({
          query,
          intent: 'context_query',
          context: options,
          options: { maxResults: options.limit || 10 }
        })
        return results?.results || []
      }

      // Fallback to simple text search in memory store
      const searchResults: any[] = []
      const lowerQuery = query.toLowerCase()

      for (const [key, entry] of this.memoryStore.entries()) {
        const entryStr = JSON.stringify(entry.data).toLowerCase()
        if (entryStr.includes(lowerQuery)) {
          searchResults.push({
            key,
            data: entry.data,
            relevance: this.calculateRelevance(lowerQuery, entryStr)
          })
        }
      }

      return searchResults
        .sort((a, b) => b.relevance - a.relevance)
        .slice(0, options.limit || 10)
    } catch (error) {
      console.error('Context query failed:', error)
      return []
    }
  }

/**
 * Enhance context for a specific agent scope
 */
async enhanceContext(context: any, agentScope: AgentContextScope): Promise<EnhancedContext> {
  try {
    // Get base context
    const baseContext = this.getRelevantContext('all')

    // Filter for agent
    const filteredContext = this.filterContextForAgent(baseContext, agentScope)

    // Enhance with RAG
    const ragEnhancement = await this.enhanceWithRAG('context_enhancement', JSON.stringify(context))

    // Perform sequential thinking if enabled
    let sequentialThought = null
    if (this.mcpIntegration) {
      sequentialThought = await this.performSequentialThinking(
        `Enhancing context for ${agentScope.agentType}`,
        { agentScope, context }
      )
    }

    return {
      context: filteredContext,
      enrichments: ragEnhancement.enrichments,
      confidence: ragEnhancement.confidence,
      recommendations: ragEnhancement.recommendations,
      agentMetadata: {
        agentId: agentScope.operationId,
        agentType: agentScope.agentType,
        operationId: agentScope.operationId,
        capabilities: agentScope.requiredCapabilities,
        sharedState: this.sharedState.get(agentScope.agentType)
      },
      codebase: this.codebaseContext,
      externalKnowledge: ragEnhancement.enhancedContext.rag,
      bestPractices: ragEnhancement.enhancedContext.bestPractices,
      templates: ragEnhancement.enhancedContext.templates,
      sequentialThought
    }
  } catch (error) {
    console.error('Context enhancement failed:', error)
    return {
      context,
      enrichments: [],
      confidence: 0.5,
      recommendations: ['Context enhancement failed'],
      agentMetadata: {
        agentId: agentScope.operationId,
        agentType: agentScope.agentType,
        operationId: agentScope.operationId,
        capabilities: agentScope.requiredCapabilities,
        sharedState: this.sharedState.get(agentScope.agentType)
      }
    }
  }
}

  // Placeholder methods for codebase analysis (to be implemented)
  private async analyzeCodebaseFiles(projectPath: string): Promise<void> {
    // TODO: Implement file analysis with AST parsing
    console.log(`Analyzing codebase files in ${projectPath}`)
  }

  private async buildDependencyGraph(): Promise<void> {
    // TODO: Implement dependency graph building
    console.log('Building dependency graph')
  }

  private async extractSymbols(): Promise<void> {
    // TODO: Implement symbol extraction from AST
    console.log('Extracting symbols')
  }

  private async analyzeArchitecture(): Promise<void> {
    // TODO: Implement architectural analysis
    console.log('Analyzing architecture')
  }

  private async storeCodebaseInGraph(): Promise<void> {
    // TODO: Implement Neo4j storage
    console.log('Storing codebase in Neo4j')
  }

  private async cacheCodebaseContext(): Promise<void> {
    // TODO: Implement Redis caching
    console.log('Caching codebase context in Redis')
  }

  private enrichContextData(step: string, data: any, contextNode: ContextNode): any {
    // TODO: Implement context enrichment with RAG
    return data
  }

  private gatherUpstreamContext(dependencies: string[]): any {
    // TODO: Implement upstream context gathering
    const upstreamContext: any = {}
    dependencies.forEach(dep => {
      const depData = this.memoryStore.get(`step_${dep}`)
      if (depData) {
        upstreamContext[dep] = depData.data
      }
    })
    return upstreamContext
  }

  private updateCrossReferences(step: string, data: any): string[] {
    // TODO: Implement cross-reference updates
    return []
  }

  private findCrossReferences(step: string, data: any): string[] {
    // TODO: Implement cross-reference finding
    return []
  }

  private getRelevantFiles(extensions: string[]): any[] {
    // TODO: Implement relevant file filtering
    if (!this.codebaseContext) return []

    const relevantFiles: any[] = []
    this.codebaseContext.files.forEach((file, path) => {
      const fileExtension = path.split('.').pop()?.toLowerCase()
      if (fileExtension && extensions.some(ext => path.endsWith(ext))) {
        relevantFiles.push({
          path,
          language: file.language,
          size: file.size,
          lastModified: file.lastModified
        })
      }
    })

    return relevantFiles
  }

  private extractUIComponents(): any[] {
    // TODO: Implement UI component extraction
    return []
  }

  private extractAPIEndpoints(): any[] {
    // TODO: Implement API endpoint extraction
    return []
  }

  private extractDatabaseSchemas(): any[] {
    // TODO: Implement database schema extraction
    return []
  }

  private extractTestableComponents(): any[] {
    // TODO: Implement testable component extraction
    return []
  }

  private getProjectStructure(): any {
    // TODO: Implement project structure analysis
    if (!this.codebaseContext) return {}

    return {
      path: this.codebaseContext.projectPath,
      fileCount: this.codebaseContext.files.size,
      symbolCount: this.codebaseContext.symbols.size,
      lastAnalyzed: this.codebaseContext.lastAnalyzed
    }
  }

  private getDependencyGraph(): any {
    // TODO: Implement dependency graph retrieval
    if (!this.codebaseContext) return {}

    const graph: any = {}
    this.codebaseContext.dependencies.forEach((deps, file) => {
      graph[file] = deps
    })

    return graph
  }

  // ============================================================================
  // HELPER METHODS FOR ENHANCED FUNCTIONALITY
  // ============================================================================

  /**
   * Generate recommendations based on enrichments (overloaded method)
   */
  private generateRecommendations(step: string, enrichments: ContextEnrichment[]): string[] {
    const recommendations: string[] = []

    // Generate recommendations based on enrichments
    enrichments.forEach(enrichment => {
      if (enrichment.confidence > 0.8) {
        recommendations.push(`High confidence ${enrichment.type} available for ${step}`)
      } else if (enrichment.confidence > 0.6) {
        recommendations.push(`Medium confidence ${enrichment.type} available for ${step}`)
      }
    })

    // Add step-specific recommendations
    const stepRecommendations: Record<string, string[]> = {
      analyze: ['Validate requirements with stakeholders', 'Consider technical constraints'],
      clarify: ['Ask follow-up questions', 'Document all assumptions'],
      summary: ['Review for completeness', 'Ensure clarity'],
      techstack: ['Consider team expertise', 'Evaluate long-term maintenance'],
      prd: ['Include user acceptance criteria', 'Define success metrics'],
      wireframes: ['Test user flow', 'Consider accessibility'],
      design: ['Create design system', 'Plan for responsive design'],
      database: ['Plan for data migration', 'Consider performance implications'],
      filesystem: ['Follow project conventions', 'Plan for scalability'],
      workflow: ['Define error handling', 'Plan for monitoring'],
      tasks: ['Estimate effort accurately', 'Define clear deliverables'],
      scaffold: ['Set up CI/CD pipeline', 'Include development tools'],
      default: [`Consider ${step} best practices`, `Validate ${step} outputs`]
    }

    const stepSpecific = stepRecommendations[step] || stepRecommendations.default
    recommendations.push(...stepSpecific)

    return recommendations
  }

  /**
   * Get best practices for a specific step
   */
  private getBestPracticesForStep(step: string): string[] {
    const practices: Record<string, string[]> = {
      analyze: ['Understand requirements thoroughly', 'Consider edge cases', 'Validate assumptions'],
      clarify: ['Ask specific questions', 'Validate understanding', 'Document assumptions'],
      summary: ['Be concise and clear', 'Include key points', 'Validate completeness'],
      techstack: ['Consider project requirements', 'Evaluate trade-offs', 'Plan for scalability'],
      prd: ['Define clear requirements', 'Include acceptance criteria', 'Consider user experience'],
      wireframes: ['Focus on user flow', 'Keep it simple', 'Consider responsive design'],
      design: ['Follow design principles', 'Ensure accessibility', 'Consider mobile-first'],
      database: ['Normalize data structure', 'Plan for performance', 'Consider data integrity'],
      filesystem: ['Organize logically', 'Follow conventions', 'Plan for maintainability'],
      workflow: ['Define clear steps', 'Consider dependencies', 'Plan for error handling'],
      tasks: ['Break down into manageable pieces', 'Define clear outcomes', 'Estimate effort'],
      scaffold: ['Follow project structure', 'Include necessary files', 'Set up development environment'],
      code: ['Write clean code', 'Add proper tests', 'Document APIs'],
      default: ['Follow industry standards', 'Consider maintainability', 'Optimize for performance']
    }

    return practices[step] || practices.default
  }

  /**
   * Get templates for a specific step
   */
  private getTemplatesForStep(step: string): Record<string, any> | null {
    const templates: Record<string, any> = {
      analyze: {
        template: 'Project Analysis Template',
        sections: ['Requirements', 'Constraints', 'Assumptions', 'Risks'],
        guidelines: this.getBestPracticesForStep(step)
      },
      prd: {
        template: 'Product Requirements Document',
        sections: ['Overview', 'Features', 'User Stories', 'Acceptance Criteria'],
        guidelines: this.getBestPracticesForStep(step)
      },
      wireframes: {
        template: 'Wireframe Template',
        sections: ['Layout', 'Navigation', 'Components', 'Interactions'],
        guidelines: this.getBestPracticesForStep(step)
      },
      database: {
        template: 'Database Schema Template',
        sections: ['Tables', 'Relationships', 'Indexes', 'Constraints'],
        guidelines: this.getBestPracticesForStep(step)
      }
    }

    return templates[step] || {
      template: `Template for ${step}`,
      sections: ['Overview', 'Details', 'Implementation', 'Validation'],
      guidelines: this.getBestPracticesForStep(step)
    }
  }

  /**
   * Calculate text relevance for search
   */
  private calculateRelevance(query: string, text: string): number {
    const queryWords = query.split(' ').filter(word => word.length > 2)
    let matches = 0

    queryWords.forEach(word => {
      if (text.includes(word)) {
        matches++
      }
    })

    return queryWords.length > 0 ? matches / queryWords.length : 0
  }

  // ============================================================================
  // TEMPORAL GRAPH CAPABILITIES
  // ============================================================================

  /**
   * Create a temporal node with bi-temporal tracking
   */
  async createTemporalNode(
    nodeType: string,
    properties: Record<string, any>,
    options: {
      validFrom?: Date
      validTo?: Date
      changeReason?: string
      changedBy?: string
    } = {}
  ): Promise<TemporalNode> {
    const now = new Date()
    const nodeId = `${nodeType}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    const temporalNode: TemporalNode = {
      id: nodeId,
      nodeType,
      properties,
      validFrom: options.validFrom || now,
      validTo: options.validTo,
      transactionTime: now,
      version: '1.0.0',
      changeType: 'CREATE',
      changeReason: options.changeReason,
      changedBy: options.changedBy
    }

    // Store in memory
    const nodeHistory = this.temporalNodes.get(nodeId) || []
    nodeHistory.push(temporalNode)
    this.temporalNodes.set(nodeId, nodeHistory)

    // Store in Neo4j if available
    if (this.neo4jDriver) {
      await this.storeTemporalNodeInGraph(temporalNode)
    }

    console.log(`🕒 Created temporal node: ${nodeType} (${nodeId})`)
    return temporalNode
  }

  /**
   * Update a temporal node (creates new version)
   */
  async updateTemporalNode(
    nodeId: string,
    properties: Record<string, any>,
    options: {
      validFrom?: Date
      validTo?: Date
      changeReason?: string
      changedBy?: string
    } = {}
  ): Promise<TemporalNode> {
    const nodeHistory = this.temporalNodes.get(nodeId)
    if (!nodeHistory || nodeHistory.length === 0) {
      throw new Error(`Temporal node ${nodeId} not found`)
    }

    const currentNode = nodeHistory[nodeHistory.length - 1]
    const now = new Date()

    // Close current version
    currentNode.validTo = options.validFrom || now

    // Create new version
    const newVersion = this.incrementVersion(currentNode.version)
    const updatedNode: TemporalNode = {
      id: nodeId,
      nodeType: currentNode.nodeType,
      properties: { ...currentNode.properties, ...properties },
      validFrom: options.validFrom || now,
      validTo: options.validTo,
      transactionTime: now,
      version: newVersion,
      previousVersion: currentNode.version,
      changeType: 'UPDATE',
      changeReason: options.changeReason,
      changedBy: options.changedBy
    }

    // Store new version
    nodeHistory.push(updatedNode)
    this.temporalNodes.set(nodeId, nodeHistory)

    // Store in Neo4j if available
    if (this.neo4jDriver) {
      await this.storeTemporalNodeInGraph(updatedNode)
    }

    console.log(`🕒 Updated temporal node: ${nodeId} (v${newVersion})`)
    return updatedNode
  }

  /**
   * Create a temporal relationship
   */
  async createTemporalRelationship(
    fromNodeId: string,
    toNodeId: string,
    relationshipType: string,
    properties: Record<string, any> = {},
    options: {
      validFrom?: Date
      validTo?: Date
      changeReason?: string
    } = {}
  ): Promise<TemporalRelationship> {
    const now = new Date()
    const relationshipId = `${fromNodeId}_${relationshipType}_${toNodeId}_${Date.now()}`

    const temporalRelationship: TemporalRelationship = {
      id: relationshipId,
      fromNodeId,
      toNodeId,
      relationshipType,
      properties,
      validFrom: options.validFrom || now,
      validTo: options.validTo,
      transactionTime: now,
      version: '1.0.0',
      changeType: 'CREATE'
    }

    // Store in memory
    const relationshipHistory = this.temporalRelationships.get(relationshipId) || []
    relationshipHistory.push(temporalRelationship)
    this.temporalRelationships.set(relationshipId, relationshipHistory)

    // Store in Neo4j if available
    if (this.neo4jDriver) {
      await this.storeTemporalRelationshipInGraph(temporalRelationship)
    }

    console.log(`🕒 Created temporal relationship: ${relationshipType} (${relationshipId})`)
    return temporalRelationship
  }

  /**
   * Query temporal graph at specific time
   */
  async queryTemporalGraph(
    query: string,
    asOfTime?: Date,
    options: {
      includeHistory?: boolean
      maxVersions?: number
    } = {}
  ): Promise<any[]> {
    const targetTime = asOfTime || new Date()
    const results: any[] = []

    try {
      // Query nodes valid at target time
      for (const [nodeId, nodeHistory] of this.temporalNodes.entries()) {
        const validNodes = nodeHistory.filter(node =>
          node.validFrom <= targetTime &&
          (!node.validTo || node.validTo > targetTime)
        )

        if (validNodes.length > 0) {
          const currentNode = validNodes[validNodes.length - 1]

          // Simple text matching for now (could be enhanced with proper query parsing)
          const nodeStr = JSON.stringify(currentNode).toLowerCase()
          if (nodeStr.includes(query.toLowerCase())) {
            results.push({
              type: 'node',
              data: currentNode,
              history: options.includeHistory ? nodeHistory.slice(0, options.maxVersions || 10) : undefined
            })
          }
        }
      }

      // Query relationships valid at target time
      for (const [relationshipId, relationshipHistory] of this.temporalRelationships.entries()) {
        const validRelationships = relationshipHistory.filter(rel =>
          rel.validFrom <= targetTime &&
          (!rel.validTo || rel.validTo > targetTime)
        )

        if (validRelationships.length > 0) {
          const currentRelationship = validRelationships[validRelationships.length - 1]

          const relationshipStr = JSON.stringify(currentRelationship).toLowerCase()
          if (relationshipStr.includes(query.toLowerCase())) {
            results.push({
              type: 'relationship',
              data: currentRelationship,
              history: options.includeHistory ? relationshipHistory.slice(0, options.maxVersions || 10) : undefined
            })
          }
        }
      }

      console.log(`🕒 Temporal query returned ${results.length} results for time: ${targetTime.toISOString()}`)
      return results
    } catch (error) {
      console.error('Temporal graph query failed:', error)
      return []
    }
  }

  /**
   * Store temporal node in Neo4j graph
   */
  private async storeTemporalNodeInGraph(node: TemporalNode): Promise<void> {
    if (!this.neo4jDriver) return

    const session = this.neo4jDriver.session()
    try {
      const query = `
        CREATE (n:TemporalNode:${node.nodeType} {
          id: $id,
          nodeType: $nodeType,
          properties: $properties,
          validFrom: datetime($validFrom),
          validTo: $validTo,
          transactionTime: datetime($transactionTime),
          version: $version,
          previousVersion: $previousVersion,
          changeType: $changeType,
          changeReason: $changeReason,
          changedBy: $changedBy
        })
        RETURN n
      `

      await session.run(query, {
        id: node.id,
        nodeType: node.nodeType,
        properties: JSON.stringify(node.properties),
        validFrom: node.validFrom.toISOString(),
        validTo: node.validTo?.toISOString(),
        transactionTime: node.transactionTime.toISOString(),
        version: node.version,
        previousVersion: node.previousVersion,
        changeType: node.changeType,
        changeReason: node.changeReason,
        changedBy: node.changedBy
      })
    } catch (error) {
      console.error('Failed to store temporal node in graph:', error)
    } finally {
      await session.close()
    }
  }

  /**
   * Store temporal relationship in Neo4j graph
   */
  private async storeTemporalRelationshipInGraph(relationship: TemporalRelationship): Promise<void> {
    if (!this.neo4jDriver) return

    const session = this.neo4jDriver.session()
    try {
      const query = `
        MATCH (from:TemporalNode {id: $fromNodeId})
        MATCH (to:TemporalNode {id: $toNodeId})
        CREATE (from)-[r:${relationship.relationshipType} {
          id: $id,
          properties: $properties,
          validFrom: datetime($validFrom),
          validTo: $validTo,
          transactionTime: datetime($transactionTime),
          version: $version,
          changeType: $changeType
        }]->(to)
        RETURN r
      `

      await session.run(query, {
        id: relationship.id,
        fromNodeId: relationship.fromNodeId,
        toNodeId: relationship.toNodeId,
        properties: JSON.stringify(relationship.properties),
        validFrom: relationship.validFrom.toISOString(),
        validTo: relationship.validTo?.toISOString(),
        transactionTime: relationship.transactionTime.toISOString(),
        version: relationship.version,
        changeType: relationship.changeType
      })
    } catch (error) {
      console.error('Failed to store temporal relationship in graph:', error)
    } finally {
      await session.close()
    }
  }

  /**
   * Initialize temporal graph schema in Neo4j
   */
  private async initializeTemporalGraphSchema(): Promise<void> {
    if (!this.neo4jDriver) return

    const session = this.neo4jDriver.session()
    try {
      // Create indexes for temporal queries
      const indexes = [
        'CREATE INDEX temporal_node_id IF NOT EXISTS FOR (n:TemporalNode) ON (n.id)',
        'CREATE INDEX temporal_node_valid_from IF NOT EXISTS FOR (n:TemporalNode) ON (n.validFrom)',
        'CREATE INDEX temporal_node_valid_to IF NOT EXISTS FOR (n:TemporalNode) ON (n.validTo)',
        'CREATE INDEX temporal_node_transaction_time IF NOT EXISTS FOR (n:TemporalNode) ON (n.transactionTime)',
        'CREATE INDEX temporal_node_version IF NOT EXISTS FOR (n:TemporalNode) ON (n.version)'
      ]

      for (const index of indexes) {
        try {
          await session.run(index)
        } catch (error) {
          console.debug('Temporal index creation skipped:', error.message)
        }
      }

      console.log('🕒 Temporal graph schema initialized')
    } catch (error) {
      console.error('Failed to initialize temporal graph schema:', error)
    } finally {
      await session.close()
    }
  }

  /**
   * Increment version string
   */
  private incrementVersion(version: string): string {
    const parts = version.split('.')
    const patch = parseInt(parts[2] || '0') + 1
    return `${parts[0]}.${parts[1]}.${patch}`
  }

  /**
   * Get temporal node history
   */
  async getTemporalNodeHistory(nodeId: string): Promise<TemporalNode[]> {
    return this.temporalNodes.get(nodeId) || []
  }

  /**
   * Get temporal relationship history
   */
  async getTemporalRelationshipHistory(relationshipId: string): Promise<TemporalRelationship[]> {
    return this.temporalRelationships.get(relationshipId) || []
  }

  /**
   * Get temporal graph statistics
   */
  getTemporalGraphStats(): {
    totalNodes: number
    totalRelationships: number
    totalVersions: number
    oldestEntry: Date | null
    newestEntry: Date | null
  } {
    let totalVersions = 0
    let oldestEntry: Date | null = null
    let newestEntry: Date | null = null

    // Count node versions
    for (const nodeHistory of this.temporalNodes.values()) {
      totalVersions += nodeHistory.length
      for (const node of nodeHistory) {
        if (!oldestEntry || node.transactionTime < oldestEntry) {
          oldestEntry = node.transactionTime
        }
        if (!newestEntry || node.transactionTime > newestEntry) {
          newestEntry = node.transactionTime
        }
      }
    }

    // Count relationship versions
    for (const relationshipHistory of this.temporalRelationships.values()) {
      totalVersions += relationshipHistory.length
      for (const relationship of relationshipHistory) {
        if (!oldestEntry || relationship.transactionTime < oldestEntry) {
          oldestEntry = relationship.transactionTime
        }
        if (!newestEntry || relationship.transactionTime > newestEntry) {
          newestEntry = relationship.transactionTime
        }
      }
    }

    return {
      totalNodes: this.temporalNodes.size,
      totalRelationships: this.temporalRelationships.size,
      totalVersions,
      oldestEntry,
      newestEntry
    }
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    try {
      if (this.neo4jDriver) {
        await this.neo4jDriver.close()
        this.neo4jDriver = null
      }

      if (this.redis) {
        this.redis.disconnect()
        this.redis = null
      }

      // Clear memory stores
      this.memoryStore.clear()
      this.decisionGraph.clear()
      this.contextVersions.clear()
      this.crossReferences.clear()
      this.registeredAgents.clear()
      this.sharedState.clear()
      this.agentCommunication.clear()

      // Clear temporal graph stores
      this.temporalNodes.clear()
      this.temporalRelationships.clear()
      this.temporalQueries.clear()

      this.isInitialized = false
      this.emit('cleanup_completed')

    } catch (error) {
      this.emit('cleanup_failed', error)
      throw error
    }
  }
}
