import { z } from 'zod'

/**
 * E2B Integration Schema Types
 * 
 * Defines the schema for E2B sandbox creation and management
 * Compatible with the existing AG3NT framework and coding workflow
 */

// File schema for multi-file applications
export const FileSchema = z.object({
  file_path: z.string(),
  file_name: z.string().optional(),
  file_content: z.string(),
  file_type: z.string().optional(),
})

export type File = z.infer<typeof FileSchema>

// Architecture schema for project structure
export const ArchitectureSchema = z.object({
  frontend: z.string(),
  backend: z.string().optional(),
  database: z.string().optional(),
  deployment: z.string().optional(),
})

export type Architecture = z.infer<typeof ArchitectureSchema>

// Fragment schema for E2B sandbox creation
export const FragmentSchema = z.object({
  // Basic properties
  title: z.string(),
  description: z.string().optional(),
  template: z.string(),
  
  // Single file support (legacy)
  code: z.string().optional(),
  file_path: z.string().optional(),
  
  // Multi-file support
  is_multi_file: z.boolean().default(false),
  files: z.array(FileSchema).optional(),
  
  // Dependencies
  has_additional_dependencies: z.boolean().default(false),
  additional_dependencies: z.array(z.string()).optional(),
  install_dependencies_command: z.string().optional(),
  
  // Architecture and metadata
  architecture: ArchitectureSchema.optional(),
  api_routes: z.array(z.string()).optional(),
  port: z.number().default(80),
  
  // Project metadata
  project_type: z.string().optional(),
  framework: z.string().optional(),
  language: z.string().optional(),
  
  // E2B specific
  sandbox_id: z.string().optional(),
  url: z.string().optional(),
})

export type FragmentSchema = z.infer<typeof FragmentSchema>

// Execution result types
export const ExecutionResultInterpreterSchema = z.object({
  sbxId: z.string(),
  template: z.string(),
  stdout: z.string(),
  stderr: z.string(),
  runtimeError: z.any().optional(),
  cellResults: z.array(z.any()),
})

export const ExecutionResultWebSchema = z.object({
  sbxId: z.string(),
  template: z.string(),
  url: z.string(),
})

export type ExecutionResultInterpreter = z.infer<typeof ExecutionResultInterpreterSchema>
export type ExecutionResultWeb = z.infer<typeof ExecutionResultWebSchema>
export type ExecutionResult = ExecutionResultInterpreter | ExecutionResultWeb

// Sandbox request schema
export const SandboxRequestSchema = z.object({
  fragment: FragmentSchema,
  userID: z.string().optional(),
  teamID: z.string().optional(),
  accessToken: z.string().optional(),
})

export type SandboxRequest = z.infer<typeof SandboxRequestSchema>

// Coding task integration with E2B
export const CodingTaskSchema = z.object({
  id: z.string(),
  type: z.enum(['frontend', 'backend', 'database', 'testing', 'deployment', 'integration']),
  title: z.string(),
  description: z.string(),
  status: z.enum(['pending', 'in_progress', 'completed', 'failed']),
  priority: z.enum(['low', 'medium', 'high', 'critical']),
  dependencies: z.array(z.string()).default([]),
  estimatedTime: z.number().optional(),
  startTime: z.number().optional(),
  endTime: z.number().optional(),
  output: z.object({
    files: z.array(z.string()).optional(),
    code: z.string().optional(),
    tests: z.array(z.string()).optional(),
    documentation: z.string().optional(),
    metrics: z.record(z.any()).optional(),
  }).optional(),
  agent: z.string().optional(),
  error: z.string().optional(),
})

export type CodingTask = z.infer<typeof CodingTaskSchema>

// Coding workflow state
export const CodingWorkflowStateSchema = z.object({
  isRunning: z.boolean(),
  currentPhase: z.enum(['planning', 'setup', 'coding', 'testing', 'deployment', 'completed']),
  tasks: z.array(CodingTaskSchema),
  completedTasks: z.number(),
  totalTasks: z.number(),
  progress: z.number(),
  startTime: z.number().optional(),
  endTime: z.number().optional(),
  projectPlan: z.any().optional(),
  sandboxes: z.array(z.object({
    id: z.string(),
    template: z.string(),
    url: z.string().optional(),
    status: z.enum(['creating', 'ready', 'error']),
  })).default([]),
})

export type CodingWorkflowState = z.infer<typeof CodingWorkflowStateSchema>

// Streaming update types for real-time progress
export const StreamingUpdateSchema = z.object({
  type: z.enum(['file', 'analysis', 'generation', 'completion', 'error']),
  message: z.string(),
  fileName: z.string().optional(),
  progress: z.number().optional(),
  timestamp: z.number(),
  taskId: z.string().optional(),
  agentId: z.string().optional(),
})

export type StreamingUpdate = z.infer<typeof StreamingUpdateSchema>

// Agent output for E2B integration
export const AgentOutputSchema = z.object({
  agentId: z.string(),
  taskId: z.string(),
  type: z.enum(['code', 'file', 'test', 'documentation', 'configuration']),
  content: z.string(),
  metadata: z.record(z.any()).optional(),
  timestamp: z.number(),
})

export type AgentOutput = z.infer<typeof AgentOutputSchema>

// Template definitions for E2B sandboxes
export const TemplateSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  framework: z.string(),
  language: z.string(),
  port: z.number(),
  supportedFeatures: z.array(z.string()),
  defaultFiles: z.array(FileSchema).optional(),
  setupCommands: z.array(z.string()).optional(),
})

export type Template = z.infer<typeof TemplateSchema>

// Available templates
export const AVAILABLE_TEMPLATES: Template[] = [
  {
    id: 'nextjs-developer',
    name: 'Next.js Developer',
    description: 'Full-stack Next.js application with TypeScript',
    framework: 'nextjs',
    language: 'typescript',
    port: 3000,
    supportedFeatures: ['react', 'typescript', 'tailwind', 'api-routes', 'ssr'],
  },
  {
    id: 'react-developer',
    name: 'React Developer',
    description: 'React application with TypeScript and Vite',
    framework: 'react',
    language: 'typescript',
    port: 5173,
    supportedFeatures: ['react', 'typescript', 'vite', 'tailwind'],
  },
  {
    id: 'code-interpreter-v1',
    name: 'Code Interpreter',
    description: 'Python code execution environment',
    framework: 'python',
    language: 'python',
    port: 8000,
    supportedFeatures: ['python', 'jupyter', 'data-science', 'visualization'],
  },
]

// Error types for E2B operations
export const E2BErrorSchema = z.object({
  code: z.string(),
  message: z.string(),
  details: z.record(z.any()).optional(),
  timestamp: z.number(),
  sandboxId: z.string().optional(),
})

export type E2BError = z.infer<typeof E2BErrorSchema>

// Utility types for better type safety
export type SandboxStatus = 'creating' | 'ready' | 'error'
export type TaskType = 'frontend' | 'backend' | 'database' | 'testing' | 'deployment' | 'integration'
export type TaskStatus = 'pending' | 'in_progress' | 'completed' | 'failed'
export type UpdateType = 'file' | 'analysis' | 'generation' | 'completion' | 'error'

// Enhanced interfaces for better type checking
export interface E2BSandboxInfo {
  id: string
  template: string
  url?: string
  status: SandboxStatus
  taskId?: string
  title?: string
  description?: string
  framework?: string
  language?: string
  port?: number
  createdAt?: number
  lastUpdated?: number
}

export interface CodingTaskExtended {
  id: string
  type: TaskType
  title: string
  description: string
  status: TaskStatus
  priority: 'low' | 'medium' | 'high' | 'critical'
  dependencies: string[]
  estimatedTime?: number
  startTime?: number
  endTime?: number
  output?: {
    files?: string[]
    code?: string
    tests?: string[]
    documentation?: string
    metrics?: Record<string, any>
    sandboxId?: string
    sandboxUrl?: string
  }
  agent?: string
  error?: string
}

// API Response types
export interface APIResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
  timestamp?: string
}

export interface SandboxAPIResponse extends APIResponse {
  data?: {
    sbxId: string
    template: string
    url?: string
    stdout?: string
    stderr?: string
    runtimeError?: any
    cellResults?: any[]
  }
}
