/**
 * AG3NT Framework - Simple API
 * 
 * Simplified, developer-friendly API for common framework operations.
 * This provides an easy-to-use interface for the most common use cases
 * while still allowing access to the full API when needed.
 * 
 * Features:
 * - Simplified method signatures
 * - Sensible defaults
 * - Fluent interface patterns
 * - Common use case shortcuts
 * - Type-safe operations
 */

import { AG3NTFramework } from "../ag3nt-framework"
import { FrameworkAPI } from "./framework-api-impl"
import { BaseAgent } from "./base-agent"
import { PlanningAgent } from "../agents/planning-agent"
import {
  IAgent,
  ExecutionResult,
  AgentHealthStatus,
  FrameworkHealth,
  FrameworkMetrics
} from "./framework-api"

/**
 * Simple API - Easy-to-use interface for common operations
 */
export class SimpleAPI {
  private framework: AG3NTFramework
  private api: FrameworkAPI

  constructor(framework?: AG3NTFramework) {
    this.framework = framework || new AG3NTFramework()
    this.api = new FrameworkAPI(this.framework)
  }

  // ============================================================================
  // LIFECYCLE OPERATIONS
  // ============================================================================

  /**
   * Initialize the framework with sensible defaults
   */
  async start(options: {
    enableMCP?: boolean
    enableCommunication?: boolean
    enableWorkflows?: boolean
    autoRegisterBuiltins?: boolean
  } = {}): Promise<void> {
    const config = {
      context: {
        enableMCP: options.enableMCP ?? true,
        enableRAG: true,
        enableEnrichment: true
      },
      communication: {
        enableRealTime: options.enableCommunication ?? true,
        heartbeatInterval: 30000
      },
      workflows: {
        enablePersistence: options.enableWorkflows ?? true,
        maxConcurrentExecutions: 10
      },
      agents: {
        autoRegisterBuiltins: options.autoRegisterBuiltins ?? true,
        maxConcurrentSessions: 5
      }
    }

    await this.api.initialize(config)
  }

  /**
   * Shutdown the framework
   */
  async stop(): Promise<void> {
    await this.api.shutdown()
  }

  /**
   * Check if framework is ready
   */
  isReady(): boolean {
    return this.api.isInitialized()
  }

  // ============================================================================
  // AGENT OPERATIONS
  // ============================================================================

  /**
   * Create and register a planning agent
   */
  async createPlanningAgent(options: {
    name?: string
    description?: string
    tags?: string[]
  } = {}): Promise<string> {
    const agent = new PlanningAgent()
    return await this.api.agents.register(agent, {
      description: options.description || 'AI-powered project planning agent',
      tags: options.tags || ['planning', 'analysis'],
      priority: 10
    })
  }

  /**
   * Register a custom agent
   */
  async registerAgent(agent: IAgent, options: {
    description?: string
    tags?: string[]
    priority?: number
    autoStart?: boolean
  } = {}): Promise<string> {
    return await this.api.agents.register(agent, {
      description: options.description || `${agent.type} agent`,
      tags: options.tags || [],
      priority: options.priority || 5,
      autoStart: options.autoStart ?? true
    })
  }

  /**
   * Execute a task with the best available agent
   */
  async execute(agentType: string, input: any, options: {
    priority?: 'low' | 'medium' | 'high' | 'critical'
    timeout?: number
    tags?: string[]
  } = {}): Promise<ExecutionResult> {
    return await this.api.agents.execute(agentType, input, {
      priority: options.priority || 'medium',
      timeout: options.timeout || 300000, // 5 minutes
      tags: options.tags || []
    })
  }

  /**
   * Plan a project using the planning agent
   */
  async planProject(prompt: string, options: {
    interactive?: boolean
    priority?: 'low' | 'medium' | 'high' | 'critical'
    designStyleGuide?: any
    hasImages?: boolean
  } = {}): Promise<ExecutionResult> {
    return await this.execute('planning-agent', {
      prompt,
      isInteractive: options.interactive || false,
      designStyleGuide: options.designStyleGuide,
      hasImages: options.hasImages || false
    }, {
      priority: options.priority || 'high'
    })
  }

  /**
   * Get all registered agents
   */
  async getAgents(): Promise<Array<{
    id: string
    type: string
    status: string
    health: string
    description: string
  }>> {
    const agents = await this.api.agents.list()
    return agents.map(agent => ({
      id: agent.agentId,
      type: agent.agentType,
      status: agent.status.state,
      health: agent.healthCheck.status,
      description: agent.metadata.description
    }))
  }

  /**
   * Get agent health status
   */
  async getAgentHealth(agentId: string): Promise<AgentHealthStatus> {
    return await this.api.agents.getHealth(agentId)
  }

  // ============================================================================
  // COMMUNICATION OPERATIONS
  // ============================================================================

  /**
   * Send a message between agents
   */
  async sendMessage(from: string, to: string, message: any, options: {
    priority?: 'low' | 'normal' | 'high' | 'critical'
    requiresResponse?: boolean
  } = {}): Promise<string> {
    return await this.api.communication.sendMessage(
      from,
      to,
      'request',
      message,
      {
        priority: options.priority || 'normal',
        requiresAck: options.requiresResponse || false
      }
    )
  }

  /**
   * Broadcast a message to all agents
   */
  async broadcast(from: string, message: any, options: {
    priority?: 'low' | 'normal' | 'high' | 'critical'
    excludeAgents?: string[]
  } = {}): Promise<string> {
    return await this.api.communication.broadcast(
      from,
      'broadcast',
      message,
      {
        priority: options.priority || 'normal',
        excludeAgents: options.excludeAgents || []
      }
    )
  }

  /**
   * Create a communication channel
   */
  async createChannel(name: string, options: {
    purpose?: string
    persistent?: boolean
    maxParticipants?: number
  } = {}): Promise<string> {
    return await this.api.communication.createChannel('coordination', {
      purpose: options.purpose || `${name} coordination channel`,
      persistent: options.persistent ?? true,
      maxParticipants: options.maxParticipants
    })
  }

  // ============================================================================
  // WORKFLOW OPERATIONS
  // ============================================================================

  /**
   * Create a simple workflow
   */
  async createWorkflow(name: string, steps: Array<{
    name: string
    agentType: string
    input?: any
    dependencies?: string[]
  }>): Promise<void> {
    const workflow = {
      workflowId: `workflow-${name.toLowerCase().replace(/\s+/g, '-')}`,
      name,
      description: `Auto-generated workflow: ${name}`,
      version: '1.0.0',
      steps: steps.map((step, index) => ({
        stepId: `step-${index + 1}`,
        name: step.name,
        agentType: step.agentType,
        requiredCapabilities: [],
        input: step.input || {},
        dependencies: step.dependencies || [],
        parallel: false,
        optional: false,
        timeout: 300000, // 5 minutes
        maxRetries: 3,
        onError: 'fail' as const
      })),
      dependencies: [],
      errorHandling: {
        strategy: 'fail_fast' as const,
        maxRetries: 3,
        retryDelay: 1000
      },
      timeout: 1800000, // 30 minutes
      maxRetries: 3
    }

    await this.api.workflows.register(workflow)
  }

  /**
   * Execute a workflow
   */
  async executeWorkflow(workflowId: string, input: any, options: {
    priority?: 'low' | 'medium' | 'high' | 'critical'
    tags?: string[]
  } = {}): Promise<string> {
    return await this.api.workflows.execute(workflowId, input, {
      priority: options.priority || 'medium',
      tags: options.tags || []
    })
  }

  /**
   * Get workflow execution status
   */
  async getWorkflowStatus(executionId: string): Promise<{
    status: string
    progress: number
    completedSteps: number
    totalSteps: number
    error?: string
  } | null> {
    const execution = await this.api.workflows.getExecution(executionId)
    if (!execution) return null

    const completedSteps = execution.steps.filter(s => s.status === 'completed').length
    const totalSteps = execution.steps.length
    const progress = totalSteps > 0 ? (completedSteps / totalSteps) * 100 : 0

    return {
      status: execution.status,
      progress,
      completedSteps,
      totalSteps,
      error: execution.error
    }
  }

  // ============================================================================
  // MONITORING OPERATIONS
  // ============================================================================

  /**
   * Get framework health
   */
  async getHealth(): Promise<FrameworkHealth> {
    return await this.api.monitoring.getHealth()
  }

  /**
   * Get framework metrics
   */
  async getMetrics(): Promise<FrameworkMetrics> {
    return await this.api.monitoring.getMetrics()
  }

  /**
   * Get simple status overview
   */
  async getStatus(): Promise<{
    healthy: boolean
    agents: { total: number, active: number, healthy: number }
    communication: { activeChannels: number, messagesPerSecond: number }
    workflows: { activeExecutions: number }
    uptime: number
  }> {
    const health = await this.getHealth()
    const metrics = await this.getMetrics()

    return {
      healthy: health.status === 'healthy',
      agents: metrics.agents,
      communication: {
        activeChannels: metrics.communication.activeChannels,
        messagesPerSecond: metrics.communication.messagesPerSecond
      },
      workflows: {
        activeExecutions: metrics.workflows.activeExecutions
      },
      uptime: metrics.system.uptime
    }
  }

  // ============================================================================
  // UTILITY OPERATIONS
  // ============================================================================

  /**
   * Wait for framework to be ready
   */
  async waitForReady(timeout: number = 30000): Promise<void> {
    const startTime = Date.now()
    
    while (!this.isReady() && (Date.now() - startTime) < timeout) {
      await new Promise(resolve => setTimeout(resolve, 100))
    }
    
    if (!this.isReady()) {
      throw new Error(`Framework failed to initialize within ${timeout}ms`)
    }
  }

  /**
   * Get framework version and capabilities
   */
  getInfo(): {
    version: string
    capabilities: string[]
    initialized: boolean
  } {
    return {
      version: this.api.getVersion(),
      capabilities: [
        'MCP_ENHANCED',
        'SEQUENTIAL_THINKING',
        'CONTEXT_ENRICHMENT',
        'RAG_INTEGRATION',
        'AGENT_COORDINATION',
        'REAL_TIME_COMMUNICATION',
        'MULTI_AGENT_WORKFLOWS',
        'EVENT_DRIVEN_ARCHITECTURE'
      ],
      initialized: this.isReady()
    }
  }

  /**
   * Access the full API for advanced operations
   */
  get fullAPI(): FrameworkAPI {
    return this.api
  }

  /**
   * Access the underlying framework
   */
  get framework(): AG3NTFramework {
    return this.framework
  }
}

// ============================================================================
// CONVENIENCE EXPORTS
// ============================================================================

/**
 * Create a new simple API instance
 */
export function createSimpleAPI(framework?: AG3NTFramework): SimpleAPI {
  return new SimpleAPI(framework)
}

/**
 * Quick start function for common use cases
 */
export async function quickStart(options: {
  enableMCP?: boolean
  enableCommunication?: boolean
  enableWorkflows?: boolean
  autoRegisterBuiltins?: boolean
} = {}): Promise<SimpleAPI> {
  const api = new SimpleAPI()
  await api.start(options)
  return api
}

/**
 * Plan a project with minimal setup
 */
export async function quickPlan(prompt: string, options: {
  interactive?: boolean
  designStyleGuide?: any
} = {}): Promise<ExecutionResult> {
  const api = await quickStart()
  return await api.planProject(prompt, options)
}

// Export the main API class
export default SimpleAPI
