/**
 * AG3NT Framework - Maintenance Agent
 * 
 * Specialized agent for handling upgrades, dependency management,
 * bug triage, and legacy refactorings.
 * 
 * Features:
 * - Dependency management and updates
 * - Legacy code refactoring
 * - Bug triage and prioritization
 * - Technical debt management
 * - Performance optimization
 * - Code quality improvements
 */

import { BaseAgent, AgentState, AgentConfig } from "../core/base-agent"
import { aiService } from "../../ai-service"

export interface MaintenanceInput {
  task: MaintenanceTask
  codebase: MaintenanceCodebase
  dependencies: DependencyInfo
  issues: IssueInfo
}

export interface MaintenanceTask {
  taskId: string
  type: 'dependency_update' | 'refactoring' | 'bug_triage' | 'debt_reduction' | 'optimization' | 'cleanup'
  title: string
  description: string
  priority: 'critical' | 'high' | 'medium' | 'low'
  scope: MaintenanceScope
  deadline?: string
}

export interface MaintenanceScope {
  modules: string[]
  dependencies: string[]
  issues: string[]
  codeAreas: string[]
  includeTests: boolean
  includeDocumentation: boolean
  includeLegacy: boolean
}

export interface MaintenanceCodebase {
  structure: CodebaseStructure
  metrics: CodebaseMetrics
  debt: TechnicalDebt
  legacy: LegacyCode
  performance: PerformanceIssues
}

export interface CodebaseStructure {
  modules: ModuleInfo[]
  files: FileInfo[]
  dependencies: DependencyUsage[]
  architecture: ArchitectureInfo
}

export interface ModuleInfo {
  name: string
  path: string
  size: number
  complexity: number
  maintainability: number
  testCoverage: number
  lastModified: string
  dependencies: string[]
  dependents: string[]
}

export interface FileInfo {
  path: string
  language: string
  size: number
  lines: number
  complexity: number
  duplication: number
  issues: CodeIssue[]
  lastModified: string
}

export interface CodeIssue {
  type: 'bug' | 'code_smell' | 'vulnerability' | 'performance' | 'maintainability'
  severity: 'critical' | 'major' | 'minor' | 'info'
  description: string
  location: CodeLocation
  suggestion: string
  effort: 'low' | 'medium' | 'high'
}

export interface CodeLocation {
  file: string
  line: number
  column: number
  function?: string
  class?: string
}

export interface DependencyUsage {
  name: string
  version: string
  usageCount: number
  locations: string[]
  critical: boolean
  outdated: boolean
}

export interface ArchitectureInfo {
  pattern: string
  layers: ArchitectureLayer[]
  violations: ArchitectureViolation[]
  coupling: CouplingMetrics
}

export interface ArchitectureLayer {
  name: string
  components: string[]
  dependencies: string[]
  violations: string[]
}

export interface ArchitectureViolation {
  type: string
  description: string
  components: string[]
  severity: 'high' | 'medium' | 'low'
  impact: string
}

export interface CouplingMetrics {
  afferent: number
  efferent: number
  instability: number
  abstractness: number
}

export interface CodebaseMetrics {
  size: SizeMetrics
  complexity: ComplexityMetrics
  quality: QualityMetrics
  maintainability: MaintainabilityMetrics
  performance: PerformanceMetrics
}

export interface SizeMetrics {
  totalFiles: number
  totalLines: number
  codeLines: number
  commentLines: number
  blankLines: number
}

export interface ComplexityMetrics {
  cyclomatic: number
  cognitive: number
  halstead: HalsteadMetrics
  nesting: number
}

export interface HalsteadMetrics {
  vocabulary: number
  length: number
  difficulty: number
  effort: number
  bugs: number
}

export interface QualityMetrics {
  duplication: number
  coverage: number
  violations: number
  debt: number
  rating: string
}

export interface MaintainabilityMetrics {
  index: number
  changeability: number
  stability: number
  testability: number
  understandability: number
}

export interface PerformanceMetrics {
  responseTime: number
  throughput: number
  memoryUsage: number
  cpuUsage: number
  bottlenecks: PerformanceBottleneck[]
}

export interface PerformanceBottleneck {
  type: string
  location: string
  impact: 'high' | 'medium' | 'low'
  description: string
  suggestion: string
}

export interface TechnicalDebt {
  total: DebtMetrics
  categories: DebtCategory[]
  items: DebtItem[]
  trends: DebtTrend[]
}

export interface DebtMetrics {
  amount: number
  ratio: number
  interest: number
  principal: number
}

export interface DebtCategory {
  name: string
  amount: number
  percentage: number
  items: number
  priority: 'high' | 'medium' | 'low'
}

export interface DebtItem {
  id: string
  type: string
  description: string
  location: string
  effort: number
  impact: string
  priority: 'high' | 'medium' | 'low'
  created: string
}

export interface DebtTrend {
  period: string
  added: number
  removed: number
  net: number
}

export interface LegacyCode {
  components: LegacyComponent[]
  patterns: LegacyPattern[]
  risks: LegacyRisk[]
  migration: MigrationPlan
}

export interface LegacyComponent {
  name: string
  path: string
  age: number
  complexity: number
  dependencies: string[]
  usage: string[]
  risks: string[]
  migrationEffort: 'low' | 'medium' | 'high'
}

export interface LegacyPattern {
  name: string
  description: string
  locations: string[]
  impact: string
  modernAlternative: string
}

export interface LegacyRisk {
  type: string
  description: string
  probability: 'low' | 'medium' | 'high'
  impact: 'low' | 'medium' | 'high'
  mitigation: string
}

export interface MigrationPlan {
  phases: MigrationPhase[]
  timeline: string
  resources: string[]
  risks: string[]
}

export interface MigrationPhase {
  name: string
  description: string
  components: string[]
  duration: string
  dependencies: string[]
  risks: string[]
}

export interface PerformanceIssues {
  bottlenecks: PerformanceBottleneck[]
  inefficiencies: PerformanceInefficiency[]
  optimizations: PerformanceOptimization[]
  monitoring: PerformanceMonitoring
}

export interface PerformanceInefficiency {
  type: string
  location: string
  description: string
  impact: string
  solution: string
  effort: 'low' | 'medium' | 'high'
}

export interface PerformanceOptimization {
  type: string
  description: string
  benefit: string
  implementation: string
  effort: 'low' | 'medium' | 'high'
  priority: 'high' | 'medium' | 'low'
}

export interface PerformanceMonitoring {
  metrics: string[]
  tools: string[]
  alerts: string[]
  dashboards: string[]
}

export interface DependencyInfo {
  current: CurrentDependencies
  outdated: OutdatedDependencies
  vulnerabilities: DependencyVulnerabilities
  conflicts: DependencyConflicts
  recommendations: DependencyRecommendations
}

export interface CurrentDependencies {
  direct: Dependency[]
  transitive: Dependency[]
  dev: Dependency[]
  peer: Dependency[]
}

export interface Dependency {
  name: string
  version: string
  latest: string
  type: 'runtime' | 'development' | 'peer' | 'optional'
  size: number
  license: string
  maintainer: string
  lastUpdate: string
  popularity: number
  security: SecurityInfo
}

export interface SecurityInfo {
  vulnerabilities: number
  score: number
  advisories: SecurityAdvisory[]
}

export interface SecurityAdvisory {
  id: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  title: string
  description: string
  patched: string
}

export interface OutdatedDependencies {
  major: Dependency[]
  minor: Dependency[]
  patch: Dependency[]
  breaking: Dependency[]
}

export interface DependencyVulnerabilities {
  critical: VulnerableDependency[]
  high: VulnerableDependency[]
  medium: VulnerableDependency[]
  low: VulnerableDependency[]
}

export interface VulnerableDependency {
  name: string
  version: string
  vulnerabilities: SecurityAdvisory[]
  fix: string
  effort: 'low' | 'medium' | 'high'
}

export interface DependencyConflicts {
  version: VersionConflict[]
  peer: PeerConflict[]
  license: LicenseConflict[]
}

export interface VersionConflict {
  dependency: string
  required: string[]
  resolved: string
  impact: string
}

export interface PeerConflict {
  dependency: string
  peer: string
  required: string
  installed: string
  impact: string
}

export interface LicenseConflict {
  dependency: string
  license: string
  conflict: string
  impact: string
}

export interface DependencyRecommendations {
  updates: UpdateRecommendation[]
  removals: RemovalRecommendation[]
  alternatives: AlternativeRecommendation[]
  optimizations: OptimizationRecommendation[]
}

export interface UpdateRecommendation {
  dependency: string
  from: string
  to: string
  type: 'major' | 'minor' | 'patch'
  breaking: boolean
  benefits: string[]
  risks: string[]
  effort: 'low' | 'medium' | 'high'
}

export interface RemovalRecommendation {
  dependency: string
  reason: string
  impact: string
  alternatives: string[]
  effort: 'low' | 'medium' | 'high'
}

export interface AlternativeRecommendation {
  current: string
  alternative: string
  benefits: string[]
  migration: string
  effort: 'low' | 'medium' | 'high'
}

export interface OptimizationRecommendation {
  type: string
  description: string
  benefit: string
  implementation: string
  effort: 'low' | 'medium' | 'high'
}

export interface IssueInfo {
  bugs: BugInfo[]
  features: FeatureRequest[]
  technical: TechnicalIssue[]
  triage: TriageInfo
}

export interface BugInfo {
  id: string
  title: string
  description: string
  severity: 'critical' | 'high' | 'medium' | 'low'
  priority: 'urgent' | 'high' | 'medium' | 'low'
  status: 'open' | 'in_progress' | 'resolved' | 'closed'
  assignee: string
  reporter: string
  created: string
  updated: string
  labels: string[]
  components: string[]
  reproduction: ReproductionInfo
  impact: ImpactInfo
}

export interface ReproductionInfo {
  steps: string[]
  environment: string
  frequency: 'always' | 'often' | 'sometimes' | 'rarely'
  conditions: string[]
}

export interface ImpactInfo {
  users: number
  severity: string
  workaround: string
  businessImpact: string
}

export interface FeatureRequest {
  id: string
  title: string
  description: string
  priority: 'high' | 'medium' | 'low'
  status: 'open' | 'in_progress' | 'completed' | 'rejected'
  requestor: string
  created: string
  votes: number
  effort: 'small' | 'medium' | 'large'
  value: 'high' | 'medium' | 'low'
}

export interface TechnicalIssue {
  id: string
  type: 'debt' | 'refactoring' | 'performance' | 'security' | 'maintenance'
  title: string
  description: string
  priority: 'high' | 'medium' | 'low'
  effort: 'small' | 'medium' | 'large'
  impact: 'high' | 'medium' | 'low'
  components: string[]
}

export interface TriageInfo {
  process: TriageProcess
  criteria: TriageCriteria
  automation: TriageAutomation
  metrics: TriageMetrics
}

export interface TriageProcess {
  steps: TriageStep[]
  roles: TriageRole[]
  sla: TriageSLA
}

export interface TriageStep {
  name: string
  description: string
  criteria: string[]
  actions: string[]
  automation: boolean
}

export interface TriageRole {
  name: string
  responsibilities: string[]
  permissions: string[]
}

export interface TriageSLA {
  critical: number
  high: number
  medium: number
  low: number
}

export interface TriageCriteria {
  severity: SeverityCriteria
  priority: PriorityCriteria
  assignment: AssignmentCriteria
}

export interface SeverityCriteria {
  critical: string[]
  high: string[]
  medium: string[]
  low: string[]
}

export interface PriorityCriteria {
  urgent: string[]
  high: string[]
  medium: string[]
  low: string[]
}

export interface AssignmentCriteria {
  expertise: string[]
  availability: string[]
  workload: string[]
}

export interface TriageAutomation {
  enabled: boolean
  rules: AutomationRule[]
  actions: AutomationAction[]
}

export interface AutomationRule {
  name: string
  condition: string
  action: string
  confidence: number
}

export interface AutomationAction {
  type: string
  description: string
  parameters: any
}

export interface TriageMetrics {
  throughput: number
  accuracy: number
  timeToTriage: number
  backlog: number
}

export interface MaintenanceResult {
  taskId: string
  status: 'completed' | 'failed' | 'partial'
  actions: MaintenanceAction[]
  updates: DependencyUpdate[]
  refactorings: RefactoringResult[]
  optimizations: OptimizationResult[]
  triage: TriageResult[]
  metrics: MaintenanceMetrics
  recommendations: MaintenanceRecommendation[]
}

export interface MaintenanceAction {
  type: 'update' | 'refactor' | 'optimize' | 'fix' | 'cleanup'
  description: string
  files: string[]
  impact: 'breaking' | 'major' | 'minor' | 'patch'
  effort: 'low' | 'medium' | 'high'
  status: 'completed' | 'failed' | 'skipped'
}

export interface DependencyUpdate {
  name: string
  from: string
  to: string
  type: 'major' | 'minor' | 'patch'
  breaking: boolean
  changes: string[]
  migration: string[]
  testing: string[]
}

export interface RefactoringResult {
  type: string
  description: string
  files: string[]
  before: CodeMetrics
  after: CodeMetrics
  improvement: ImprovementMetrics
}

export interface CodeMetrics {
  complexity: number
  maintainability: number
  duplication: number
  coverage: number
}

export interface ImprovementMetrics {
  complexity: number
  maintainability: number
  duplication: number
  coverage: number
}

export interface OptimizationResult {
  type: string
  description: string
  improvement: string
  measurement: PerformanceMeasurement
}

export interface PerformanceMeasurement {
  before: PerformanceData
  after: PerformanceData
  improvement: number
}

export interface PerformanceData {
  responseTime: number
  throughput: number
  memoryUsage: number
  cpuUsage: number
}

export interface TriageResult {
  issueId: string
  action: 'assigned' | 'prioritized' | 'closed' | 'escalated'
  assignee?: string
  priority?: string
  reasoning: string
  confidence: number
}

export interface MaintenanceMetrics {
  dependenciesUpdated: number
  vulnerabilitiesFixed: number
  debtReduced: number
  performanceImproved: number
  issuesTriaged: number
  codeQuality: number
}

export interface MaintenanceRecommendation {
  type: 'dependency' | 'refactoring' | 'performance' | 'security' | 'process'
  priority: 'high' | 'medium' | 'low'
  description: string
  rationale: string
  implementation: string
  effort: string
  impact: string
}

/**
 * Maintenance Agent - Code maintenance and dependency management
 */
export class MaintenanceAgent extends BaseAgent {
  private readonly maintenanceSteps = [
    'analyze_codebase', 'assess_dependencies', 'triage_issues',
    'plan_maintenance', 'update_dependencies', 'refactor_legacy',
    'optimize_performance', 'reduce_debt', 'validate_changes'
  ]

  constructor(config: Partial<AgentConfig> = {}) {
    super('maintenance', {
      capabilities: {
        requiredCapabilities: [
          'dependency_management',
          'code_refactoring',
          'bug_triage',
          'debt_reduction',
          'performance_optimization',
          'legacy_modernization',
          'quality_improvement'
        ],
        contextFilters: ['maintenance', 'dependencies', 'refactoring', 'debt', 'performance'],
        mcpEnhanced: true,
        sequentialThinking: true,
        contextEnrichment: true,
        ragIntegration: true
      },
      ...config
    })
  }

  /**
   * Execute maintenance workflow
   */
  protected async executeWorkflow(state: AgentState): Promise<AgentState> {
    const input = state.input as MaintenanceInput
    
    console.log(`🔧 Starting maintenance workflow: ${input.task.title}`)

    // Execute maintenance steps sequentially
    for (const stepId of this.maintenanceSteps) {
      console.log(`🔄 Executing step: ${stepId}`)
      
      const stepResult = await this.executeStepWithContext(stepId, input)
      
      if (stepResult.needsInput) {
        state.needsInput = true
        state.question = stepResult.question
        break
      }

      // Update progress
      state.metadata.completedSteps++
      state.metadata.lastUpdated = new Date().toISOString()
      
      this.emit('step_completed', {
        agentId: this.agentId,
        stepId,
        progress: state.metadata.completedSteps / state.metadata.totalSteps
      })
    }

    // Mark as completed if no input needed
    if (!state.needsInput) {
      state.completed = true
      console.log(`✅ Maintenance workflow completed: ${input.task.title}`)
    }

    return state
  }

  /**
   * Execute individual maintenance step with context enhancement
   */
  private async executeStepWithContext(stepId: string, input?: any): Promise<any> {
    // Get enhanced context from context engine
    const enhancedState = await this.getEnhancedContext(stepId)

    // Execute step with MCP enhancement
    switch (stepId) {
      case 'analyze_codebase':
        return await this.analyzeCodebaseWithMCP(enhancedState, input)
      case 'assess_dependencies':
        return await this.assessDependenciesWithMCP(enhancedState)
      case 'triage_issues':
        return await this.triageIssuesWithMCP(enhancedState)
      case 'plan_maintenance':
        return await this.planMaintenanceWithMCP(enhancedState)
      case 'update_dependencies':
        return await this.updateDependenciesWithMCP(enhancedState)
      case 'refactor_legacy':
        return await this.refactorLegacyWithMCP(enhancedState)
      case 'optimize_performance':
        return await this.optimizePerformanceWithMCP(enhancedState)
      case 'reduce_debt':
        return await this.reduceDebtWithMCP(enhancedState)
      case 'validate_changes':
        return await this.validateChangesWithMCP(enhancedState)
      default:
        throw new Error(`Unknown maintenance step: ${stepId}`)
    }
  }

  /**
   * Get total steps for progress tracking
   */
  protected getTotalSteps(): number {
    return this.maintenanceSteps.length
  }

  /**
   * Get relevant documentation for maintenance
   */
  protected async getRelevantDocumentation(): Promise<Record<string, any>> {
    return {
      maintenance: 'Software maintenance best practices and strategies',
      dependencies: 'Dependency management and update strategies',
      refactoring: 'Code refactoring techniques and patterns',
      debt: 'Technical debt management and reduction',
      performance: 'Performance optimization and monitoring',
      legacy: 'Legacy code modernization and migration'
    }
  }

  // ============================================================================
  // STEP IMPLEMENTATIONS WITH MCP ENHANCEMENT
  // ============================================================================

  private async analyzeCodebaseWithMCP(state: any, input: MaintenanceInput): Promise<any> {
    const analysis = await aiService.analyzeCodebaseForMaintenance(
      input.codebase,
      input.task.scope
    )

    this.state!.results.codebaseAnalysis = analysis
    
    return {
      results: analysis,
      needsInput: false,
      completed: false
    }
  }

  private async assessDependenciesWithMCP(state: any): Promise<any> {
    const dependencies = this.state!.input.dependencies
    
    const assessment = await aiService.assessDependencies(dependencies)

    this.state!.results.dependencyAssessment = assessment
    
    return {
      results: assessment,
      needsInput: false,
      completed: false
    }
  }

  private async triageIssuesWithMCP(state: any): Promise<any> {
    const issues = this.state!.input.issues
    
    const triage = await aiService.triageIssues(issues)

    this.state!.results.triage = triage
    
    return {
      results: triage,
      needsInput: false,
      completed: false
    }
  }

  private async planMaintenanceWithMCP(state: any): Promise<any> {
    const allAnalysis = {
      codebase: this.state!.results.codebaseAnalysis,
      dependencies: this.state!.results.dependencyAssessment,
      triage: this.state!.results.triage
    }
    
    const plan = await aiService.planMaintenanceStrategy(allAnalysis)

    this.state!.results.maintenancePlan = plan
    
    return {
      results: plan,
      needsInput: false,
      completed: false
    }
  }

  private async updateDependenciesWithMCP(state: any): Promise<any> {
    const plan = this.state!.results.maintenancePlan
    
    const updates = await aiService.updateDependencies(
      plan.dependencies,
      this.state!.input.dependencies
    )

    this.state!.results.dependencyUpdates = updates
    
    return {
      results: updates,
      needsInput: false,
      completed: false
    }
  }

  private async refactorLegacyWithMCP(state: any): Promise<any> {
    const plan = this.state!.results.maintenancePlan
    
    const refactoring = await aiService.refactorLegacyCode(
      plan.refactoring,
      this.state!.input.codebase.legacy
    )

    this.state!.results.refactoring = refactoring
    
    return {
      results: refactoring,
      needsInput: false,
      completed: false
    }
  }

  private async optimizePerformanceWithMCP(state: any): Promise<any> {
    const plan = this.state!.results.maintenancePlan
    
    const optimization = await aiService.optimizeCodePerformance(
      plan.optimization,
      this.state!.input.codebase.performance
    )

    this.state!.results.optimization = optimization
    
    return {
      results: optimization,
      needsInput: false,
      completed: false
    }
  }

  private async reduceDebtWithMCP(state: any): Promise<any> {
    const plan = this.state!.results.maintenancePlan
    
    const debtReduction = await aiService.reduceTechnicalDebt(
      plan.debt,
      this.state!.input.codebase.debt
    )

    this.state!.results.debtReduction = debtReduction
    
    return {
      results: debtReduction,
      needsInput: false,
      completed: false
    }
  }

  private async validateChangesWithMCP(state: any): Promise<any> {
    const allResults = {
      dependencies: this.state!.results.dependencyUpdates,
      refactoring: this.state!.results.refactoring,
      optimization: this.state!.results.optimization,
      debt: this.state!.results.debtReduction
    }
    
    const validation = await aiService.validateMaintenanceChanges(
      allResults,
      this.state!.input.task
    )

    this.state!.results.validation = validation
    
    return {
      results: validation,
      needsInput: false,
      completed: true // Final step
    }
  }
}

// Export for easy access
export { MaintenanceAgent as default }
