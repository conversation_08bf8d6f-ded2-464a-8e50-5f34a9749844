/**
 * AG3NT CLI - Create Project Command
 */

import fs from 'fs/promises'
import path from 'path'
import chalk from 'chalk'
import { execSync } from 'child_process'

export interface CreateOptions {
  template?: string
  directory?: string
  skipInstall?: boolean
  skipGit?: boolean
}

export async function createProject(projectName: string, options: CreateOptions): Promise<void> {
  const targetDir = options.directory || projectName
  const template = options.template || 'basic'

  // Create project directory
  await fs.mkdir(targetDir, { recursive: true })

  // Generate project files based on template
  await generateProjectFiles(targetDir, projectName, template)

  // Initialize git repository
  if (!options.skipGit) {
    try {
      execSync('git init', { cwd: targetDir, stdio: 'ignore' })
      execSync('git add .', { cwd: targetDir, stdio: 'ignore' })
      execSync('git commit -m "Initial commit"', { cwd: targetDir, stdio: 'ignore' })
    } catch (error) {
      console.warn(chalk.yellow('Warning: Failed to initialize git repository'))
    }
  }

  // Install dependencies
  if (!options.skipInstall) {
    try {
      console.log(chalk.cyan('Installing dependencies...'))
      execSync('npm install', { cwd: targetDir, stdio: 'inherit' })
    } catch (error) {
      console.warn(chalk.yellow('Warning: Failed to install dependencies'))
    }
  }
}

async function generateProjectFiles(targetDir: string, projectName: string, template: string): Promise<void> {
  // Generate package.json
  const packageJson = {
    name: projectName,
    version: '0.1.0',
    description: `AG3NT project: ${projectName}`,
    main: 'src/index.ts',
    scripts: {
      dev: 'tsx watch src/index.ts',
      build: 'tsc',
      start: 'node dist/index.js',
      test: 'jest',
      lint: 'eslint src --ext .ts'
    },
    dependencies: {
      '@ag3nt/framework': '^1.0.0'
    },
    devDependencies: {
      '@types/node': '^20.0.0',
      'typescript': '^5.0.0',
      'tsx': '^3.14.0',
      'jest': '^29.0.0',
      '@types/jest': '^29.0.0',
      'ts-jest': '^29.0.0',
      'eslint': '^8.0.0',
      '@typescript-eslint/eslint-plugin': '^6.0.0',
      '@typescript-eslint/parser': '^6.0.0'
    }
  }

  await fs.writeFile(
    path.join(targetDir, 'package.json'),
    JSON.stringify(packageJson, null, 2)
  )

  // Generate TypeScript config
  const tsConfig = {
    compilerOptions: {
      target: 'ES2020',
      module: 'commonjs',
      lib: ['ES2020'],
      outDir: './dist',
      rootDir: './src',
      strict: true,
      esModuleInterop: true,
      skipLibCheck: true,
      forceConsistentCasingInFileNames: true,
      declaration: true,
      declarationMap: true,
      sourceMap: true
    },
    include: ['src/**/*'],
    exclude: ['node_modules', 'dist']
  }

  await fs.writeFile(
    path.join(targetDir, 'tsconfig.json'),
    JSON.stringify(tsConfig, null, 2)
  )

  // Generate main application file based on template
  const mainContent = generateMainFile(template)
  await fs.mkdir(path.join(targetDir, 'src'), { recursive: true })
  await fs.writeFile(path.join(targetDir, 'src', 'index.ts'), mainContent)

  // Generate AG3NT configuration
  const ag3ntConfig = generateAG3NTConfig(template)
  await fs.writeFile(
    path.join(targetDir, 'ag3nt.config.json'),
    JSON.stringify(ag3ntConfig, null, 2)
  )

  // Generate README
  const readme = generateReadme(projectName, template)
  await fs.writeFile(path.join(targetDir, 'README.md'), readme)

  // Generate .gitignore
  const gitignore = `
node_modules/
dist/
.env
.env.local
*.log
.DS_Store
coverage/
.nyc_output/
.ag3nt/
`
  await fs.writeFile(path.join(targetDir, '.gitignore'), gitignore.trim())

  // Generate environment file
  const envContent = `
# AG3NT Framework Configuration
AG3NT_API_KEY=your-api-key-here
AG3NT_LOG_LEVEL=info
AG3NT_ENVIRONMENT=development

# OpenAI Configuration (if using OpenAI)
OPENAI_API_KEY=your-openai-key-here

# Other API Keys
# Add your API keys here
`
  await fs.writeFile(path.join(targetDir, '.env.example'), envContent.trim())
}

function generateMainFile(template: string): string {
  switch (template) {
    case 'basic':
      return `
import { AG3NTFramework, createPlanningAgent, createExecutorAgent } from '@ag3nt/framework'

async function main() {
  // Initialize AG3NT Framework
  const framework = new AG3NTFramework({
    contextEngine: {
      enableMCP: true,
      enableSequentialThinking: true
    }
  })

  await framework.initialize()

  // Create and register agents
  const planningAgent = createPlanningAgent()
  const executorAgent = createExecutorAgent()

  await framework.registerAgent(planningAgent)
  await framework.registerAgent(executorAgent)

  // Example: Run a simple workflow
  const result = await framework.executeWorkflow('basic-development', {
    task: 'Create a simple web application',
    requirements: ['React frontend', 'Node.js backend', 'REST API']
  })

  console.log('Workflow result:', result)

  // Shutdown framework
  await framework.shutdown()
}

main().catch(console.error)
`

    case 'advanced':
      return `
import { 
  AG3NTFramework, 
  createPlanningAgent, 
  createExecutorAgent,
  createFrontendCoderAgent,
  createBackendCoderAgent,
  createTesterAgent,
  AdvancedFeaturesManager
} from '@ag3nt/framework'

async function main() {
  // Initialize AG3NT Framework with advanced features
  const framework = new AG3NTFramework({
    contextEngine: {
      enableMCP: true,
      enableSequentialThinking: true,
      enableRAG: true,
      enableContextEnrichment: true
    },
    advancedFeatures: {
      adaptiveLearning: { enabled: true },
      temporalDatabase: { enabled: true },
      collaboration: { enabled: true },
      optimization: { enabled: true },
      marketplace: { enabled: true },
      monitoring: { enabled: true }
    }
  })

  await framework.initialize()

  // Create specialized agents
  const agents = [
    createPlanningAgent({ capabilities: ['advanced_planning', 'architecture_design'] }),
    createExecutorAgent({ capabilities: ['task_coordination', 'workflow_management'] }),
    createFrontendCoderAgent({ capabilities: ['react', 'vue', 'angular'] }),
    createBackendCoderAgent({ capabilities: ['node', 'python', 'go'] }),
    createTesterAgent({ capabilities: ['unit_testing', 'integration_testing', 'e2e_testing'] })
  ]

  // Register all agents
  for (const agent of agents) {
    await framework.registerAgent(agent)
  }

  // Start collaborative session
  const collaboration = framework.getCollaboration()
  if (collaboration) {
    const session = await collaboration.createSession(
      'advanced-development-task',
      'system',
      {
        description: 'Advanced multi-agent development session',
        priority: 'high'
      }
    )

    console.log('Collaborative session created:', session.sessionId)
  }

  // Example: Run advanced workflow
  const result = await framework.executeWorkflow('complete-project', {
    task: 'Build a full-stack application with microservices',
    requirements: [
      'React frontend with TypeScript',
      'Node.js microservices',
      'PostgreSQL database',
      'Docker containerization',
      'CI/CD pipeline',
      'Comprehensive testing'
    ],
    constraints: {
      timeline: '2 weeks',
      budget: 'medium',
      scalability: 'high'
    }
  })

  console.log('Advanced workflow result:', result)

  // Get insights and recommendations
  const insights = await framework.getAdvancedFeatures()?.getComprehensiveInsights()
  console.log('System insights:', insights)

  // Shutdown framework
  await framework.shutdown()
}

main().catch(console.error)
`

    case 'enterprise':
      return `
import { 
  AG3NTFramework,
  AdvancedFeaturesManager,
  createPlanningAgent,
  createExecutorAgent,
  createFrontendCoderAgent,
  createBackendCoderAgent,
  createTesterAgent,
  createReviewerAgent,
  createSecurityAgent,
  createDevOpsAgent,
  createDocumentationAgent
} from '@ag3nt/framework'

class EnterpriseAG3NTApplication {
  private framework: AG3NTFramework
  private advancedFeatures: AdvancedFeaturesManager

  constructor() {
    this.framework = new AG3NTFramework({
      contextEngine: {
        enableMCP: true,
        enableSequentialThinking: true,
        enableRAG: true,
        enableContextEnrichment: true
      },
      advancedFeatures: {
        adaptiveLearning: { 
          enabled: true,
          learningRate: 0.1,
          optimizationInterval: 3600000 // 1 hour
        },
        temporalDatabase: { 
          enabled: true,
          maxHistorySize: 1000000,
          compressionEnabled: true
        },
        collaboration: { 
          enabled: true,
          maxConcurrentAgents: 20,
          conflictResolutionStrategy: 'consensus'
        },
        optimization: { 
          enabled: true,
          strategy: 'hybrid',
          autoOptimization: true
        },
        marketplace: { 
          enabled: true,
          sandboxEnabled: true,
          securityScanningEnabled: true
        },
        monitoring: { 
          enabled: true,
          realTimeEnabled: true,
          predictiveAnalytics: true
        }
      }
    })
  }

  async initialize(): Promise<void> {
    await this.framework.initialize()
    this.advancedFeatures = this.framework.getAdvancedFeatures()!

    // Register enterprise agent suite
    await this.registerEnterpriseAgents()

    // Setup monitoring and alerting
    await this.setupMonitoring()

    // Initialize security policies
    await this.initializeSecurity()
  }

  private async registerEnterpriseAgents(): Promise<void> {
    const agents = [
      // Core development agents
      createPlanningAgent({ 
        capabilities: ['enterprise_planning', 'architecture_design', 'risk_assessment'] 
      }),
      createExecutorAgent({ 
        capabilities: ['enterprise_coordination', 'resource_management'] 
      }),
      
      // Development agents
      createFrontendCoderAgent({ 
        capabilities: ['react', 'vue', 'angular', 'enterprise_ui'] 
      }),
      createBackendCoderAgent({ 
        capabilities: ['microservices', 'enterprise_apis', 'scalability'] 
      }),
      
      // Quality assurance
      createTesterAgent({ 
        capabilities: ['enterprise_testing', 'performance_testing', 'security_testing'] 
      }),
      createReviewerAgent({ 
        capabilities: ['enterprise_review', 'compliance_check', 'security_review'] 
      }),
      
      // Operations and security
      createSecurityAgent({ 
        capabilities: ['vulnerability_scanning', 'compliance_audit', 'threat_detection'] 
      }),
      createDevOpsAgent({ 
        capabilities: ['enterprise_cicd', 'infrastructure_management', 'monitoring'] 
      }),
      createDocumentationAgent({ 
        capabilities: ['enterprise_docs', 'compliance_docs', 'api_documentation'] 
      })
    ]

    for (const agent of agents) {
      await this.framework.registerAgent(agent)
    }
  }

  private async setupMonitoring(): Promise<void> {
    const monitoring = this.advancedFeatures.getMonitoring()
    if (monitoring) {
      // Create enterprise dashboard
      monitoring.createDashboard({
        name: 'Enterprise Overview',
        description: 'Enterprise-wide system monitoring',
        panels: [
          {
            id: 'system-health',
            title: 'System Health',
            type: 'gauge',
            query: {
              metrics: ['system_health', 'agent_availability'],
              timeRange: { start: Date.now() - 3600000, end: Date.now() },
              filters: [],
              groupBy: [],
              aggregation: 'avg',
              interval: '1m'
            },
            visualization: {
              colors: ['#00ff00', '#ffff00', '#ff0000'],
              thresholds: [
                { value: 0.8, color: 'green', label: 'Healthy', operator: 'gt' },
                { value: 0.6, color: 'yellow', label: 'Warning', operator: 'gt' },
                { value: 0.4, color: 'red', label: 'Critical', operator: 'lt' }
              ],
              axes: [],
              legend: { show: true, position: 'bottom', alignment: 'center' },
              tooltip: { show: true, format: '{value}%', precision: 1 }
            },
            position: { x: 0, y: 0, width: 12, height: 6 },
            alerts: []
          }
        ],
        layout: { columns: 12, rowHeight: 200, margin: 10, responsive: true },
        filters: [],
        autoRefresh: 30000,
        permissions: ['admin', 'operator']
      })
    }
  }

  private async initializeSecurity(): Promise<void> {
    // Enterprise security initialization
    console.log('Initializing enterprise security policies...')
  }

  async executeEnterpriseWorkflow(workflowName: string, input: any): Promise<any> {
    console.log(\`Executing enterprise workflow: \${workflowName}\`)
    
    const result = await this.framework.executeWorkflow(workflowName, input)
    
    // Log execution for compliance
    const monitoring = this.advancedFeatures.getMonitoring()
    if (monitoring) {
      monitoring.recordMetric('workflow_execution', 1, {
        workflow: workflowName,
        status: result.status,
        duration: result.duration
      })
    }
    
    return result
  }

  async getEnterpriseInsights(): Promise<any> {
    return await this.advancedFeatures.getComprehensiveInsights()
  }

  async shutdown(): Promise<void> {
    await this.framework.shutdown()
  }
}

async function main() {
  const app = new EnterpriseAG3NTApplication()
  
  try {
    await app.initialize()
    
    // Example enterprise workflow
    const result = await app.executeEnterpriseWorkflow('enterprise-development', {
      project: 'Enterprise Application Suite',
      requirements: [
        'Microservices architecture',
        'High availability',
        'Security compliance',
        'Scalable infrastructure',
        'Comprehensive monitoring',
        'Automated testing',
        'Documentation'
      ],
      constraints: {
        compliance: ['SOC2', 'GDPR', 'HIPAA'],
        performance: 'enterprise-grade',
        security: 'maximum',
        scalability: 'unlimited'
      }
    })
    
    console.log('Enterprise workflow completed:', result)
    
    // Get insights
    const insights = await app.getEnterpriseInsights()
    console.log('Enterprise insights:', insights)
    
  } catch (error) {
    console.error('Enterprise application error:', error)
  } finally {
    await app.shutdown()
  }
}

main().catch(console.error)
`

    default:
      return generateMainFile('basic')
  }
}

function generateAG3NTConfig(template: string): any {
  const baseConfig = {
    version: '1.0.0',
    template,
    framework: {
      contextEngine: {
        enableMCP: true,
        enableSequentialThinking: true
      },
      agents: {
        maxConcurrentSessions: template === 'enterprise' ? 20 : template === 'advanced' ? 10 : 5,
        defaultTimeout: 300000
      }
    }
  }

  if (template === 'advanced' || template === 'enterprise') {
    baseConfig.framework['advancedFeatures'] = {
      adaptiveLearning: { enabled: true },
      temporalDatabase: { enabled: true },
      collaboration: { enabled: true },
      optimization: { enabled: true },
      marketplace: { enabled: template === 'enterprise' },
      monitoring: { enabled: true }
    }
  }

  return baseConfig
}

function generateReadme(projectName: string, template: string): string {
  return `
# ${projectName}

AG3NT Framework project created with template: ${template}

## Getting Started

1. Install dependencies:
   \`\`\`bash
   npm install
   \`\`\`

2. Copy environment file:
   \`\`\`bash
   cp .env.example .env
   \`\`\`

3. Configure your API keys in \`.env\`

4. Run the development server:
   \`\`\`bash
   npm run dev
   \`\`\`

## Available Scripts

- \`npm run dev\` - Start development server
- \`npm run build\` - Build for production
- \`npm start\` - Start production server
- \`npm test\` - Run tests
- \`npm run lint\` - Run linter

## AG3NT Commands

- \`ag3nt agent add <type>\` - Add a new agent
- \`ag3nt workflow run <name>\` - Run a workflow
- \`ag3nt docs\` - Generate documentation
- \`ag3nt validate\` - Validate project

## Learn More

- [AG3NT Framework Documentation](https://ag3nt.dev/docs)
- [API Reference](https://ag3nt.dev/api)
- [Examples](https://ag3nt.dev/examples)

## License

This project is licensed under the terms specified in the AG3NT Framework license.
`.trim()
}
