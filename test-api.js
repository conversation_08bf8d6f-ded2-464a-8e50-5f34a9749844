/**
 * Test API Endpoints
 */

const http = require('http')

console.log('🧪 Testing API endpoints...')

function testEndpoint(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    }

    const req = http.request(options, (res) => {
      let body = ''
      res.on('data', (chunk) => {
        body += chunk
      })
      res.on('end', () => {
        try {
          const result = JSON.parse(body)
          resolve({ status: res.statusCode, data: result })
        } catch (error) {
          resolve({ status: res.statusCode, data: body })
        }
      })
    })

    req.on('error', (error) => {
      reject(error)
    })

    if (data) {
      req.write(JSON.stringify(data))
    }

    req.end()
  })
}

async function runTests() {
  try {
    console.log('\n📊 Testing framework status...')
    const status = await testEndpoint('/api/framework?action=status')
    console.log('Status:', status.status, status.data)

    console.log('\n📈 Testing framework analytics...')
    const analytics = await testEndpoint('/api/framework?action=analytics')
    console.log('Analytics:', analytics.status, analytics.data)

    console.log('\n🤖 Testing framework agents...')
    const agents = await testEndpoint('/api/framework?action=agents')
    console.log('Agents:', agents.status, agents.data)

    console.log('\n🚀 Testing framework initialization...')
    const init = await testEndpoint('/api/framework', 'POST', { action: 'initialize' })
    console.log('Initialize:', init.status, init.data)

    console.log('\n✅ All tests completed!')

  } catch (error) {
    console.error('❌ Test failed:', error.message)
    console.log('Make sure the server is running: npm run dev')
  }
}

runTests()
