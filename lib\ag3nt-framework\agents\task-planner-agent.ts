/**
 * AG3NT Framework - Task Planner Agent
 * 
 * Specialized agent for breaking down high-level plans into executable tasks.
 * Works in coordination with PlanningAgent to create detailed task breakdowns.
 * 
 * Features:
 * - Task decomposition and dependency analysis
 * - Resource estimation and timeline planning
 * - Task prioritization and scheduling
 * - Integration with workflow coordination
 * - MCP-enhanced task analysis
 */

import { BaseAgent, AgentState, AgentConfig } from "../core/base-agent"
import { aiService } from "../../ai-service"

export interface TaskPlannerInput {
  projectPlan: any // Output from PlanningAgent
  requirements: any
  constraints?: {
    timeline?: string
    resources?: string[]
    budget?: number
  }
  preferences?: {
    methodology?: 'agile' | 'waterfall' | 'kanban'
    teamSize?: number
    sprintDuration?: number
  }
}

export interface TaskPlannerResult {
  tasks: Task[]
  dependencies: TaskDependency[]
  timeline: ProjectTimeline
  resources: ResourceAllocation[]
  milestones: Milestone[]
  risks: Risk[]
}

export interface Task {
  taskId: string
  title: string
  description: string
  type: 'development' | 'design' | 'testing' | 'deployment' | 'documentation'
  priority: 'critical' | 'high' | 'medium' | 'low'
  estimatedHours: number
  complexity: 'simple' | 'medium' | 'complex'
  requiredSkills: string[]
  assignedAgent?: string
  status: 'pending' | 'in_progress' | 'completed' | 'blocked'
  dependencies: string[]
  deliverables: string[]
  acceptanceCriteria: string[]
}

export interface TaskDependency {
  fromTask: string
  toTask: string
  type: 'finish_to_start' | 'start_to_start' | 'finish_to_finish'
  lag?: number // in hours
}

export interface ProjectTimeline {
  totalDuration: number // in hours
  phases: Phase[]
  criticalPath: string[]
  bufferTime: number
}

export interface Phase {
  phaseId: string
  name: string
  startDate: string
  endDate: string
  tasks: string[]
  deliverables: string[]
}

export interface ResourceAllocation {
  agentType: string
  requiredCapabilities: string[]
  utilizationPercentage: number
  peakDemandPeriod: string
}

export interface Milestone {
  milestoneId: string
  name: string
  description: string
  targetDate: string
  criteria: string[]
  dependencies: string[]
}

export interface Risk {
  riskId: string
  description: string
  probability: 'low' | 'medium' | 'high'
  impact: 'low' | 'medium' | 'high'
  mitigation: string
  contingency: string
}

/**
 * Task Planner Agent - Sophisticated task breakdown and project planning
 */
export class TaskPlannerAgent extends BaseAgent {
  private readonly planningSteps = [
    'analyze_plan', 'decompose_tasks', 'analyze_dependencies', 
    'estimate_effort', 'create_timeline', 'allocate_resources',
    'identify_risks', 'define_milestones', 'optimize_schedule'
  ]

  constructor(config: Partial<AgentConfig> = {}) {
    super('task-planner', {
      capabilities: {
        requiredCapabilities: [
          'task_decomposition',
          'dependency_analysis', 
          'effort_estimation',
          'resource_planning',
          'risk_assessment',
          'timeline_optimization'
        ],
        contextFilters: ['planning', 'tasks', 'resources', 'timeline'],
        mcpEnhanced: true,
        sequentialThinking: true,
        contextEnrichment: true,
        ragIntegration: true
      },
      ...config
    })
  }

  /**
   * Execute task planning workflow
   */
  protected async executeWorkflow(state: AgentState): Promise<AgentState> {
    const input = state.input as TaskPlannerInput
    
    console.log(`📋 Starting task planning for project: ${input.projectPlan?.summary?.title || 'Unknown Project'}`)

    // Execute planning steps sequentially
    for (const stepId of this.planningSteps) {
      console.log(`🔄 Executing step: ${stepId}`)
      
      const stepResult = await this.executeStepWithContext(stepId, input)
      
      if (stepResult.needsInput) {
        state.needsInput = true
        state.question = stepResult.question
        break
      }

      // Update progress
      state.metadata.completedSteps++
      state.metadata.lastUpdated = new Date().toISOString()
      
      this.emit('step_completed', {
        agentId: this.agentId,
        stepId,
        progress: state.metadata.completedSteps / state.metadata.totalSteps
      })
    }

    // Mark as completed if no input needed
    if (!state.needsInput) {
      state.completed = true
      console.log(`✅ Task planning completed with ${state.results.tasks?.length || 0} tasks`)
    }

    return state
  }

  /**
   * Execute individual planning step with context enhancement
   */
  private async executeStepWithContext(stepId: string, input?: any): Promise<any> {
    // Get enhanced context from context engine
    const enhancedState = await this.getEnhancedContext(stepId)

    // Execute step with MCP enhancement
    switch (stepId) {
      case 'analyze_plan':
        return await this.analyzePlanWithMCP(enhancedState, input)
      case 'decompose_tasks':
        return await this.decomposeTasksWithMCP(enhancedState)
      case 'analyze_dependencies':
        return await this.analyzeDependenciesWithMCP(enhancedState)
      case 'estimate_effort':
        return await this.estimateEffortWithMCP(enhancedState)
      case 'create_timeline':
        return await this.createTimelineWithMCP(enhancedState)
      case 'allocate_resources':
        return await this.allocateResourcesWithMCP(enhancedState)
      case 'identify_risks':
        return await this.identifyRisksWithMCP(enhancedState)
      case 'define_milestones':
        return await this.defineMilestonesWithMCP(enhancedState)
      case 'optimize_schedule':
        return await this.optimizeScheduleWithMCP(enhancedState)
      default:
        throw new Error(`Unknown task planning step: ${stepId}`)
    }
  }

  /**
   * Get total steps for progress tracking
   */
  protected getTotalSteps(): number {
    return this.planningSteps.length
  }

  /**
   * Get relevant documentation for task planning
   */
  protected async getRelevantDocumentation(): Promise<Record<string, any>> {
    return {
      taskPlanning: 'Task decomposition and project planning methodologies',
      agileMethodology: 'Agile development practices and sprint planning',
      resourcePlanning: 'Resource allocation and capacity planning',
      riskManagement: 'Project risk identification and mitigation strategies',
      timelineOptimization: 'Critical path method and schedule optimization'
    }
  }

  // ============================================================================
  // STEP IMPLEMENTATIONS WITH MCP ENHANCEMENT
  // ============================================================================

  private async analyzePlanWithMCP(state: any, input: TaskPlannerInput): Promise<any> {
    const analysis = await aiService.analyzeProjectPlan(
      input.projectPlan,
      input.requirements,
      input.constraints
    )

    this.state!.results.planAnalysis = analysis
    
    return {
      results: analysis,
      needsInput: false,
      completed: false
    }
  }

  private async decomposeTasksWithMCP(state: any): Promise<any> {
    const planAnalysis = this.state!.results.planAnalysis
    
    const tasks = await aiService.decomposeIntoTasks(
      planAnalysis,
      this.state!.input.preferences?.methodology || 'agile'
    )

    this.state!.results.tasks = tasks
    
    return {
      results: tasks,
      needsInput: false,
      completed: false
    }
  }

  private async analyzeDependenciesWithMCP(state: any): Promise<any> {
    const tasks = this.state!.results.tasks
    
    const dependencies = await aiService.analyzeDependencies(tasks)

    this.state!.results.dependencies = dependencies
    
    return {
      results: dependencies,
      needsInput: false,
      completed: false
    }
  }

  private async estimateEffortWithMCP(state: any): Promise<any> {
    const tasks = this.state!.results.tasks
    
    const estimates = await aiService.estimateEffort(
      tasks,
      this.state!.input.preferences?.teamSize || 3
    )

    // Update tasks with effort estimates
    this.state!.results.tasks = tasks.map((task: Task, index: number) => ({
      ...task,
      estimatedHours: estimates[index]?.hours || 8,
      complexity: estimates[index]?.complexity || 'medium'
    }))
    
    return {
      results: estimates,
      needsInput: false,
      completed: false
    }
  }

  private async createTimelineWithMCP(state: any): Promise<any> {
    const tasks = this.state!.results.tasks
    const dependencies = this.state!.results.dependencies
    
    const timeline = await aiService.createProjectTimeline(
      tasks,
      dependencies,
      this.state!.input.preferences?.sprintDuration || 2
    )

    this.state!.results.timeline = timeline
    
    return {
      results: timeline,
      needsInput: false,
      completed: false
    }
  }

  private async allocateResourcesWithMCP(state: any): Promise<any> {
    const tasks = this.state!.results.tasks
    const timeline = this.state!.results.timeline
    
    const allocation = await aiService.allocateResources(tasks, timeline)

    this.state!.results.resources = allocation
    
    return {
      results: allocation,
      needsInput: false,
      completed: false
    }
  }

  private async identifyRisksWithMCP(state: any): Promise<any> {
    const planAnalysis = this.state!.results.planAnalysis
    const tasks = this.state!.results.tasks
    
    const risks = await aiService.identifyProjectRisks(planAnalysis, tasks)

    this.state!.results.risks = risks
    
    return {
      results: risks,
      needsInput: false,
      completed: false
    }
  }

  private async defineMilestonesWithMCP(state: any): Promise<any> {
    const timeline = this.state!.results.timeline
    const tasks = this.state!.results.tasks
    
    const milestones = await aiService.defineMilestones(timeline, tasks)

    this.state!.results.milestones = milestones
    
    return {
      results: milestones,
      needsInput: false,
      completed: false
    }
  }

  private async optimizeScheduleWithMCP(state: any): Promise<any> {
    const timeline = this.state!.results.timeline
    const resources = this.state!.results.resources
    const risks = this.state!.results.risks
    
    const optimizedSchedule = await aiService.optimizeSchedule(
      timeline,
      resources,
      risks
    )

    this.state!.results.optimizedSchedule = optimizedSchedule
    
    return {
      results: optimizedSchedule,
      needsInput: false,
      completed: true // Final step
    }
  }
}

// Export for easy access
export { TaskPlannerAgent as default }
