#!/usr/bin/env node

/**
 * AG3NT Platform - Integration Test
 * 
 * Tests the framework integration with the platform
 */

const { execSync } = require('child_process')
const fs = require('fs')

console.log('🧪 Testing AG3NT Framework Integration')
console.log('=' .repeat(50))

async function testFrameworkImport() {
  console.log('\n📦 Testing framework import...')
  
  const testScript = `
const { AG3NTFramework, createPlanningAgent } = require('./ag3nt-framework-standalone/dist/index.js');

console.log('✅ Framework imported successfully');
console.log('✅ AG3NTFramework:', typeof AG3NTFramework);
console.log('✅ createPlanningAgent:', typeof createPlanningAgent);

// Test basic initialization
const framework = new AG3NTFramework({
  contextEngine: { enableMCP: false },
  coordination: { enableTaskDelegation: false },
  discovery: { enableAgentDiscovery: false }
});

console.log('✅ Framework instance created');
console.log('Framework capabilities:', Object.keys(framework));
`

  try {
    fs.writeFileSync('temp-test.js', testScript)
    execSync('node temp-test.js', { stdio: 'inherit' })
    fs.unlinkSync('temp-test.js')
    console.log('✅ Framework import test passed')
  } catch (error) {
    console.error('❌ Framework import test failed:', error.message)
    if (fs.existsSync('temp-test.js')) {
      fs.unlinkSync('temp-test.js')
    }
    throw error
  }
}

async function testAPIEndpoints() {
  console.log('\n🌐 Testing API endpoints...')
  
  // Check if API files exist
  const apiFiles = [
    'app/api/framework/route.ts'
  ]
  
  for (const file of apiFiles) {
    if (fs.existsSync(file)) {
      console.log(`✅ ${file} exists`)
    } else {
      console.log(`❌ ${file} missing`)
      throw new Error(`Required API file missing: ${file}`)
    }
  }
}

async function testComponents() {
  console.log('\n🎨 Testing React components...')
  
  const componentFiles = [
    'components/framework-status.tsx',
    'hooks/use-framework.ts',
    'lib/framework-service.ts'
  ]
  
  for (const file of componentFiles) {
    if (fs.existsSync(file)) {
      console.log(`✅ ${file} exists`)
    } else {
      console.log(`❌ ${file} missing`)
      throw new Error(`Required component file missing: ${file}`)
    }
  }
}

async function testFrameworkBuild() {
  console.log('\n🔨 Testing framework build...')
  
  try {
    process.chdir('ag3nt-framework-standalone')
    
    // Check if framework is built
    if (fs.existsSync('dist/index.js')) {
      console.log('✅ Framework is built')
    } else {
      console.log('🔨 Building framework...')
      execSync('npm run build', { stdio: 'inherit' })
      console.log('✅ Framework built successfully')
    }
    
    // Test framework functionality
    console.log('🧪 Testing framework functionality...')
    execSync('npm test', { stdio: 'inherit' })
    console.log('✅ Framework tests passed')
    
    process.chdir('..')
  } catch (error) {
    process.chdir('..')
    console.error('❌ Framework build/test failed:', error.message)
    throw error
  }
}

async function main() {
  try {
    // Test framework build and functionality
    await testFrameworkBuild()
    
    // Test framework import
    await testFrameworkImport()
    
    // Test API endpoints
    await testAPIEndpoints()
    
    // Test components
    await testComponents()
    
    console.log('\n🎉 ALL INTEGRATION TESTS PASSED!')
    console.log('\n✅ Framework Integration Status:')
    console.log('  • Framework built and functional')
    console.log('  • API endpoints configured')
    console.log('  • React components ready')
    console.log('  • TypeScript configuration updated')
    console.log('\n🚀 Your AG3NT Platform is ready with framework integration!')
    console.log('\nNext steps:')
    console.log('  1. Run: npm run dev:platform')
    console.log('  2. Open: http://localhost:3000')
    console.log('  3. Test the planning agent with framework backend')
    
  } catch (error) {
    console.error('\n❌ Integration test failed:', error.message)
    console.log('\n🔧 Troubleshooting:')
    console.log('  1. Run: npm run framework:build')
    console.log('  2. Check: ag3nt-framework-standalone/dist/ exists')
    console.log('  3. Verify: All required files are present')
    console.log('  4. Run: node scripts/setup-framework.js')
    process.exit(1)
  }
}

main().catch(console.error)
