/**
 * AG3NT Framework - Agent Registry
 * 
 * Centralized registry for managing all agents in the framework.
 * Extracted from context-engine.ts agent registration patterns.
 * 
 * Features:
 * - Agent registration and discovery
 * - Capability-based agent matching
 * - Agent lifecycle management
 * - Health monitoring and status tracking
 * - Agent communication coordination
 */

import { EventEmitter } from "events"
import { BaseAgent, AgentCapabilities, AgentState } from "./base-agent"

export interface RegisteredAgent {
  agentId: string
  agentType: string
  instance: BaseAgent
  capabilities: AgentCapabilities
  status: AgentStatus
  metadata: AgentRegistrationMetadata
  healthCheck: AgentHealthCheck
}

export interface AgentStatus {
  state: 'idle' | 'busy' | 'error' | 'offline'
  currentSession?: string
  lastActivity: string
  activeSessions: number
  totalExecutions: number
  successRate: number
}

export interface AgentRegistrationMetadata {
  registeredAt: string
  version: string
  description: string
  tags: string[]
  priority: number
  maxConcurrentSessions: number
}

export interface AgentHealthCheck {
  lastCheck: string
  status: 'healthy' | 'degraded' | 'unhealthy'
  responseTime: number
  errorCount: number
  uptime: number
  memoryUsage?: number
  cpuUsage?: number
  queueSize?: number
}

export interface AgentLoadMetrics {
  currentLoad: number // 0-1
  averageLoad: number // 0-1
  peakLoad: number // 0-1
  requestsPerMinute: number
  averageResponseTime: number
  successRate: number
  errorRate: number
  queueLength: number
  capacity: number
}

export interface AgentDiscoveryInfo {
  agentId: string
  agentType: string
  capabilities: string[]
  location: string
  version: string
  lastSeen: string
  discoveryMethod: 'registration' | 'heartbeat' | 'broadcast' | 'dns'
  networkInfo: {
    ip: string
    port: number
    protocol: string
  }
}

export interface LoadBalancingStrategy {
  type: 'round_robin' | 'least_connections' | 'weighted_round_robin' | 'least_response_time' | 'resource_based'
  weights?: Record<string, number>
  healthThreshold?: number
  failoverEnabled?: boolean
}

export interface FailoverConfig {
  enabled: boolean
  maxRetries: number
  retryDelay: number
  backupAgents: string[]
  circuitBreakerThreshold: number
}

export interface AgentQuery {
  agentType?: string
  requiredCapabilities?: string[]
  tags?: string[]
  status?: AgentStatus['state']
  priority?: number
}

/**
 * Agent Registry - Central management for all AG3NT agents
 */
export class AgentRegistry extends EventEmitter {
  private agents: Map<string, RegisteredAgent> = new Map()
  private agentsByType: Map<string, Set<string>> = new Map()
  private agentsByCapability: Map<string, Set<string>> = new Map()
  private healthCheckInterval: NodeJS.Timeout | null = null
  private isInitialized: boolean = false

  // Enhanced discovery and load balancing
  private agentLoadMetrics: Map<string, AgentLoadMetrics> = new Map()
  private agentDiscoveryInfo: Map<string, AgentDiscoveryInfo> = new Map()
  private loadBalancingStrategy: LoadBalancingStrategy = { type: 'least_response_time' }
  private failoverConfig: FailoverConfig = {
    enabled: true,
    maxRetries: 3,
    retryDelay: 1000,
    backupAgents: [],
    circuitBreakerThreshold: 0.5
  }
  private circuitBreakers: Map<string, { failures: number, lastFailure: number, state: 'closed' | 'open' | 'half-open' }> = new Map()
  private roundRobinCounters: Map<string, number> = new Map()

  constructor() {
    super()
  }

  /**
   * Initialize the registry
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return

    // Start health check monitoring
    this.startHealthMonitoring()
    
    this.isInitialized = true
    this.emit('registry_initialized')
    console.log('🏛️ AG3NT Agent Registry initialized')
  }

  /**
   * Register an agent with the registry
   */
  async registerAgent(
    agent: BaseAgent,
    metadata: Partial<AgentRegistrationMetadata> = {}
  ): Promise<string> {
    const agentId = agent.id
    const agentType = agent.type

    if (this.agents.has(agentId)) {
      throw new Error(`Agent ${agentId} is already registered`)
    }

    // Initialize agent if not already done
    if (!agent.initialized) {
      throw new Error(`Agent ${agentId} must be initialized before registration`)
    }

    const registeredAgent: RegisteredAgent = {
      agentId,
      agentType,
      instance: agent,
      capabilities: agent['config'].capabilities, // Access protected config
      status: {
        state: 'idle',
        lastActivity: new Date().toISOString(),
        activeSessions: 0,
        totalExecutions: 0,
        successRate: 1.0
      },
      metadata: {
        registeredAt: new Date().toISOString(),
        version: '1.0.0',
        description: `${agentType} agent`,
        tags: [],
        priority: 1,
        maxConcurrentSessions: 5,
        ...metadata
      },
      healthCheck: {
        lastCheck: new Date().toISOString(),
        status: 'healthy',
        responseTime: 0,
        errorCount: 0,
        uptime: 0
      }
    }

    // Store in registry
    this.agents.set(agentId, registeredAgent)

    // Index by type
    if (!this.agentsByType.has(agentType)) {
      this.agentsByType.set(agentType, new Set())
    }
    this.agentsByType.get(agentType)!.add(agentId)

    // Index by capabilities
    registeredAgent.capabilities.requiredCapabilities.forEach(capability => {
      if (!this.agentsByCapability.has(capability)) {
        this.agentsByCapability.set(capability, new Set())
      }
      this.agentsByCapability.get(capability)!.add(agentId)
    })

    // Set up agent event listeners
    this.setupAgentEventListeners(agent, registeredAgent)

    this.emit('agent_registered', { agentId, agentType })
    console.log(`🤖 Agent registered: ${agentType} (${agentId})`)

    return agentId
  }

  /**
   * Unregister an agent from the registry
   */
  async unregisterAgent(agentId: string): Promise<void> {
    const registeredAgent = this.agents.get(agentId)
    if (!registeredAgent) {
      throw new Error(`Agent ${agentId} not found in registry`)
    }

    // Remove from indices
    const agentType = registeredAgent.agentType
    this.agentsByType.get(agentType)?.delete(agentId)
    
    registeredAgent.capabilities.requiredCapabilities.forEach(capability => {
      this.agentsByCapability.get(capability)?.delete(agentId)
    })

    // Remove from main registry
    this.agents.delete(agentId)

    this.emit('agent_unregistered', { agentId, agentType })
    console.log(`🗑️ Agent unregistered: ${agentType} (${agentId})`)
  }

  /**
   * Find agents matching query criteria
   */
  findAgents(query: AgentQuery = {}): RegisteredAgent[] {
    let candidates = Array.from(this.agents.values())

    // Filter by agent type
    if (query.agentType) {
      candidates = candidates.filter(agent => agent.agentType === query.agentType)
    }

    // Filter by required capabilities
    if (query.requiredCapabilities && query.requiredCapabilities.length > 0) {
      candidates = candidates.filter(agent => 
        query.requiredCapabilities!.every(capability =>
          agent.capabilities.requiredCapabilities.includes(capability)
        )
      )
    }

    // Filter by tags
    if (query.tags && query.tags.length > 0) {
      candidates = candidates.filter(agent =>
        query.tags!.some(tag => agent.metadata.tags.includes(tag))
      )
    }

    // Filter by status
    if (query.status) {
      candidates = candidates.filter(agent => agent.status.state === query.status)
    }

    // Filter by priority
    if (query.priority !== undefined) {
      candidates = candidates.filter(agent => agent.metadata.priority >= query.priority!)
    }

    // Sort by priority and success rate
    candidates.sort((a, b) => {
      if (a.metadata.priority !== b.metadata.priority) {
        return b.metadata.priority - a.metadata.priority
      }
      return b.status.successRate - a.status.successRate
    })

    return candidates
  }

  /**
   * Get the best available agent for a task
   */
  getBestAgent(query: AgentQuery): RegisteredAgent | null {
    const candidates = this.findAgents({
      ...query,
      status: 'idle' // Only consider idle agents
    })

    // Filter out agents at capacity
    const availableAgents = candidates.filter(agent => 
      agent.status.activeSessions < agent.metadata.maxConcurrentSessions
    )

    return availableAgents.length > 0 ? availableAgents[0] : null
  }

  /**
   * Get agent by ID
   */
  getAgent(agentId: string): RegisteredAgent | null {
    return this.agents.get(agentId) || null
  }

  /**
   * Get all agents of a specific type
   */
  getAgentsByType(agentType: string): RegisteredAgent[] {
    const agentIds = this.agentsByType.get(agentType) || new Set()
    return Array.from(agentIds).map(id => this.agents.get(id)!).filter(Boolean)
  }

  /**
   * Get registry statistics
   */
  getStats(): {
    totalAgents: number
    agentsByType: Record<string, number>
    agentsByStatus: Record<string, number>
    healthyAgents: number
    averageSuccessRate: number
  } {
    const agents = Array.from(this.agents.values())
    
    const agentsByType: Record<string, number> = {}
    const agentsByStatus: Record<string, number> = {}
    
    let totalSuccessRate = 0
    let healthyAgents = 0

    agents.forEach(agent => {
      // Count by type
      agentsByType[agent.agentType] = (agentsByType[agent.agentType] || 0) + 1
      
      // Count by status
      agentsByStatus[agent.status.state] = (agentsByStatus[agent.status.state] || 0) + 1
      
      // Calculate health and success metrics
      if (agent.healthCheck.status === 'healthy') {
        healthyAgents++
      }
      totalSuccessRate += agent.status.successRate
    })

    return {
      totalAgents: agents.length,
      agentsByType,
      agentsByStatus,
      healthyAgents,
      averageSuccessRate: agents.length > 0 ? totalSuccessRate / agents.length : 0
    }
  }

  /**
   * Set up event listeners for agent lifecycle events
   */
  private setupAgentEventListeners(agent: BaseAgent, registeredAgent: RegisteredAgent): void {
    agent.on('execution_started', (data) => {
      registeredAgent.status.state = 'busy'
      registeredAgent.status.currentSession = data.sessionId
      registeredAgent.status.activeSessions++
      registeredAgent.status.lastActivity = new Date().toISOString()
    })

    agent.on('execution_completed', (data) => {
      registeredAgent.status.state = 'idle'
      registeredAgent.status.currentSession = undefined
      registeredAgent.status.activeSessions = Math.max(0, registeredAgent.status.activeSessions - 1)
      registeredAgent.status.totalExecutions++
      registeredAgent.status.lastActivity = new Date().toISOString()
      
      // Update success rate
      const successCount = Math.floor(registeredAgent.status.totalExecutions * registeredAgent.status.successRate) + 1
      registeredAgent.status.successRate = successCount / registeredAgent.status.totalExecutions
    })

    agent.on('execution_failed', (data) => {
      registeredAgent.status.state = 'error'
      registeredAgent.status.currentSession = undefined
      registeredAgent.status.activeSessions = Math.max(0, registeredAgent.status.activeSessions - 1)
      registeredAgent.status.totalExecutions++
      registeredAgent.status.lastActivity = new Date().toISOString()
      
      // Update success rate
      const successCount = Math.floor(registeredAgent.status.totalExecutions * registeredAgent.status.successRate)
      registeredAgent.status.successRate = successCount / registeredAgent.status.totalExecutions
      
      // Update health check
      registeredAgent.healthCheck.errorCount++
      if (registeredAgent.healthCheck.errorCount > 5) {
        registeredAgent.healthCheck.status = 'unhealthy'
      } else if (registeredAgent.healthCheck.errorCount > 2) {
        registeredAgent.healthCheck.status = 'degraded'
      }
    })
  }

  /**
   * Start health monitoring for all agents
   */
  private startHealthMonitoring(): void {
    this.healthCheckInterval = setInterval(async () => {
      await this.performHealthChecks()
    }, 30000) // Check every 30 seconds
  }

  /**
   * Perform health checks on all registered agents
   */
  private async performHealthChecks(): Promise<void> {
    const agents = Array.from(this.agents.values())
    
    for (const registeredAgent of agents) {
      const startTime = Date.now()
      
      try {
        // Simple health check - verify agent is responsive
        const isHealthy = registeredAgent.instance.initialized
        const responseTime = Date.now() - startTime
        
        registeredAgent.healthCheck.lastCheck = new Date().toISOString()
        registeredAgent.healthCheck.responseTime = responseTime
        registeredAgent.healthCheck.uptime = Date.now() - new Date(registeredAgent.metadata.registeredAt).getTime()
        
        if (isHealthy && responseTime < 5000) {
          registeredAgent.healthCheck.status = 'healthy'
          registeredAgent.healthCheck.errorCount = Math.max(0, registeredAgent.healthCheck.errorCount - 1)
        } else {
          registeredAgent.healthCheck.status = 'degraded'
        }
        
      } catch (error) {
        registeredAgent.healthCheck.status = 'unhealthy'
        registeredAgent.healthCheck.errorCount++
        console.warn(`Health check failed for agent ${registeredAgent.agentId}:`, error)
      }
    }
  }

  // ============================================================================
  // ENHANCED DISCOVERY AND LOAD BALANCING
  // ============================================================================

  /**
   * Update agent load metrics
   */
  updateAgentLoad(agentId: string, metrics: Partial<AgentLoadMetrics>): void {
    const existing = this.agentLoadMetrics.get(agentId) || {
      currentLoad: 0,
      averageLoad: 0,
      peakLoad: 0,
      requestsPerMinute: 0,
      averageResponseTime: 1000,
      successRate: 1.0,
      errorRate: 0,
      queueLength: 0,
      capacity: 100
    }

    const updated = { ...existing, ...metrics }

    // Update averages
    if (metrics.currentLoad !== undefined) {
      updated.averageLoad = (updated.averageLoad * 0.9) + (metrics.currentLoad * 0.1)
      updated.peakLoad = Math.max(updated.peakLoad, metrics.currentLoad)
    }

    this.agentLoadMetrics.set(agentId, updated)
    this.emit('agent_load_updated', { agentId, metrics: updated })
  }

  /**
   * Get optimal agent using advanced load balancing
   */
  getOptimalAgentAdvanced(query: AgentQuery): RegisteredAgent | null {
    const candidates = this.findCandidateAgents(query)
    if (candidates.length === 0) return null

    // Filter out unhealthy agents
    const healthyCandidates = candidates.filter(agent =>
      agent.healthCheck.status === 'healthy' || agent.healthCheck.status === 'degraded'
    )

    if (healthyCandidates.length === 0) {
      // If no healthy agents, try failover
      return this.handleFailover(query, candidates)
    }

    // Apply load balancing strategy
    return this.selectAgentByStrategy(healthyCandidates, this.loadBalancingStrategy)
  }

  /**
   * Find candidate agents based on query
   */
  private findCandidateAgents(query: AgentQuery): RegisteredAgent[] {
    let candidates: RegisteredAgent[] = []

    // Filter by agent type
    if (query.agentType) {
      const agentIds = this.agentsByType.get(query.agentType)
      if (agentIds) {
        candidates = Array.from(agentIds)
          .map(id => this.agents.get(id))
          .filter(agent => agent !== undefined) as RegisteredAgent[]
      }
    } else {
      candidates = Array.from(this.agents.values())
    }

    // Filter by capabilities
    if (query.requiredCapabilities && query.requiredCapabilities.length > 0) {
      candidates = candidates.filter(agent =>
        query.requiredCapabilities!.every(cap =>
          agent.capabilities.requiredCapabilities.includes(cap)
        )
      )
    }

    // Filter by priority
    if (query.priority !== undefined) {
      candidates = candidates.filter(agent =>
        agent.metadata.priority >= query.priority!
      )
    }

    // Filter by availability
    candidates = candidates.filter(agent =>
      agent.status.state === 'idle' ||
      (agent.status.state === 'busy' && agent.status.activeSessions < agent.metadata.maxConcurrentSessions)
    )

    return candidates
  }

  /**
   * Select agent based on load balancing strategy
   */
  private selectAgentByStrategy(candidates: RegisteredAgent[], strategy: LoadBalancingStrategy): RegisteredAgent | null {
    if (candidates.length === 0) return null
    if (candidates.length === 1) return candidates[0]

    switch (strategy.type) {
      case 'round_robin':
        return this.selectRoundRobin(candidates)

      case 'least_connections':
        return this.selectLeastConnections(candidates)

      case 'weighted_round_robin':
        return this.selectWeightedRoundRobin(candidates, strategy.weights || {})

      case 'least_response_time':
        return this.selectLeastResponseTime(candidates)

      case 'resource_based':
        return this.selectResourceBased(candidates)

      default:
        return candidates[0]
    }
  }

  /**
   * Round robin selection
   */
  private selectRoundRobin(candidates: RegisteredAgent[]): RegisteredAgent {
    const key = 'round_robin'
    const counter = this.roundRobinCounters.get(key) || 0
    const selected = candidates[counter % candidates.length]
    this.roundRobinCounters.set(key, counter + 1)
    return selected
  }

  /**
   * Least connections selection
   */
  private selectLeastConnections(candidates: RegisteredAgent[]): RegisteredAgent {
    return candidates.reduce((best, current) =>
      current.status.activeSessions < best.status.activeSessions ? current : best
    )
  }

  /**
   * Weighted round robin selection
   */
  private selectWeightedRoundRobin(candidates: RegisteredAgent[], weights: Record<string, number>): RegisteredAgent {
    const weightedCandidates: RegisteredAgent[] = []

    for (const candidate of candidates) {
      const weight = weights[candidate.agentId] || 1
      for (let i = 0; i < weight; i++) {
        weightedCandidates.push(candidate)
      }
    }

    return this.selectRoundRobin(weightedCandidates)
  }

  /**
   * Least response time selection
   */
  private selectLeastResponseTime(candidates: RegisteredAgent[]): RegisteredAgent {
    return candidates.reduce((best, current) => {
      const bestMetrics = this.agentLoadMetrics.get(best.agentId)
      const currentMetrics = this.agentLoadMetrics.get(current.agentId)

      const bestResponseTime = bestMetrics?.averageResponseTime || current.healthCheck.responseTime
      const currentResponseTime = currentMetrics?.averageResponseTime || current.healthCheck.responseTime

      return currentResponseTime < bestResponseTime ? current : best
    })
  }

  /**
   * Resource-based selection
   */
  private selectResourceBased(candidates: RegisteredAgent[]): RegisteredAgent {
    return candidates.reduce((best, current) => {
      const bestMetrics = this.agentLoadMetrics.get(best.agentId)
      const currentMetrics = this.agentLoadMetrics.get(current.agentId)

      const bestLoad = bestMetrics?.currentLoad || 0.5
      const currentLoad = currentMetrics?.currentLoad || 0.5

      return currentLoad < bestLoad ? current : best
    })
  }

  /**
   * Handle failover when no healthy agents available
   */
  private handleFailover(query: AgentQuery, candidates: RegisteredAgent[]): RegisteredAgent | null {
    if (!this.failoverConfig.enabled) return null

    // Try backup agents first
    for (const backupAgentId of this.failoverConfig.backupAgents) {
      const backupAgent = this.agents.get(backupAgentId)
      if (backupAgent && this.isAgentSuitable(backupAgent, query)) {
        console.log(`🔄 Using backup agent: ${backupAgentId}`)
        return backupAgent
      }
    }

    // Check circuit breakers
    const availableAgents = candidates.filter(agent => {
      const circuitBreaker = this.circuitBreakers.get(agent.agentId)
      if (!circuitBreaker) return true

      if (circuitBreaker.state === 'open') {
        // Check if circuit breaker should be half-open
        const timeSinceLastFailure = Date.now() - circuitBreaker.lastFailure
        if (timeSinceLastFailure > 60000) { // 1 minute
          circuitBreaker.state = 'half-open'
          return true
        }
        return false
      }

      return true
    })

    return availableAgents.length > 0 ? availableAgents[0] : null
  }

  /**
   * Check if agent is suitable for query
   */
  private isAgentSuitable(agent: RegisteredAgent, query: AgentQuery): boolean {
    // Check agent type
    if (query.agentType && agent.agentType !== query.agentType) {
      return false
    }

    // Check capabilities
    if (query.requiredCapabilities && query.requiredCapabilities.length > 0) {
      const hasCapabilities = query.requiredCapabilities.every(cap =>
        agent.capabilities.requiredCapabilities.includes(cap)
      )
      if (!hasCapabilities) return false
    }

    // Check priority
    if (query.priority !== undefined && agent.metadata.priority < query.priority) {
      return false
    }

    return true
  }

  /**
   * Record agent execution result for circuit breaker
   */
  recordAgentExecution(agentId: string, success: boolean): void {
    let circuitBreaker = this.circuitBreakers.get(agentId)
    if (!circuitBreaker) {
      circuitBreaker = { failures: 0, lastFailure: 0, state: 'closed' }
      this.circuitBreakers.set(agentId, circuitBreaker)
    }

    if (success) {
      circuitBreaker.failures = 0
      if (circuitBreaker.state === 'half-open') {
        circuitBreaker.state = 'closed'
      }
    } else {
      circuitBreaker.failures++
      circuitBreaker.lastFailure = Date.now()

      const failureRate = circuitBreaker.failures / 10 // Last 10 attempts
      if (failureRate >= this.failoverConfig.circuitBreakerThreshold) {
        circuitBreaker.state = 'open'
        console.log(`🔴 Circuit breaker opened for agent: ${agentId}`)
      }
    }
  }

  /**
   * Configure load balancing strategy
   */
  setLoadBalancingStrategy(strategy: LoadBalancingStrategy): void {
    this.loadBalancingStrategy = strategy
    console.log(`⚖️ Load balancing strategy updated: ${strategy.type}`)
  }

  /**
   * Configure failover settings
   */
  setFailoverConfig(config: Partial<FailoverConfig>): void {
    this.failoverConfig = { ...this.failoverConfig, ...config }
    console.log('🔄 Failover configuration updated')
  }

  /**
   * Get agent load metrics
   */
  getAgentLoadMetrics(agentId: string): AgentLoadMetrics | null {
    return this.agentLoadMetrics.get(agentId) || null
  }

  /**
   * Get all agent load metrics
   */
  getAllAgentLoadMetrics(): Map<string, AgentLoadMetrics> {
    return new Map(this.agentLoadMetrics)
  }

  /**
   * Get registry statistics
   */
  getRegistryStats(): {
    totalAgents: number
    healthyAgents: number
    degradedAgents: number
    unhealthyAgents: number
    averageLoad: number
    totalCapacity: number
  } {
    const agents = Array.from(this.agents.values())
    const loadMetrics = Array.from(this.agentLoadMetrics.values())

    return {
      totalAgents: agents.length,
      healthyAgents: agents.filter(a => a.healthCheck.status === 'healthy').length,
      degradedAgents: agents.filter(a => a.healthCheck.status === 'degraded').length,
      unhealthyAgents: agents.filter(a => a.healthCheck.status === 'unhealthy').length,
      averageLoad: loadMetrics.reduce((sum, m) => sum + m.currentLoad, 0) / Math.max(loadMetrics.length, 1),
      totalCapacity: loadMetrics.reduce((sum, m) => sum + m.capacity, 0)
    }
  }

  /**
   * Cleanup and shutdown
   */
  async shutdown(): Promise<void> {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval)
      this.healthCheckInterval = null
    }

    // Unregister all agents
    const agentIds = Array.from(this.agents.keys())
    for (const agentId of agentIds) {
      await this.unregisterAgent(agentId)
    }

    // Clear enhanced state
    this.agentLoadMetrics.clear()
    this.agentDiscoveryInfo.clear()
    this.circuitBreakers.clear()
    this.roundRobinCounters.clear()

    this.isInitialized = false
    this.emit('registry_shutdown')
    console.log('🏛️ AG3NT Agent Registry shutdown complete')
  }
}
