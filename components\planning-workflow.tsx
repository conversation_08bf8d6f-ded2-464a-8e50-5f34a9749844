"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { AutoResizeTextarea } from "@/components/ui/auto-resize-textarea"
import { Card, CardContent } from "@/components/ui/card"
import { motion, AnimatePresence } from "framer-motion"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Loader2, Paperclip, X } from "lucide-react"
import type { PlanningTask, Question } from "@/types/planning"
import type { ChatMessage } from "@/hooks/use-chat-state"
import { isolatedFrameworkService } from "@/lib/framework-service-isolated"

interface PlanningWorkflowProps {
  // Core state
  userPrompt: string
  setUserPrompt: (prompt: string) => void
  hasStarted: boolean
  setHasStarted: (started: boolean) => void
  isProcessing: boolean
  setIsProcessing: (processing: boolean) => void
  tasks: PlanningTask[]
  setTasks: (tasks: PlanningTask[]) => void
  currentTaskIndex: number
  setCurrentTaskIndex: (index: number) => void
  
  // Results and context
  results: Record<string, any>
  setResults: (results: Record<string, any>) => void
  planningContext: any
  setPlanningContext: (context: any) => void
  
  // Error handling
  error: string | null
  setError: (error: string | null) => void
  canRetry: boolean
  setCanRetry: (canRetry: boolean) => void
  
  // Image processing
  uploadedImages: File[]
  setUploadedImages: (images: File[]) => void
  isProcessingImages: boolean
  setIsProcessingImages: (processing: boolean) => void
  designStyleGuideState: string | null
  setDesignStyleGuideState: (guide: string | null) => void
  
  // Chat integration
  addChatMessage: (message: Omit<ChatMessage, 'id' | 'timestamp'>) => void
  
  // Settings
  preferredModel: string
  userApiKey: string
  isAutonomousMode: boolean
  
  // Callbacks
  onPlanningComplete?: () => void
}

export function PlanningWorkflow({
  userPrompt,
  setUserPrompt,
  hasStarted,
  setHasStarted,
  isProcessing,
  setIsProcessing,
  tasks,
  setTasks,
  currentTaskIndex,
  setCurrentTaskIndex,
  results,
  setResults,
  planningContext,
  setPlanningContext,
  error,
  setError,
  canRetry,
  setCanRetry,
  uploadedImages,
  setUploadedImages,
  isProcessingImages,
  setIsProcessingImages,
  designStyleGuideState,
  setDesignStyleGuideState,
  addChatMessage,
  preferredModel,
  userApiKey,
  isAutonomousMode,
  onPlanningComplete,
}: PlanningWorkflowProps) {
  
  const extractProjectName = (prompt: string): string => {
    const match = prompt.match(/(?:build|create|make|develop)\s+(?:a|an)?\s*([^.!?]+)/i)
    return match ? match[1].trim() : "New Project"
  }

  const extractFeatures = (prompt: string): string[] => {
    const features = []
    if (prompt.toLowerCase().includes('auth')) features.push('authentication')
    if (prompt.toLowerCase().includes('database')) features.push('database')
    if (prompt.toLowerCase().includes('api')) features.push('api')
    if (prompt.toLowerCase().includes('responsive')) features.push('responsive design')
    return features
  }

  const handleStartPlanning = async () => {
    if (!userPrompt.trim()) return

    console.log("Starting planning process...")
    setHasStarted(true)
    setIsProcessing(true)
    setCurrentTaskIndex(0)
    setError(null)

    // Add user message to chat
    addChatMessage({
      type: 'user',
      content: userPrompt,
    })

    // Add AI response
    setTimeout(() => {
      addChatMessage({
        type: 'ai',
        content: "I'll help you build that! Let me start by analyzing your requirements and creating a development plan.",
      })
    }, 500)

    try {
      let designStyleGuide = null

      // If images are uploaded, use the design agent first
      if (uploadedImages.length > 0) {
        console.log("Processing uploaded images with design agent...")
        setIsProcessingImages(true)

        try {
          const formData = new FormData()
          uploadedImages.forEach((image, index) => {
            formData.append('images', image)
          })

          const designResponse = await fetch("/api/design-agent", {
            method: "POST",
            body: formData,
          })

          if (designResponse.ok) {
            const designResult = await designResponse.json()
            designStyleGuide = designResult.styleGuide
            setDesignStyleGuideState(designStyleGuide)
            console.log("Design agent generated style guide - will be added when design step runs")
          } else {
            const errorText = await designResponse.text()
            console.warn("Design agent failed:", errorText)
          }
        } catch (designError) {
          console.error("Design agent error:", designError)
        } finally {
          setIsProcessingImages(false)
        }
      }

      // Try to use Isolated AG3NT Framework for planning
      console.log("Using Isolated AG3NT Framework for planning...")

      try {
        await isolatedFrameworkService.initialize()

        const planResult = await isolatedFrameworkService.planProject({
          projectName: extractProjectName(userPrompt),
          projectDescription: userPrompt,
          projectType: 'web-application',
          frontendFramework: 'react',
          backendFramework: 'nestjs',
          database: 'postgresql',
          features: extractFeatures(userPrompt)
        })

        if (planResult.success) {
          console.log("Isolated AG3NT Framework planning completed:", planResult)
          setPlanningContext({
            ...planResult.data,
            designStyleGuide: designStyleGuide,
            frameworkResult: planResult,
            agentsUsed: planResult.agentsUsed,
            executionTime: planResult.executionTime
          })
        } else {
          throw new Error(planResult.error || "Framework planning failed")
        }
      } catch (frameworkError) {
        console.warn("Isolated framework planning failed, falling back to API:", frameworkError.message)

        // Fallback to original API planning
        const response = await fetch("/api/planning", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            prompt: userPrompt,
            isInteractive: false,
            answers: {},
            designStyleGuide,
            hasImages: uploadedImages.length > 0,
            userPreferences: {
              model: preferredModel,
              apiKey: userApiKey || undefined,
              autonomousMode: isAutonomousMode
            }
          }),
        })

        const responseText = await response.text()
        let result
        try {
          result = JSON.parse(responseText)
        } catch (parseError) {
          throw new Error("Invalid JSON response from planning API")
        }

        if (!response.ok) {
          throw new Error(`API Error: ${response.status} - ${result.error || "Unknown error"}`)
        }

        console.log("Using fallback API planning")
        setPlanningContext({
          ...result,
          designStyleGuide: designStyleGuide,
        })
      }
      
      setCurrentTaskIndex(0)
      onPlanningComplete?.()
    } catch (error) {
      console.error("Failed to start AI planning:", error)

      let errorMessage = "AI planning failed. "
      let suggestion = ""

      if (error instanceof Error) {
        if (error.message.includes("401") || error.message.includes("403")) {
          errorMessage += "Authentication failed."
          suggestion = "Please check your OPENROUTER_API_KEY is valid and has sufficient credits."
        } else if (error.message.includes("429")) {
          errorMessage += "Rate limit exceeded."
          suggestion = "Please wait a moment and try again."
        } else if (error.message.includes("network") || error.message.includes("fetch")) {
          errorMessage += "Network connection failed."
          suggestion = "Please check your internet connection and try again."
        } else {
          errorMessage += error.message
          suggestion = "Please check your OPENROUTER_API_KEY and try again."
        }
      } else {
        errorMessage += "Unknown error occurred."
        suggestion = "Please refresh the page and try again."
      }

      setError(`${errorMessage} ${suggestion}`)
      setIsProcessing(false)
    }
  }

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    setUploadedImages(prev => [...prev, ...files])
  }

  const removeImage = (index: number) => {
    setUploadedImages(prev => prev.filter((_, i) => i !== index))
  }

  if (hasStarted) {
    return null // Planning workflow is handled in chat sidebar when started
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="text-center mb-8">
        <h1 className="text-4xl md:text-6xl font-bold mb-4">
          <span className="text-white">AG3N</span>
          <span className="text-[#ff2d55]">T</span>
        </h1>
        <p className="text-gray-400 text-lg">
          Autonomous AI agents that plan, design, and build your projects
        </p>
      </div>

      <Card className="bg-[#1A1A1A] border-gray-700">
        <CardContent className="p-6">
          <div className="space-y-4">
            <AutoResizeTextarea
              value={userPrompt}
              onChange={(e) => setUserPrompt(e.target.value)}
              placeholder="Describe your project idea... (e.g., 'Build a cyberpunk-themed calculator app with neon colors and futuristic design')"
              className="w-full bg-[#0A0A0A] border-gray-600 text-white placeholder-gray-400 min-h-[120px]"
              disabled={isProcessing}
            />

            {/* Image Upload Section */}
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <input
                  type="file"
                  id="image-upload"
                  multiple
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                  disabled={isProcessing}
                />
                <label
                  htmlFor="image-upload"
                  className="flex items-center gap-2 px-3 py-2 bg-[#0A0A0A] border border-gray-600 rounded-lg text-gray-300 hover:text-white hover:border-gray-500 transition-colors cursor-pointer"
                >
                  <Paperclip className="w-4 h-4" />
                  Upload Design Images
                </label>
                {isProcessingImages && (
                  <div className="flex items-center gap-2 text-blue-400">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span className="text-sm">Processing images...</span>
                  </div>
                )}
              </div>

              {uploadedImages.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {uploadedImages.map((image, index) => (
                    <div key={index} className="relative group">
                      <img
                        src={URL.createObjectURL(image)}
                        alt={`Upload ${index + 1}`}
                        className="w-16 h-16 object-cover rounded border border-gray-600"
                      />
                      <button
                        onClick={() => removeImage(index)}
                        className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                        disabled={isProcessing}
                      >
                        <X className="w-3 h-3 text-white" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {error && (
              <div className="p-4 bg-red-900/20 border border-red-500/30 rounded-lg">
                <div className="flex items-start gap-3">
                  <AlertCircle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
                  <div className="flex-1">
                    <p className="text-red-300 text-sm">{error}</p>
                  </div>
                </div>
              </div>
            )}

            <Button
              onClick={handleStartPlanning}
              disabled={!userPrompt.trim() || isProcessing}
              className="w-full bg-[#ff2d55] hover:bg-[#e02847] text-white font-medium py-3 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isProcessing ? (
                <div className="flex items-center gap-2">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Planning Your Project...
                </div>
              ) : (
                "Start Planning"
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
