/**
 * AG3NT Platform - Framework Status Component
 * 
 * Displays the current status of the AG3NT Framework
 */

'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { 
  CheckCircle, 
  XCircle, 
  Loader2, 
  Brain, 
  Users, 
  Zap, 
  BarChart3,
  RefreshCw,
  Settings
} from 'lucide-react'
import { useFramework } from '@/hooks/use-framework'

export function FrameworkStatus() {
  const framework = useFramework()

  const getStatusIcon = () => {
    if (framework.status.loading) {
      return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
    }
    if (framework.hasError) {
      return <XCircle className="h-4 w-4 text-red-500" />
    }
    if (framework.isReady) {
      return <CheckCircle className="h-4 w-4 text-green-500" />
    }
    return <XCircle className="h-4 w-4 text-gray-400" />
  }

  const getStatusText = () => {
    if (framework.status.loading) return "Initializing..."
    if (framework.hasError) return "Limited Mode"
    if (framework.isReady) return "Ready"
    return "Starting..."
  }

  const getStatusColor = () => {
    if (framework.status.loading) return "blue"
    if (framework.hasError) return "yellow"
    if (framework.isReady) return "green"
    return "gray"
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Brain className="h-5 w-5 text-blue-500" />
            <CardTitle className="text-lg">AG3NT Framework</CardTitle>
          </div>
          <div className="flex items-center space-x-2">
            {getStatusIcon()}
            <Badge variant={getStatusColor() as any}>
              {getStatusText()}
            </Badge>
          </div>
        </div>
        <CardDescription>
          Advanced multi-agent development framework status
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Framework Status */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Framework Status</span>
            <span className="font-medium">
              {framework.isReady ? 'Operational' : 'Offline'}
            </span>
          </div>
          <Progress 
            value={framework.isReady ? 100 : framework.status.loading ? 50 : 0} 
            className="h-2"
          />
        </div>

        {/* Agent Count */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Users className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">Active Agents</span>
          </div>
          <Badge variant="outline">
            {framework.status.agentCount}
          </Badge>
        </div>

        {/* Analytics Summary */}
        {framework.analytics && (
          <>
            <Separator />
            <div className="space-y-3">
              <h4 className="text-sm font-medium flex items-center space-x-2">
                <BarChart3 className="h-4 w-4" />
                <span>Performance Metrics</span>
              </h4>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="space-y-1">
                  <div className="text-muted-foreground">Total Projects</div>
                  <div className="font-medium">{framework.analytics.totalProjects}</div>
                </div>
                <div className="space-y-1">
                  <div className="text-muted-foreground">Success Rate</div>
                  <div className="font-medium">
                    {(framework.analytics.successRate * 100).toFixed(1)}%
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="text-muted-foreground">Avg. Execution</div>
                  <div className="font-medium">
                    {(framework.analytics.averageExecutionTime / 1000).toFixed(1)}s
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="text-muted-foreground">Agent Efficiency</div>
                  <div className="font-medium">
                    {Object.keys(framework.analytics.agentUtilization).length > 0 ? 'High' : 'N/A'}
                  </div>
                </div>
              </div>
            </div>
          </>
        )}

        {/* Available Agents */}
        {framework.agents.length > 0 && (
          <>
            <Separator />
            <div className="space-y-3">
              <h4 className="text-sm font-medium">Available Agents</h4>
              <div className="grid grid-cols-2 gap-2">
                {framework.agents.slice(0, 6).map((agent) => (
                  <div key={agent.id} className="flex items-center space-x-2 text-xs">
                    <div className={`w-2 h-2 rounded-full ${
                      agent.status === 'available' ? 'bg-green-500' : 'bg-gray-400'
                    }`} />
                    <span className="truncate">{agent.type}</span>
                  </div>
                ))}
                {framework.agents.length > 6 && (
                  <div className="text-xs text-muted-foreground col-span-2">
                    +{framework.agents.length - 6} more agents
                  </div>
                )}
              </div>
            </div>
          </>
        )}

        {/* Error Display */}
        {framework.hasError && (
          <>
            <Separator />
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-yellow-600">Limited Mode</h4>
              <p className="text-xs text-yellow-600 bg-yellow-50 p-2 rounded">
                Running in simplified mode. Some advanced features may be limited.
                {framework.status.error && (
                  <details className="mt-1">
                    <summary className="cursor-pointer">Details</summary>
                    <div className="mt-1 text-xs">{framework.status.error}</div>
                  </details>
                )}
              </p>
            </div>
          </>
        )}

        {/* Action Buttons */}
        <Separator />
        <div className="flex space-x-2">
          {!framework.isReady && !framework.status.loading && (
            <Button 
              size="sm" 
              onClick={framework.initializeFramework}
              disabled={framework.status.loading}
              className="flex-1"
            >
              <Zap className="h-4 w-4 mr-2" />
              Initialize Framework
            </Button>
          )}
          
          <Button 
            size="sm" 
            variant="outline" 
            onClick={framework.refresh}
            disabled={framework.status.loading}
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>

        {/* Framework Capabilities */}
        {framework.isReady && (
          <>
            <Separator />
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Capabilities</h4>
              <div className="flex flex-wrap gap-1">
                {[
                  'Multi-Agent Coordination',
                  'Load Balancing',
                  'Auto Failover',
                  'Real-time Analytics',
                  'Adaptive Learning'
                ].map((capability) => (
                  <Badge key={capability} variant="secondary" className="text-xs">
                    {capability}
                  </Badge>
                ))}
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}

export default FrameworkStatus
