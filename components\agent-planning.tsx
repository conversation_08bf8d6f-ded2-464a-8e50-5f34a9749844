"use client"

import React, { useState, useEffect } from "react"
import { CheckCircle, Circle, Loader2, Brain, Zap, Target, Settings, ChevronDown, ChevronRight } from "lucide-react"
import { <PERSON>rollArea } from "@/components/ui/scroll-area"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

interface PlanningStep {
  id: string
  title: string
  description: string
  status: "pending" | "running" | "completed" | "error"
  progress?: number
  substeps?: PlanningStep[]
  estimatedTime?: string
  dependencies?: string[]
}

interface AgentPlanningProps {
  mode: "full" | "compact"
  steps: PlanningStep[]
  title?: string
  onStepClick?: (stepId: string) => void
  onComplete?: () => void
  className?: string
}

const defaultSteps: PlanningStep[] = [
  // Empty by default - steps will be populated by backend
]

export default function AgentPlanning({
  mode = "full",
  steps = defaultSteps,
  title = "Project Planning",
  onStepClick,
  onComplete,
  className = ""
}: AgentPlanningProps) {
  const [expandedSteps, setExpandedSteps] = useState<Set<string>>(new Set(["design"]))
  const [currentStep, setCurrentStep] = useState<string>("design")

  useEffect(() => {
    const runningStep = steps.find(step => step.status === "running")
    if (runningStep) {
      setCurrentStep(runningStep.id)
      setExpandedSteps(prev => new Set([...prev, runningStep.id]))
    }
  }, [steps])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="w-4 h-4 text-green-400" />
      case "running":
        return <Loader2 className="w-4 h-4 animate-spin text-blue-400" />
      case "error":
        return <Circle className="w-4 h-4 text-red-400" />
      default:
        return <Circle className="w-4 h-4 text-gray-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "text-green-400"
      case "running":
        return "text-blue-400"
      case "error":
        return "text-red-400"
      default:
        return "text-gray-400"
    }
  }

  const toggleStepExpansion = (stepId: string) => {
    setExpandedSteps(prev => {
      const newSet = new Set(prev)
      if (newSet.has(stepId)) {
        newSet.delete(stepId)
      } else {
        newSet.add(stepId)
      }
      return newSet
    })
  }

  const completedSteps = steps.filter(step => step.status === "completed").length
  const totalSteps = steps.length
  const overallProgress = totalSteps > 0 ? (completedSteps / totalSteps) * 100 : 0

  if (mode === "compact") {
    return (
      <div className={`bg-[#181818] rounded-lg p-4 space-y-3 ${className}`}>
        <div className="flex items-center gap-3">
          <Brain className="w-4 h-4 text-blue-400" />
          <span className="text-sm font-medium text-white">{title}</span>
          <Badge variant="outline" className="text-xs">
            {completedSteps}/{totalSteps}
          </Badge>
        </div>
        
        <div className="space-y-2">
          {steps.slice(0, 3).map((step) => (
            <div key={step.id} className="flex items-center gap-3 text-sm">
              {getStatusIcon(step.status)}
              <span className={`flex-1 ${getStatusColor(step.status)}`}>
                {step.title}
              </span>
              {step.estimatedTime && (
                <span className="text-xs text-gray-500">{step.estimatedTime}</span>
              )}
            </div>
          ))}
          {steps.length > 3 && (
            <div className="text-xs text-gray-500 text-center">
              +{steps.length - 3} more steps
            </div>
          )}
        </div>

        {totalSteps > 0 && (
          <div className="space-y-1">
            <div className="flex justify-between text-xs text-gray-400">
              <span>Progress</span>
              <span>{Math.round(overallProgress)}%</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-1.5">
              <div 
                className="bg-blue-400 h-1.5 rounded-full transition-all duration-300"
                style={{ width: `${overallProgress}%` }}
              />
            </div>
          </div>
        )}
      </div>
    )
  }

  // Full mode
  return (
    <div className={`h-full flex flex-col ${className}`}>
      <div className="flex items-center justify-between p-6 border-b border-[#1a1a1a]">
        <div className="flex items-center gap-3">
          <Brain className="w-5 h-5 text-blue-400" />
          <h2 className="text-lg font-semibold text-white">{title}</h2>
          <Badge variant="outline" className="text-xs">
            {completedSteps}/{totalSteps} Complete
          </Badge>
        </div>
        
        {totalSteps > 0 && (
          <div className="flex items-center gap-3">
            <span className="text-sm text-gray-400">{Math.round(overallProgress)}%</span>
            <div className="w-24 bg-gray-700 rounded-full h-2">
              <div 
                className="bg-blue-400 h-2 rounded-full transition-all duration-300"
                style={{ width: `${overallProgress}%` }}
              />
            </div>
          </div>
        )}
      </div>

      <ScrollArea className="flex-1 p-6">
        <div className="space-y-4">
          {steps.map((step) => (
            <div key={step.id} className="border border-[#1a1a1a] rounded-lg p-4 bg-[#0a0a0a]">
              <div 
                className="flex items-center gap-3 cursor-pointer"
                onClick={() => {
                  toggleStepExpansion(step.id)
                  onStepClick?.(step.id)
                }}
              >
                {getStatusIcon(step.status)}
                <span className={`flex-1 font-medium ${getStatusColor(step.status)}`}>
                  {step.title}
                </span>
                {step.estimatedTime && (
                  <Badge variant="outline" className="text-xs">
                    {step.estimatedTime}
                  </Badge>
                )}
                {step.substeps && step.substeps.length > 0 && (
                  expandedSteps.has(step.id) ? 
                    <ChevronDown className="w-4 h-4 text-gray-400" /> :
                    <ChevronRight className="w-4 h-4 text-gray-400" />
                )}
              </div>
              
              {step.description && (
                <p className="text-sm text-gray-400 mt-2 ml-7">{step.description}</p>
              )}

              {step.progress !== undefined && step.status === "running" && (
                <div className="mt-3 ml-7">
                  <div className="flex justify-between text-xs text-gray-400 mb-1">
                    <span>Progress</span>
                    <span>{step.progress}%</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-1.5">
                    <div 
                      className="bg-blue-400 h-1.5 rounded-full transition-all duration-300"
                      style={{ width: `${step.progress}%` }}
                    />
                  </div>
                </div>
              )}

              {step.substeps && expandedSteps.has(step.id) && (
                <div className="mt-4 ml-7 space-y-2">
                  {step.substeps.map((substep) => (
                    <div key={substep.id} className="flex items-center gap-3 text-sm">
                      {getStatusIcon(substep.status)}
                      <span className={`flex-1 ${getStatusColor(substep.status)}`}>
                        {substep.title}
                      </span>
                      {substep.estimatedTime && (
                        <span className="text-xs text-gray-500">{substep.estimatedTime}</span>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>

        {steps.length === 0 && (
          <div className="text-center py-12">
            <Brain className="w-12 h-12 text-gray-600 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-400 mb-2">No Planning Steps</h3>
            <p className="text-sm text-gray-500">Planning steps will appear here when the agent starts working.</p>
          </div>
        )}

        {overallProgress === 100 && onComplete && (
          <div className="mt-6 text-center">
            <Button onClick={onComplete} className="bg-blue-600 hover:bg-blue-700">
              Deploy Agent
            </Button>
          </div>
        )}
      </ScrollArea>
    </div>
  )
}
