"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface SettingsModalProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  userApiKey: string
  setUserApiKey: (key: string) => void
  preferredModel: string
  setPreferredModel: (model: string) => void
  isAutonomousMode: boolean
  setIsAutonomousMode: (autonomous: boolean) => void
}

export function SettingsModal({
  isOpen,
  onOpenChange,
  userApiKey,
  setUserApiKey,
  preferredModel,
  setPreferredModel,
  isAutonomousMode,
  setIsAutonomousMode,
}: SettingsModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="bg-[#181818] border-gray-700 text-white">
        <DialogHeader>
          <DialogTitle>Settings</DialogTitle>
        </DialogHeader>
        <div className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="api-key">OpenRouter API Key</Label>
            <Input
              id="api-key"
              type="password"
              placeholder="Enter your OpenRouter API key"
              value={userApiKey}
              onChange={(e) => setUserApiKey(e.target.value)}
              className="bg-[#0A0A0A] border-gray-600 text-white"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="model">Preferred Model</Label>
            <Select value={preferredModel} onValueChange={setPreferredModel}>
              <SelectTrigger className="bg-[#0A0A0A] border-gray-600 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-[#000000] border-gray-600 text-white">
                <SelectItem value="anthropic/claude-sonnet-4">Claude Sonnet 4</SelectItem>
                <SelectItem value="anthropic/claude-3.5-sonnet">Claude 3.5 Sonnet</SelectItem>
                <SelectItem value="openai/gpt-4">GPT-4</SelectItem>
                <SelectItem value="openai/gpt-4-turbo">GPT-4 Turbo</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex items-center justify-between">
            <Label htmlFor="autonomous-mode">Autonomous Mode</Label>
            <Switch
              id="autonomous-mode"
              checked={isAutonomousMode}
              onCheckedChange={setIsAutonomousMode}
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
