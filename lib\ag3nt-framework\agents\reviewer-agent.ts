/**
 * AG3NT Framework - Reviewer Agent
 * 
 * Specialized agent for code review, quality assessment, and approval workflows.
 * Provides comprehensive code analysis, best practices validation, and improvement recommendations.
 * 
 * Features:
 * - Automated code review and analysis
 * - Quality metrics and technical debt assessment
 * - Security and performance review
 * - Best practices validation
 * - Improvement recommendations
 * - Approval workflow management
 */

import { BaseAgent, AgentState, AgentConfig } from "../core/base-agent"
import { aiService } from "../../ai-service"

export interface ReviewerInput {
  task: ReviewTask
  codeChanges: CodeChanges
  context: ReviewContext
  criteria: ReviewCriteria
}

export interface ReviewTask {
  taskId: string
  type: 'code_review' | 'architecture_review' | 'security_review' | 'performance_review' | 'quality_review'
  title: string
  description: string
  priority: 'critical' | 'high' | 'medium' | 'low'
  scope: ReviewScope
  deadline?: string
  reviewers: string[]
}

export interface ReviewScope {
  files: string[]
  components: string[]
  features: string[]
  apis: string[]
  changes: ChangeType[]
}

export interface ChangeType {
  type: 'addition' | 'modification' | 'deletion' | 'refactor' | 'optimization'
  impact: 'breaking' | 'major' | 'minor' | 'patch'
  risk: 'high' | 'medium' | 'low'
}

export interface CodeChanges {
  pullRequest?: PullRequestInfo
  commits: CommitInfo[]
  files: FileChange[]
  statistics: ChangeStatistics
}

export interface PullRequestInfo {
  id: string
  title: string
  description: string
  author: string
  branch: string
  target: string
  status: 'open' | 'closed' | 'merged'
  labels: string[]
}

export interface CommitInfo {
  hash: string
  message: string
  author: string
  timestamp: string
  files: string[]
  additions: number
  deletions: number
}

export interface FileChange {
  path: string
  type: 'added' | 'modified' | 'deleted' | 'renamed'
  language: string
  additions: number
  deletions: number
  changes: LineChange[]
  complexity: number
}

export interface LineChange {
  lineNumber: number
  type: 'added' | 'removed' | 'modified'
  content: string
  context: string[]
}

export interface ChangeStatistics {
  totalFiles: number
  totalAdditions: number
  totalDeletions: number
  netChanges: number
  complexity: number
  riskScore: number
}

export interface ReviewContext {
  project: ProjectContext
  codebase: CodebaseContext
  standards: CodingStandards
  history: ReviewHistory
}

export interface ProjectContext {
  name: string
  type: string
  language: string
  framework: string
  version: string
  dependencies: string[]
}

export interface CodebaseContext {
  architecture: string
  patterns: string[]
  conventions: string[]
  testCoverage: number
  technicalDebt: number
  maintainabilityIndex: number
}

export interface CodingStandards {
  style: StyleGuide
  quality: QualityStandards
  security: SecurityStandards
  performance: PerformanceStandards
  documentation: DocumentationStandards
}

export interface StyleGuide {
  formatter: string
  linter: string
  rules: Record<string, any>
  exceptions: string[]
}

export interface QualityStandards {
  complexity: ComplexityThresholds
  coverage: CoverageThresholds
  duplication: DuplicationThresholds
  maintainability: MaintainabilityThresholds
}

export interface ComplexityThresholds {
  cyclomatic: number
  cognitive: number
  nesting: number
  parameters: number
}

export interface CoverageThresholds {
  statements: number
  branches: number
  functions: number
  lines: number
}

export interface DuplicationThresholds {
  blocks: number
  lines: number
  tokens: number
}

export interface MaintainabilityThresholds {
  index: number
  debt: number
  ratio: number
}

export interface SecurityStandards {
  vulnerabilities: string[]
  practices: string[]
  tools: string[]
  compliance: string[]
}

export interface PerformanceStandards {
  metrics: string[]
  thresholds: Record<string, number>
  tools: string[]
  benchmarks: string[]
}

export interface DocumentationStandards {
  coverage: number
  format: string
  sections: string[]
  examples: boolean
}

export interface ReviewHistory {
  previousReviews: PreviousReview[]
  patterns: ReviewPattern[]
  metrics: ReviewMetrics
}

export interface PreviousReview {
  id: string
  date: string
  reviewer: string
  outcome: 'approved' | 'rejected' | 'needs_changes'
  issues: ReviewIssue[]
}

export interface ReviewPattern {
  type: string
  frequency: number
  impact: string
  resolution: string
}

export interface ReviewMetrics {
  averageTime: number
  approvalRate: number
  issueRate: number
  reworkRate: number
}

export interface ReviewCriteria {
  quality: QualityCriteria
  security: SecurityCriteria
  performance: PerformanceCriteria
  maintainability: MaintainabilityCriteria
  compliance: ComplianceCriteria
}

export interface QualityCriteria {
  codeQuality: number
  testCoverage: number
  documentation: number
  complexity: number
  duplication: number
}

export interface SecurityCriteria {
  vulnerabilities: boolean
  authentication: boolean
  authorization: boolean
  dataValidation: boolean
  encryption: boolean
}

export interface PerformanceCriteria {
  efficiency: boolean
  scalability: boolean
  resourceUsage: boolean
  optimization: boolean
  benchmarks: boolean
}

export interface MaintainabilityCriteria {
  readability: boolean
  modularity: boolean
  reusability: boolean
  testability: boolean
  documentation: boolean
}

export interface ComplianceCriteria {
  standards: string[]
  regulations: string[]
  policies: string[]
  guidelines: string[]
}

export interface ReviewerResult {
  taskId: string
  status: 'approved' | 'rejected' | 'needs_changes' | 'pending'
  score: ReviewScore
  issues: ReviewIssue[]
  recommendations: ReviewRecommendation[]
  metrics: ReviewResultMetrics
  approval: ApprovalDecision
  report: ReviewReport
}

export interface ReviewScore {
  overall: number
  quality: number
  security: number
  performance: number
  maintainability: number
  compliance: number
}

export interface ReviewIssue {
  id: string
  type: 'error' | 'warning' | 'suggestion' | 'nitpick'
  category: 'quality' | 'security' | 'performance' | 'maintainability' | 'style'
  severity: 'critical' | 'major' | 'minor' | 'trivial'
  file: string
  line: number
  column?: number
  message: string
  description: string
  suggestion: string
  rule?: string
  effort: 'low' | 'medium' | 'high'
}

export interface ReviewRecommendation {
  type: 'improvement' | 'refactor' | 'optimization' | 'security' | 'documentation'
  priority: 'high' | 'medium' | 'low'
  description: string
  rationale: string
  implementation: string
  effort: string
  impact: string
  examples?: string[]
}

export interface ReviewResultMetrics {
  reviewTime: number
  issuesFound: number
  criticalIssues: number
  majorIssues: number
  minorIssues: number
  linesReviewed: number
  filesReviewed: number
  complexity: number
}

export interface ApprovalDecision {
  approved: boolean
  conditional: boolean
  conditions: string[]
  blockers: string[]
  nextSteps: string[]
  reviewers: string[]
}

export interface ReviewReport {
  summary: ReviewSummary
  details: ReviewDetails
  metrics: ReviewMetrics
  trends: ReviewTrends
  attachments: ReviewAttachment[]
}

export interface ReviewSummary {
  overview: string
  highlights: string[]
  concerns: string[]
  recommendations: string[]
  decision: string
}

export interface ReviewDetails {
  qualityAnalysis: QualityAnalysis
  securityAnalysis: SecurityAnalysis
  performanceAnalysis: PerformanceAnalysis
  maintainabilityAnalysis: MaintainabilityAnalysis
}

export interface QualityAnalysis {
  score: number
  issues: ReviewIssue[]
  metrics: QualityMetrics
  trends: QualityTrends
}

export interface QualityMetrics {
  complexity: number
  duplication: number
  coverage: number
  debt: number
}

export interface QualityTrends {
  improving: boolean
  stable: boolean
  degrading: boolean
  metrics: Record<string, number>
}

export interface SecurityAnalysis {
  score: number
  vulnerabilities: SecurityVulnerability[]
  compliance: SecurityCompliance[]
  recommendations: string[]
}

export interface SecurityVulnerability {
  type: string
  severity: 'critical' | 'high' | 'medium' | 'low'
  description: string
  location: string
  cve?: string
  fix: string
}

export interface SecurityCompliance {
  standard: string
  requirement: string
  status: 'compliant' | 'non-compliant' | 'partial'
  evidence: string
}

export interface PerformanceAnalysis {
  score: number
  bottlenecks: PerformanceBottleneck[]
  optimizations: PerformanceOptimization[]
  benchmarks: PerformanceBenchmark[]
}

export interface PerformanceBottleneck {
  type: string
  location: string
  impact: 'high' | 'medium' | 'low'
  description: string
  solution: string
}

export interface PerformanceOptimization {
  type: string
  description: string
  effort: 'low' | 'medium' | 'high'
  impact: 'high' | 'medium' | 'low'
  implementation: string
}

export interface PerformanceBenchmark {
  metric: string
  current: number
  target: number
  status: 'met' | 'not_met' | 'exceeded'
}

export interface MaintainabilityAnalysis {
  score: number
  index: number
  debt: TechnicalDebt
  refactoring: RefactoringOpportunity[]
}

export interface TechnicalDebt {
  total: number
  critical: number
  major: number
  minor: number
  effort: string
}

export interface RefactoringOpportunity {
  type: string
  location: string
  description: string
  benefit: string
  effort: 'low' | 'medium' | 'high'
  priority: 'high' | 'medium' | 'low'
}

export interface ReviewTrends {
  quality: TrendData
  security: TrendData
  performance: TrendData
  maintainability: TrendData
}

export interface TrendData {
  direction: 'improving' | 'stable' | 'degrading'
  rate: number
  confidence: number
  prediction: string
}

export interface ReviewAttachment {
  type: 'report' | 'chart' | 'log' | 'screenshot'
  name: string
  description: string
  content: string
  format: string
}

/**
 * Reviewer Agent - Comprehensive code review and quality assessment
 */
export class ReviewerAgent extends BaseAgent {
  private readonly reviewSteps = [
    'analyze_changes', 'quality_review', 'security_review', 'performance_review',
    'maintainability_review', 'compliance_review', 'generate_recommendations',
    'make_decision', 'create_report'
  ]

  constructor(config: Partial<AgentConfig> = {}) {
    super('reviewer', {
      capabilities: {
        requiredCapabilities: [
          'code_review',
          'quality_assessment',
          'security_analysis',
          'performance_analysis',
          'maintainability_analysis',
          'compliance_checking',
          'recommendation_generation'
        ],
        contextFilters: ['review', 'quality', 'security', 'performance', 'code'],
        mcpEnhanced: true,
        sequentialThinking: true,
        contextEnrichment: true,
        ragIntegration: true
      },
      ...config
    })
  }

  /**
   * Execute review workflow
   */
  protected async executeWorkflow(state: AgentState): Promise<AgentState> {
    const input = state.input as ReviewerInput
    
    console.log(`🔍 Starting code review: ${input.task.title}`)

    // Execute review steps sequentially
    for (const stepId of this.reviewSteps) {
      console.log(`🔄 Executing step: ${stepId}`)
      
      const stepResult = await this.executeStepWithContext(stepId, input)
      
      if (stepResult.needsInput) {
        state.needsInput = true
        state.question = stepResult.question
        break
      }

      // Update progress
      state.metadata.completedSteps++
      state.metadata.lastUpdated = new Date().toISOString()
      
      this.emit('step_completed', {
        agentId: this.agentId,
        stepId,
        progress: state.metadata.completedSteps / state.metadata.totalSteps
      })
    }

    // Mark as completed if no input needed
    if (!state.needsInput) {
      state.completed = true
      console.log(`✅ Code review completed: ${input.task.title}`)
    }

    return state
  }

  /**
   * Execute individual review step with context enhancement
   */
  private async executeStepWithContext(stepId: string, input?: any): Promise<any> {
    // Get enhanced context from context engine
    const enhancedState = await this.getEnhancedContext(stepId)

    // Execute step with MCP enhancement
    switch (stepId) {
      case 'analyze_changes':
        return await this.analyzeChangesWithMCP(enhancedState, input)
      case 'quality_review':
        return await this.qualityReviewWithMCP(enhancedState)
      case 'security_review':
        return await this.securityReviewWithMCP(enhancedState)
      case 'performance_review':
        return await this.performanceReviewWithMCP(enhancedState)
      case 'maintainability_review':
        return await this.maintainabilityReviewWithMCP(enhancedState)
      case 'compliance_review':
        return await this.complianceReviewWithMCP(enhancedState)
      case 'generate_recommendations':
        return await this.generateRecommendationsWithMCP(enhancedState)
      case 'make_decision':
        return await this.makeDecisionWithMCP(enhancedState)
      case 'create_report':
        return await this.createReportWithMCP(enhancedState)
      default:
        throw new Error(`Unknown review step: ${stepId}`)
    }
  }

  /**
   * Get total steps for progress tracking
   */
  protected getTotalSteps(): number {
    return this.reviewSteps.length
  }

  /**
   * Get relevant documentation for code review
   */
  protected async getRelevantDocumentation(): Promise<Record<string, any>> {
    return {
      codeReview: 'Code review best practices and methodologies',
      qualityAssurance: 'Software quality metrics and assessment techniques',
      security: 'Security code review and vulnerability assessment',
      performance: 'Performance analysis and optimization techniques',
      maintainability: 'Code maintainability and technical debt management',
      compliance: 'Compliance standards and regulatory requirements'
    }
  }

  // ============================================================================
  // STEP IMPLEMENTATIONS WITH MCP ENHANCEMENT
  // ============================================================================

  private async analyzeChangesWithMCP(state: any, input: ReviewerInput): Promise<any> {
    const analysis = await aiService.analyzeCodeChanges(
      input.codeChanges,
      input.context,
      input.criteria
    )

    this.state!.results.changeAnalysis = analysis
    
    return {
      results: analysis,
      needsInput: false,
      completed: false
    }
  }

  private async qualityReviewWithMCP(state: any): Promise<any> {
    const changeAnalysis = this.state!.results.changeAnalysis
    
    const qualityReview = await aiService.performQualityReview(
      changeAnalysis,
      this.state!.input.criteria.quality
    )

    this.state!.results.qualityReview = qualityReview
    
    return {
      results: qualityReview,
      needsInput: false,
      completed: false
    }
  }

  private async securityReviewWithMCP(state: any): Promise<any> {
    const changeAnalysis = this.state!.results.changeAnalysis
    
    const securityReview = await aiService.performSecurityReview(
      changeAnalysis,
      this.state!.input.criteria.security
    )

    this.state!.results.securityReview = securityReview
    
    return {
      results: securityReview,
      needsInput: false,
      completed: false
    }
  }

  private async performanceReviewWithMCP(state: any): Promise<any> {
    const changeAnalysis = this.state!.results.changeAnalysis
    
    const performanceReview = await aiService.performPerformanceReview(
      changeAnalysis,
      this.state!.input.criteria.performance
    )

    this.state!.results.performanceReview = performanceReview
    
    return {
      results: performanceReview,
      needsInput: false,
      completed: false
    }
  }

  private async maintainabilityReviewWithMCP(state: any): Promise<any> {
    const changeAnalysis = this.state!.results.changeAnalysis
    
    const maintainabilityReview = await aiService.performMaintainabilityReview(
      changeAnalysis,
      this.state!.input.criteria.maintainability
    )

    this.state!.results.maintainabilityReview = maintainabilityReview
    
    return {
      results: maintainabilityReview,
      needsInput: false,
      completed: false
    }
  }

  private async complianceReviewWithMCP(state: any): Promise<any> {
    const changeAnalysis = this.state!.results.changeAnalysis
    
    const complianceReview = await aiService.performComplianceReview(
      changeAnalysis,
      this.state!.input.criteria.compliance
    )

    this.state!.results.complianceReview = complianceReview
    
    return {
      results: complianceReview,
      needsInput: false,
      completed: false
    }
  }

  private async generateRecommendationsWithMCP(state: any): Promise<any> {
    const allReviews = {
      quality: this.state!.results.qualityReview,
      security: this.state!.results.securityReview,
      performance: this.state!.results.performanceReview,
      maintainability: this.state!.results.maintainabilityReview,
      compliance: this.state!.results.complianceReview
    }
    
    const recommendations = await aiService.generateReviewRecommendations(
      allReviews,
      this.state!.input.context
    )

    this.state!.results.recommendations = recommendations
    
    return {
      results: recommendations,
      needsInput: false,
      completed: false
    }
  }

  private async makeDecisionWithMCP(state: any): Promise<any> {
    const allReviews = {
      quality: this.state!.results.qualityReview,
      security: this.state!.results.securityReview,
      performance: this.state!.results.performanceReview,
      maintainability: this.state!.results.maintainabilityReview,
      compliance: this.state!.results.complianceReview
    }
    
    const decision = await aiService.makeReviewDecision(
      allReviews,
      this.state!.results.recommendations,
      this.state!.input.criteria
    )

    this.state!.results.decision = decision
    
    return {
      results: decision,
      needsInput: false,
      completed: false
    }
  }

  private async createReportWithMCP(state: any): Promise<any> {
    const allResults = {
      changeAnalysis: this.state!.results.changeAnalysis,
      qualityReview: this.state!.results.qualityReview,
      securityReview: this.state!.results.securityReview,
      performanceReview: this.state!.results.performanceReview,
      maintainabilityReview: this.state!.results.maintainabilityReview,
      complianceReview: this.state!.results.complianceReview,
      recommendations: this.state!.results.recommendations,
      decision: this.state!.results.decision
    }
    
    const report = await aiService.createReviewReport(
      allResults,
      this.state!.input.task
    )

    this.state!.results.report = report
    
    return {
      results: report,
      needsInput: false,
      completed: true // Final step
    }
  }
}

// Export for easy access
export { ReviewerAgent as default }
