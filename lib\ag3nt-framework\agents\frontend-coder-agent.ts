/**
 * AG3NT Framework - Frontend Coder Agent
 * 
 * Specialized agent for frontend development tasks.
 * Handles UI/UX implementation, component development, and frontend architecture.
 * 
 * Features:
 * - React/Vue/Angular component development
 * - UI/UX implementation from designs
 * - Frontend architecture and state management
 * - Responsive design and accessibility
 * - Performance optimization
 * - Testing and quality assurance
 */

import { BaseAgent, AgentState, AgentConfig } from "../core/base-agent"
import { aiService } from "../../ai-service"
import { File, FragmentSchema, AgentOutput } from "../../e2b-schema"

export interface FrontendCoderInput {
  task: FrontendTask
  design: DesignSpecification
  requirements: FrontendRequirements
  codebase: CodebaseContext
}

export interface FrontendTask {
  taskId: string
  type: 'component' | 'page' | 'feature' | 'refactor' | 'optimization' | 'testing'
  title: string
  description: string
  priority: 'critical' | 'high' | 'medium' | 'low'
  acceptanceCriteria: string[]
  technicalRequirements: string[]
}

export interface DesignSpecification {
  wireframes?: any[]
  mockups?: any[]
  designSystem?: DesignSystem
  userFlows?: UserFlow[]
  responsiveBreakpoints?: ResponsiveBreakpoint[]
}

export interface DesignSystem {
  colors: ColorPalette
  typography: Typography
  spacing: SpacingScale
  components: ComponentLibrary
  icons: IconLibrary
}

export interface ColorPalette {
  primary: string[]
  secondary: string[]
  neutral: string[]
  semantic: {
    success: string
    warning: string
    error: string
    info: string
  }
}

export interface Typography {
  fontFamilies: string[]
  fontSizes: number[]
  fontWeights: number[]
  lineHeights: number[]
}

export interface SpacingScale {
  unit: number
  scale: number[]
}

export interface ComponentLibrary {
  [componentName: string]: ComponentSpec
}

export interface ComponentSpec {
  name: string
  variants: string[]
  props: ComponentProp[]
  states: string[]
  examples: any[]
}

export interface ComponentProp {
  name: string
  type: string
  required: boolean
  default?: any
  description: string
}

export interface IconLibrary {
  style: 'outline' | 'filled' | 'duotone'
  icons: string[]
}

export interface UserFlow {
  flowId: string
  name: string
  steps: UserFlowStep[]
  entryPoints: string[]
  exitPoints: string[]
}

export interface UserFlowStep {
  stepId: string
  action: string
  screen: string
  interactions: string[]
  validations: string[]
}

export interface ResponsiveBreakpoint {
  name: string
  minWidth: number
  maxWidth?: number
  columns: number
  gutters: number
}

export interface FrontendRequirements {
  framework: 'react' | 'vue' | 'angular' | 'svelte' | 'vanilla'
  language: 'typescript' | 'javascript'
  styling: 'css' | 'scss' | 'styled-components' | 'tailwind' | 'emotion'
  stateManagement?: 'redux' | 'zustand' | 'mobx' | 'context' | 'pinia'
  testing: 'jest' | 'vitest' | 'cypress' | 'playwright'
  bundler: 'webpack' | 'vite' | 'parcel' | 'rollup'
  accessibility: boolean
  performance: PerformanceRequirements
  browser: BrowserSupport
}

export interface PerformanceRequirements {
  targetLCP: number // Largest Contentful Paint in ms
  targetFID: number // First Input Delay in ms
  targetCLS: number // Cumulative Layout Shift
  bundleSize: number // Max bundle size in KB
  codesplitting: boolean
  lazyLoading: boolean
}

export interface BrowserSupport {
  chrome: string
  firefox: string
  safari: string
  edge: string
  mobile: boolean
}

export interface CodebaseContext {
  projectStructure: ProjectStructure
  existingComponents: ExistingComponent[]
  dependencies: Dependency[]
  buildConfig: BuildConfig
}

export interface ProjectStructure {
  srcDirectory: string
  componentsDirectory: string
  pagesDirectory: string
  stylesDirectory: string
  assetsDirectory: string
  testsDirectory: string
}

export interface ExistingComponent {
  name: string
  path: string
  props: ComponentProp[]
  dependencies: string[]
  usage: string[]
}

export interface Dependency {
  name: string
  version: string
  type: 'dependency' | 'devDependency' | 'peerDependency'
}

export interface BuildConfig {
  entry: string
  output: string
  publicPath: string
  devServer: any
  optimization: any
}

export interface FrontendCoderResult {
  taskId: string
  status: 'completed' | 'failed' | 'needs_review'
  deliverables: Deliverable[]
  codeChanges: CodeChange[]
  testResults: TestResult[]
  performanceMetrics: PerformanceMetrics
  accessibilityReport: AccessibilityReport
  documentation: Documentation[]
}

export interface Deliverable {
  type: 'component' | 'page' | 'style' | 'test' | 'documentation'
  name: string
  path: string
  content: string
  dependencies: string[]
}

export interface CodeChange {
  file: string
  type: 'create' | 'modify' | 'delete'
  changes: string
  linesAdded: number
  linesRemoved: number
}

export interface TestResult {
  testFile: string
  testSuite: string
  passed: number
  failed: number
  coverage: number
  duration: number
}

export interface PerformanceMetrics {
  bundleSize: number
  loadTime: number
  renderTime: number
  interactiveTime: number
  memoryUsage: number
}

export interface AccessibilityReport {
  score: number
  violations: AccessibilityViolation[]
  recommendations: string[]
}

export interface AccessibilityViolation {
  rule: string
  severity: 'error' | 'warning' | 'info'
  element: string
  description: string
  fix: string
}

export interface Documentation {
  type: 'component' | 'api' | 'usage' | 'changelog'
  title: string
  content: string
  examples: any[]
}

/**
 * Frontend Coder Agent - Specialized frontend development
 */
export class FrontendCoderAgent extends BaseAgent {
  private readonly codingSteps = [
    'analyze_requirements', 'plan_implementation', 'setup_environment',
    'develop_components', 'implement_styling', 'add_interactions',
    'optimize_performance', 'ensure_accessibility', 'write_tests', 'document_code'
  ]

  constructor(config: Partial<AgentConfig> = {}) {
    super('frontend-coder', {
      capabilities: {
        requiredCapabilities: [
          'frontend_development',
          'ui_implementation',
          'component_architecture',
          'responsive_design',
          'performance_optimization',
          'accessibility_compliance',
          'frontend_testing'
        ],
        contextFilters: ['frontend', 'ui', 'components', 'design', 'code'],
        mcpEnhanced: true,
        sequentialThinking: true,
        contextEnrichment: true,
        ragIntegration: true
      },
      ...config
    })
  }

  /**
   * Execute frontend coding workflow
   */
  protected async executeWorkflow(state: AgentState): Promise<AgentState> {
    const input = state.input as FrontendCoderInput
    
    console.log(`💻 Starting frontend development: ${input.task.title}`)

    // Execute coding steps sequentially
    for (const stepId of this.codingSteps) {
      console.log(`🔄 Executing step: ${stepId}`)
      
      const stepResult = await this.executeStepWithContext(stepId, input)
      
      if (stepResult.needsInput) {
        state.needsInput = true
        state.question = stepResult.question
        break
      }

      if (stepResult.needsReview) {
        state.results.status = 'needs_review'
        state.results.reviewReason = stepResult.reviewReason
        break
      }

      // Update progress
      state.metadata.completedSteps++
      state.metadata.lastUpdated = new Date().toISOString()
      
      this.emit('step_completed', {
        agentId: this.agentId,
        stepId,
        progress: state.metadata.completedSteps / state.metadata.totalSteps
      })
    }

    // Mark as completed if no input needed and no review required
    if (!state.needsInput && state.results.status !== 'needs_review') {
      state.completed = true
      console.log(`✅ Frontend development completed: ${input.task.title}`)
    }

    return state
  }

  /**
   * Execute individual coding step with context enhancement
   */
  private async executeStepWithContext(stepId: string, input?: any): Promise<any> {
    // Get enhanced context from context engine
    const enhancedState = await this.getEnhancedContext(stepId)

    // Execute step with MCP enhancement
    switch (stepId) {
      case 'analyze_requirements':
        return await this.analyzeRequirementsWithMCP(enhancedState, input)
      case 'plan_implementation':
        return await this.planImplementationWithMCP(enhancedState)
      case 'setup_environment':
        return await this.setupEnvironmentWithMCP(enhancedState)
      case 'develop_components':
        return await this.developComponentsWithMCP(enhancedState)
      case 'implement_styling':
        return await this.implementStylingWithMCP(enhancedState)
      case 'add_interactions':
        return await this.addInteractionsWithMCP(enhancedState)
      case 'optimize_performance':
        return await this.optimizePerformanceWithMCP(enhancedState)
      case 'ensure_accessibility':
        return await this.ensureAccessibilityWithMCP(enhancedState)
      case 'write_tests':
        return await this.writeTestsWithMCP(enhancedState)
      case 'document_code':
        return await this.documentCodeWithMCP(enhancedState)
      default:
        throw new Error(`Unknown frontend coding step: ${stepId}`)
    }
  }

  /**
   * Get total steps for progress tracking
   */
  protected getTotalSteps(): number {
    return this.codingSteps.length
  }

  /**
   * Get relevant documentation for frontend development
   */
  protected async getRelevantDocumentation(): Promise<Record<string, any>> {
    return {
      frontendDevelopment: 'Modern frontend development practices and patterns',
      componentArchitecture: 'Component-based architecture and design patterns',
      responsiveDesign: 'Responsive web design and mobile-first development',
      accessibility: 'Web accessibility guidelines and WCAG compliance',
      performance: 'Frontend performance optimization techniques',
      testing: 'Frontend testing strategies and best practices'
    }
  }

  // ============================================================================
  // STEP IMPLEMENTATIONS WITH MCP ENHANCEMENT
  // ============================================================================

  private async analyzeRequirementsWithMCP(state: any, input: FrontendCoderInput): Promise<any> {
    const analysis = await aiService.analyzeFrontendRequirements(
      input.task,
      input.design,
      input.requirements,
      input.codebase
    )

    this.state!.results.requirementsAnalysis = analysis
    
    return {
      results: analysis,
      needsInput: false,
      completed: false
    }
  }

  private async planImplementationWithMCP(state: any): Promise<any> {
    const requirementsAnalysis = this.state!.results.requirementsAnalysis
    
    const implementationPlan = await aiService.planFrontendImplementation(
      requirementsAnalysis,
      this.state!.input.codebase
    )

    this.state!.results.implementationPlan = implementationPlan
    
    return {
      results: implementationPlan,
      needsInput: false,
      completed: false
    }
  }

  private async setupEnvironmentWithMCP(state: any): Promise<any> {
    const implementationPlan = this.state!.results.implementationPlan
    
    const environmentSetup = await aiService.setupFrontendEnvironment(
      implementationPlan,
      this.state!.input.requirements
    )

    this.state!.results.environmentSetup = environmentSetup
    
    return {
      results: environmentSetup,
      needsInput: false,
      completed: false
    }
  }

  private async developComponentsWithMCP(state: any): Promise<any> {
    const implementationPlan = this.state!.results.implementationPlan
    
    const components = await aiService.developFrontendComponents(
      implementationPlan,
      this.state!.input.design,
      this.state!.input.requirements
    )

    this.state!.results.components = components
    
    return {
      results: components,
      needsInput: false,
      completed: false
    }
  }

  private async implementStylingWithMCP(state: any): Promise<any> {
    const components = this.state!.results.components
    
    const styling = await aiService.implementFrontendStyling(
      components,
      this.state!.input.design.designSystem,
      this.state!.input.requirements.styling
    )

    this.state!.results.styling = styling
    
    return {
      results: styling,
      needsInput: false,
      completed: false
    }
  }

  private async addInteractionsWithMCP(state: any): Promise<any> {
    const components = this.state!.results.components
    
    const interactions = await aiService.addFrontendInteractions(
      components,
      this.state!.input.design.userFlows,
      this.state!.input.requirements.stateManagement
    )

    this.state!.results.interactions = interactions
    
    return {
      results: interactions,
      needsInput: false,
      completed: false
    }
  }

  private async optimizePerformanceWithMCP(state: any): Promise<any> {
    const components = this.state!.results.components
    
    const optimization = await aiService.optimizeFrontendPerformance(
      components,
      this.state!.input.requirements.performance
    )

    this.state!.results.optimization = optimization
    
    return {
      results: optimization,
      needsInput: false,
      completed: false
    }
  }

  private async ensureAccessibilityWithMCP(state: any): Promise<any> {
    const components = this.state!.results.components
    
    const accessibility = await aiService.ensureFrontendAccessibility(
      components,
      this.state!.input.requirements.accessibility
    )

    this.state!.results.accessibility = accessibility
    
    return {
      results: accessibility,
      needsInput: false,
      completed: false
    }
  }

  private async writeTestsWithMCP(state: any): Promise<any> {
    const components = this.state!.results.components
    
    const tests = await aiService.writeFrontendTests(
      components,
      this.state!.input.requirements.testing
    )

    this.state!.results.tests = tests
    
    return {
      results: tests,
      needsInput: false,
      completed: false
    }
  }

  private async documentCodeWithMCP(state: any): Promise<any> {
    const components = this.state!.results.components
    
    const documentation = await aiService.documentFrontendCode(
      components,
      this.state!.input.task
    )

    this.state!.results.documentation = documentation
    
    return {
      results: documentation,
      needsInput: false,
      completed: true // Final step
    }
  }

  /**
   * Generate E2B-compatible code files
   */
  async generateE2BFiles(task: FrontendTask, requirements: FrontendRequirements): Promise<File[]> {
    const files: File[] = []

    // Generate package.json for Next.js project
    files.push({
      file_path: 'package.json',
      file_content: this.generatePackageJson(requirements),
      file_type: 'json'
    })

    // Generate main page component
    files.push({
      file_path: 'pages/index.tsx',
      file_content: this.generateMainPage(task, requirements),
      file_type: 'typescript-react'
    })

    // Generate components based on task requirements
    if (requirements.components && requirements.components.length > 0) {
      for (const component of requirements.components) {
        files.push({
          file_path: `components/${component.name}.tsx`,
          file_content: this.generateComponent(component, requirements),
          file_type: 'typescript-react'
        })
      }
    }

    // Generate styling configuration
    files.push({
      file_path: 'tailwind.config.js',
      file_content: this.generateTailwindConfig(requirements),
      file_type: 'javascript'
    })

    // Generate TypeScript configuration
    files.push({
      file_path: 'tsconfig.json',
      file_content: this.generateTSConfig(),
      file_type: 'json'
    })

    // Generate Next.js configuration
    files.push({
      file_path: 'next.config.js',
      file_content: this.generateNextConfig(),
      file_type: 'javascript'
    })

    return files
  }

  /**
   * Generate package.json for E2B sandbox
   */
  private generatePackageJson(requirements: FrontendRequirements): string {
    const dependencies: Record<string, string> = {
      'next': '^14.0.0',
      'react': '^18.0.0',
      'react-dom': '^18.0.0',
      'typescript': '^5.0.0',
      '@types/node': '^20.0.0',
      '@types/react': '^18.0.0',
      '@types/react-dom': '^18.0.0'
    }

    // Add styling dependencies
    if (requirements.styling?.framework === 'tailwind') {
      dependencies['tailwindcss'] = '^3.3.0'
      dependencies['autoprefixer'] = '^10.4.0'
      dependencies['postcss'] = '^8.4.0'
    }

    // Add state management dependencies
    if (requirements.stateManagement?.library === 'zustand') {
      dependencies['zustand'] = '^4.4.0'
    } else if (requirements.stateManagement?.library === 'redux') {
      dependencies['@reduxjs/toolkit'] = '^1.9.0'
      dependencies['react-redux'] = '^8.1.0'
    }

    // Add UI library dependencies
    if (requirements.uiLibrary === 'shadcn') {
      dependencies['@radix-ui/react-slot'] = '^1.0.0'
      dependencies['class-variance-authority'] = '^0.7.0'
      dependencies['clsx'] = '^2.0.0'
      dependencies['tailwind-merge'] = '^1.14.0'
    }

    const packageJson = {
      name: 'ag3nt-generated-frontend',
      version: '0.1.0',
      private: true,
      scripts: {
        dev: 'next dev',
        build: 'next build',
        start: 'next start',
        lint: 'next lint'
      },
      dependencies
    }

    return JSON.stringify(packageJson, null, 2)
  }

  /**
   * Generate main page component
   */
  private generateMainPage(task: FrontendTask, requirements: FrontendRequirements): string {
    const componentImports = requirements.components?.map(c =>
      `import ${c.name} from '../components/${c.name}'`
    ).join('\n') || ''

    return `import React from 'react'
import Head from 'next/head'
${componentImports}

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 text-white">
      <Head>
        <title>${task.title} - AG3NT Generated</title>
        <meta name="description" content="${task.description}" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <main className="container mx-auto px-4 py-8">
        <div className="text-center mb-12">
          <h1 className="text-5xl font-bold mb-4 bg-gradient-to-r from-green-400 via-blue-500 to-purple-600 bg-clip-text text-transparent">
            ${task.title}
          </h1>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            ${task.description}
          </p>
          <div className="mt-6 text-sm text-green-400">
            🤖 Autonomously generated by AG3NT Framework
          </div>
        </div>

        <div className="space-y-8">
          ${requirements.components?.map(c => `<${c.name} />`).join('\n          ') || ''}
        </div>
      </main>
    </div>
  )
}`
  }

  /**
   * Generate component code
   */
  private generateComponent(component: any, requirements: FrontendRequirements): string {
    // This would be enhanced with actual AI generation based on component specs
    return `import React from 'react'

interface ${component.name}Props {
  className?: string
}

export default function ${component.name}({ className = '' }: ${component.name}Props) {
  return (
    <div className={\`bg-gray-800 border border-gray-700 rounded-lg p-6 \${className}\`}>
      <h2 className="text-2xl font-semibold mb-4 text-green-400">
        ${component.name}
      </h2>
      <p className="text-gray-300">
        This is the ${component.name} component generated by AG3NT.
      </p>
      <div className="mt-4 p-4 bg-gray-900 rounded border border-green-400">
        <code className="text-green-400 text-sm">
          Component: ${component.name}
        </code>
      </div>
    </div>
  )
}`
  }

  /**
   * Generate Tailwind configuration
   */
  private generateTailwindConfig(requirements: FrontendRequirements): string {
    return `/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        'ag3nt-green': '#00ff41',
        'ag3nt-blue': '#0066ff',
        'ag3nt-purple': '#9933ff',
      },
      animation: {
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'glow': 'glow 2s ease-in-out infinite alternate',
      },
      keyframes: {
        glow: {
          '0%': { textShadow: '0 0 5px #00ff41, 0 0 10px #00ff41, 0 0 15px #00ff41' },
          '100%': { textShadow: '0 0 10px #00ff41, 0 0 20px #00ff41, 0 0 30px #00ff41' },
        }
      }
    },
  },
  plugins: [],
}`
  }

  /**
   * Generate TypeScript configuration
   */
  private generateTSConfig(): string {
    const tsConfig = {
      compilerOptions: {
        target: 'es5',
        lib: ['dom', 'dom.iterable', 'es6'],
        allowJs: true,
        skipLibCheck: true,
        strict: true,
        forceConsistentCasingInFileNames: true,
        noEmit: true,
        esModuleInterop: true,
        module: 'esnext',
        moduleResolution: 'node',
        resolveJsonModule: true,
        isolatedModules: true,
        jsx: 'preserve',
        incremental: true,
        plugins: [{ name: 'next' }],
        paths: {
          '@/*': ['./*']
        }
      },
      include: ['next-env.d.ts', '**/*.ts', '**/*.tsx', '.next/types/**/*.ts'],
      exclude: ['node_modules']
    }

    return JSON.stringify(tsConfig, null, 2)
  }

  /**
   * Generate Next.js configuration
   */
  private generateNextConfig(): string {
    return `/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  experimental: {
    appDir: false
  }
}

module.exports = nextConfig`
  }

  /**
   * Create E2B fragment from generated files
   */
  async createE2BFragment(task: FrontendTask, requirements: FrontendRequirements): Promise<FragmentSchema> {
    const files = await this.generateE2BFiles(task, requirements)

    return {
      title: `AG3NT Frontend - ${task.title}`,
      description: task.description,
      template: 'nextjs-developer',
      is_multi_file: true,
      files,
      has_additional_dependencies: true,
      additional_dependencies: files.find(f => f.file_path === 'package.json') ?
        Object.keys(JSON.parse(files.find(f => f.file_path === 'package.json')!.file_content).dependencies) : [],
      install_dependencies_command: 'npm install',
      port: 3000,
      framework: 'nextjs',
      language: 'typescript',
      project_type: 'frontend'
    }
  }
}

// Export for easy access
export { FrontendCoderAgent as default }
