import { useState } from "react"
import { StreamingUpdate } from "@/lib/e2b-schema"

export function useCodingState() {
  // Coding workflow state
  const [isCoding, setIsCoding] = useState(false)
  const [codingTasks, setCodingTasks] = useState<any[]>([])
  const [currentCodingTask, setCurrentCodingTask] = useState<any>(null)
  const [activeFile, setActiveFile] = useState<string | null>(null)
  const [liveCode, setLiveCode] = useState<string>("")

  // E2B state
  const [e2bSandboxes, setE2bSandboxes] = useState<any[]>([])
  const [activeSandbox, setActiveSandbox] = useState<any | null>(null)
  const [streamingUpdates, setStreamingUpdates] = useState<StreamingUpdate[]>([])

  return {
    // Coding workflow state
    isCoding,
    setIsCoding,
    codingTasks,
    setCodingTasks,
    currentCodingTask,
    setCurrentCodingTask,
    activeFile,
    setActiveFile,
    liveCode,
    setLiveCode,

    // E2B state
    e2bSandboxes,
    setE2bSandboxes,
    activeSandbox,
    setActiveSandbox,
    streamingUpdates,
    setStreamingUpdates,
  }
}
