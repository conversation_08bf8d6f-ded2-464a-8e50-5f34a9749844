/**
 * Quick Framework Test
 */

console.log('🧪 Testing AG3NT Framework Integration...')

// Test basic imports
try {
  console.log('📦 Testing framework service import...')
  const { frameworkService } = require('./lib/framework-service.ts')
  console.log('✅ Framework service imported successfully')
  
  // Test framework status
  const status = frameworkService.getStatus()
  console.log('📊 Framework status:', status)
  
  console.log('🎉 Basic framework test passed!')
  
} catch (error) {
  console.error('❌ Framework test failed:', error.message)
  console.log('\n🔧 This is expected - the framework needs to be properly built')
  console.log('The platform should still work with the existing planning system')
}
