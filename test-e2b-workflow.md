# AG3NT E2B Integration - End-to-End Workflow Test

## Overview
This document outlines the complete testing procedure for the AG3NT platform's E2B integration, from planning to autonomous coding to live preview.

## Test Scenario: Simple Calculator App

### 1. Planning Phase Test
**Input Prompt:** "Create a simple calculator app with basic arithmetic operations"

**Expected Behavior:**
- Planning agent starts automatically
- All planning tasks complete successfully
- Results include:
  - Project analysis
  - Tech stack selection (Next.js, React, TypeScript)
  - Wireframes with ASCII mockups
  - Database schema (if needed)
  - File system structure
  - Task breakdown

**Verification Points:**
- [ ] Planning tasks appear in chat sidebar
- [ ] Each task shows completion status
- [ ] Planning tab shows detailed results
- [ ] No errors in browser console
- [ ] Planning completes within 2-3 minutes

### 2. Autonomous Coding Transition Test

**Expected Behavior:**
- Planning completion triggers automatic coding workflow
- Chat message appears explaining the transition
- UI automatically switches to "Code" tab
- Coding tasks appear with status indicators

**Verification Points:**
- [ ] Automatic transition message appears in chat
- [ ] Active tab switches to "Code"
- [ ] Coding tasks list appears with 10 tasks
- [ ] Live coding indicator (green dot) appears on Code tab
- [ ] File explorer shows initial structure

### 3. E2B Sandbox Creation Test

**Expected Behavior:**
- Frontend tasks trigger E2B sandbox creation
- Sandbox API receives proper file structure
- E2B sandbox is created with Next.js template
- Live preview becomes available

**Verification Points:**
- [ ] Sandbox creation API calls succeed
- [ ] Sandbox ID is returned
- [ ] Sandbox URL is accessible
- [ ] Preview tab shows sandbox count badge
- [ ] E2B preview component loads

### 4. Live Code Generation Test

**Expected Behavior:**
- Code tab shows live file generation
- Files appear in real-time as agents work
- Code content is realistic and functional
- Progress updates stream in real-time

**Verification Points:**
- [ ] File explorer populates with generated files
- [ ] Selected files show actual code content
- [ ] Code syntax highlighting works
- [ ] Live generation indicator shows activity
- [ ] Streaming updates appear in Updates tab

### 5. Live Preview Test

**Expected Behavior:**
- Preview tab shows E2B sandbox iframe
- Calculator app loads and functions
- Responsive design controls work
- External link opens in new tab

**Verification Points:**
- [ ] Preview iframe loads successfully
- [ ] Calculator interface appears
- [ ] Basic arithmetic operations work
- [ ] Viewport controls (desktop/tablet/mobile) function
- [ ] "Open in New Tab" button works
- [ ] Sandbox status shows "Ready"

### 6. Streaming Updates Test

**Expected Behavior:**
- Updates tab shows real-time progress
- Console output appears for debugging
- Error handling works properly
- Update timestamps are accurate

**Verification Points:**
- [ ] Streaming updates appear in real-time
- [ ] Different update types (file, generation, completion) show
- [ ] Console tab shows relevant output
- [ ] Error messages (if any) are clear
- [ ] Timestamps are current and formatted correctly

## API Endpoints Test

### Required API Calls
1. `POST /api/planning` - Start planning
2. `POST /api/planning/step` - Execute planning steps
3. `POST /api/coding` - Start coding workflow
4. `GET /api/coding?action=progress` - Get coding progress
5. `GET /api/coding?action=streaming_updates` - Get streaming updates
6. `POST /api/sandbox` - Create E2B sandbox
7. `GET /api/coding?action=sandboxes` - Get sandbox list

### API Response Validation
- [ ] All API calls return proper JSON responses
- [ ] Error responses include helpful messages
- [ ] Sandbox creation returns valid URL
- [ ] Progress updates include sandbox information
- [ ] Streaming updates follow correct schema

## Performance Test

### Timing Expectations
- Planning phase: 2-3 minutes
- Coding workflow start: < 10 seconds
- Sandbox creation: 30-60 seconds
- First preview available: 2-3 minutes
- Complete workflow: 5-8 minutes

### Resource Usage
- [ ] No memory leaks during long sessions
- [ ] CPU usage remains reasonable
- [ ] Network requests are efficient
- [ ] Browser remains responsive

## Error Handling Test

### Simulated Failures
1. **E2B API Key Missing**
   - Expected: Clear error message about authentication
   - Fallback: Graceful degradation without preview

2. **Sandbox Creation Failure**
   - Expected: Error message with retry option
   - Fallback: Code generation continues without preview

3. **Network Connectivity Issues**
   - Expected: Retry mechanisms activate
   - Fallback: Offline mode with cached data

### Error Recovery
- [ ] System recovers from temporary failures
- [ ] User receives clear error messages
- [ ] Retry mechanisms work properly
- [ ] No data loss during errors

## Browser Compatibility Test

### Supported Browsers
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)

### Mobile Responsiveness
- [ ] Planning interface works on mobile
- [ ] Code editor is usable on tablets
- [ ] Preview iframe scales properly
- [ ] Touch interactions work correctly

## Security Test

### Data Validation
- [ ] User input is properly sanitized
- [ ] API endpoints validate parameters
- [ ] E2B sandbox isolation works
- [ ] No sensitive data in client logs

### Access Control
- [ ] Sandbox URLs are properly scoped
- [ ] API endpoints require proper authentication
- [ ] File access is restricted to sandbox
- [ ] No unauthorized code execution

## Success Criteria

### Minimum Viable Test
✅ **PASS**: User can input prompt, see planning complete, watch coding begin, and view live preview

### Full Feature Test
✅ **PASS**: All components work together seamlessly with proper error handling and performance

### Production Readiness
✅ **PASS**: System handles edge cases, scales properly, and provides excellent user experience

## Test Results

**Date:** [To be filled during testing]
**Tester:** [To be filled during testing]
**Environment:** [Development/Staging/Production]

### Overall Status: [PASS/FAIL/PARTIAL]

### Issues Found:
1. [Issue description]
2. [Issue description]
3. [Issue description]

### Recommendations:
1. [Recommendation]
2. [Recommendation]
3. [Recommendation]

---

## Quick Test Commands

```bash
# Start development server
npm run dev

# Test API endpoints
curl -X GET "http://localhost:3000/api/coding?action=status"
curl -X GET "http://localhost:3000/api/coding?action=progress"
curl -X GET "http://localhost:3000/api/coding?action=streaming_updates"

# Check E2B integration
curl -X POST "http://localhost:3000/api/sandbox" \
  -H "Content-Type: application/json" \
  -d '{"template": "nextjs-developer", "is_multi_file": true}'
```

## Next Steps After Testing

1. **Performance Optimization**
   - Optimize API response times
   - Implement caching strategies
   - Reduce bundle sizes

2. **Feature Enhancements**
   - Add more E2B templates
   - Implement real-time collaboration
   - Add deployment automation

3. **Production Deployment**
   - Set up monitoring
   - Configure error tracking
   - Implement analytics

4. **Documentation Updates**
   - Update user guides
   - Create developer documentation
   - Record demo videos
