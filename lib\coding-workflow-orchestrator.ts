/**
 * AG3NT Platform - Coding Workflow Orchestrator
 *
 * Orchestrates the automatic transition from planning to coding execution
 * Enhanced with E2B sandbox integration for live preview and code execution
 */

import { FragmentSchema, ExecutionResult, CodingTask as E2BCodingTask, StreamingUpdate } from './e2b-schema'
import { ProjectPlan, isAG3NTPlanningResults } from './project-plan-schema'

export interface CodingTask {
  id: string
  type: 'frontend' | 'backend' | 'database' | 'testing' | 'deployment' | 'integration'
  title: string
  description: string
  dependencies: string[]
  estimatedTime: number
  priority: 'high' | 'medium' | 'low'
  status: 'pending' | 'in_progress' | 'completed' | 'failed'
  agentId?: string
  startTime?: number
  endTime?: number
  output?: {
    files?: string[]
    code?: string
    tests?: string[]
    documentation?: string
    metrics?: Record<string, any>
    sandboxId?: string
    sandboxUrl?: string
  }
  error?: string
}

export interface CodingProgress {
  totalTasks: number
  completedTasks: number
  inProgressTasks: number
  failedTasks: number
  currentPhase: string
  estimatedCompletion: number
  activeAgents: string[]
  sandboxes: SandboxInfo[]
  streamingUpdates: StreamingUpdate[]
}

export interface SandboxInfo {
  id: string
  template: string
  url?: string
  status: 'creating' | 'ready' | 'error'
  taskId?: string
}

export interface ProjectPlan {
  projectName: string
  projectDescription: string
  architecture: any
  techStack: any
  features: string[]
  wireframes: any
  design: any
  filesystem: any
  workflow: any
  tasks: any
}

/**
 * Coding Workflow Orchestrator - Manages autonomous code generation with E2B integration
 */
export class CodingWorkflowOrchestrator {
  private codingTasks: CodingTask[] = []
  private activeAgents: Map<string, any> = new Map()
  private projectPlan: ProjectPlan | null = null
  private isRunning = false
  private progressCallbacks: ((progress: CodingProgress) => void)[] = []
  private sandboxes: Map<string, SandboxInfo> = new Map()
  private streamingUpdates: StreamingUpdate[] = []
  private updateCallbacks: ((update: StreamingUpdate) => void)[] = []

  /**
   * Start the coding workflow from a completed plan
   */
  async startCodingWorkflow(plan: ProjectPlan): Promise<void> {
    if (this.isRunning) {
      throw new Error('Coding workflow is already running')
    }

    console.log('🚀 Starting autonomous coding workflow...')
    this.projectPlan = plan
    this.isRunning = true

    try {
      // Generate coding tasks from the plan
      this.codingTasks = this.generateCodingTasks(plan)
      
      // Initialize coding agents
      await this.initializeCodingAgents()
      
      // Start parallel execution
      await this.executeCodingTasks()
      
      console.log('✅ Coding workflow completed successfully')
      
    } catch (error) {
      console.error('❌ Coding workflow failed:', error)
      throw error
    } finally {
      this.isRunning = false
    }
  }

  /**
   * Generate coding tasks from the project plan
   */
  private generateCodingTasks(plan: ProjectPlan): CodingTask[] {
    const tasks: CodingTask[] = []

    // Extract tasks from plan based on format
    let planTasks: any[] = []

    if (isAG3NTPlanningResults(plan)) {
      // AG3NT planning results format
      const ag3ntPlan = plan as any
      planTasks = ag3ntPlan.results?.tasks?.breakdown || []
      console.log(`📋 Found ${planTasks.length} tasks in AG3NT planning results`)
    } else {
      // Legacy format (from planning agent or custom upload)
      const legacyPlan = plan as any

      // Handle different task structures in legacy format
      if (legacyPlan.tasks) {
        if (Array.isArray(legacyPlan.tasks)) {
          // Direct array of tasks
          planTasks = legacyPlan.tasks
        } else if (legacyPlan.tasks.breakdown) {
          // Tasks with breakdown structure
          planTasks = legacyPlan.tasks.breakdown
        } else if (typeof legacyPlan.tasks === 'object') {
          // Tasks as object, try to extract array
          planTasks = Object.values(legacyPlan.tasks).filter(Array.isArray).flat()
          if (planTasks.length === 0) {
            // If no arrays found, try to convert object values to tasks
            planTasks = Object.entries(legacyPlan.tasks).map(([key, value]: [string, any]) => ({
              id: key,
              title: value.title || key,
              description: value.description || `Task: ${key}`,
              type: value.type || 'frontend',
              priority: value.priority || 'medium'
            }))
          }
        }
      }

      console.log(`📋 Found ${planTasks.length} tasks in legacy project plan`)
    }

    // Convert plan tasks to coding tasks
    if (planTasks.length > 0) {
      planTasks.forEach((planTask: any, index: number) => {
        tasks.push({
          id: planTask.id || `task-${index + 1}`,
          type: this.mapTaskType(planTask.type || 'frontend'),
          title: planTask.title || `Task ${index + 1}`,
          description: planTask.description || 'Generated task from project plan',
          dependencies: planTask.dependencies || [],
          estimatedTime: this.parseEstimatedTime(planTask.estimatedTime) || 300,
          priority: planTask.priority || 'medium',
          status: 'pending'
        })
      })
    } else {
      // Fallback to default tasks if no tasks in plan
      console.log('📋 No tasks found in plan, generating default tasks')

      // Database setup tasks
      tasks.push({
        id: 'db-schema',
        type: 'database',
        title: 'Create Database Schema',
        description: 'Generate database schema and migrations',
        dependencies: [],
        estimatedTime: 300, // 5 minutes
        priority: 'high',
        status: 'pending'
      })

    // Backend tasks
    tasks.push({
      id: 'backend-setup',
      type: 'backend',
      title: 'Setup Backend Project',
      description: 'Initialize backend project structure',
      dependencies: ['db-schema'],
      estimatedTime: 600, // 10 minutes
      priority: 'high',
      status: 'pending'
    })

    tasks.push({
      id: 'backend-api',
      type: 'backend',
      title: 'Generate API Endpoints',
      description: 'Create REST API endpoints based on requirements',
      dependencies: ['backend-setup'],
      estimatedTime: 900, // 15 minutes
      priority: 'high',
      status: 'pending'
    })

    // Frontend tasks
    tasks.push({
      id: 'frontend-setup',
      type: 'frontend',
      title: 'Setup Frontend Project',
      description: 'Initialize React project with TypeScript',
      dependencies: [],
      estimatedTime: 600, // 10 minutes
      priority: 'high',
      status: 'pending'
    })

    tasks.push({
      id: 'frontend-components',
      type: 'frontend',
      title: 'Generate UI Components',
      description: 'Create React components based on wireframes',
      dependencies: ['frontend-setup'],
      estimatedTime: 1200, // 20 minutes
      priority: 'medium',
      status: 'pending'
    })

    tasks.push({
      id: 'frontend-styling',
      type: 'frontend',
      title: 'Apply Design System',
      description: 'Implement cyberpunk theme and styling',
      dependencies: ['frontend-components'],
      estimatedTime: 900, // 15 minutes
      priority: 'medium',
      status: 'pending'
    })

    // Integration tasks
    tasks.push({
      id: 'api-integration',
      type: 'frontend',
      title: 'Integrate Frontend with API',
      description: 'Connect frontend components to backend API',
      dependencies: ['frontend-components', 'backend-api'],
      estimatedTime: 600, // 10 minutes
      priority: 'high',
      status: 'pending'
    })

    // Testing tasks
    tasks.push({
      id: 'unit-tests',
      type: 'testing',
      title: 'Generate Unit Tests',
      description: 'Create comprehensive unit tests',
      dependencies: ['backend-api', 'frontend-components'],
      estimatedTime: 900, // 15 minutes
      priority: 'medium',
      status: 'pending'
    })

    tasks.push({
      id: 'integration-tests',
      type: 'testing',
      title: 'Generate Integration Tests',
      description: 'Create end-to-end integration tests',
      dependencies: ['api-integration'],
      estimatedTime: 600, // 10 minutes
      priority: 'low',
      status: 'pending'
    })

    // Deployment tasks
    tasks.push({
      id: 'deployment-config',
      type: 'deployment',
      title: 'Setup Deployment Configuration',
      description: 'Configure deployment for Vercel and Railway',
      dependencies: ['api-integration'],
      estimatedTime: 300, // 5 minutes
      priority: 'medium',
      status: 'pending'
    })
    }

    return tasks
  }

  /**
   * Initialize coding agents
   */
  private async initializeCodingAgents(): Promise<void> {
    const agentTypes = ['frontend-coder', 'backend-coder', 'database-agent', 'tester-agent', 'devops-agent']
    
    for (const agentType of agentTypes) {
      const agent = {
        id: agentType,
        type: agentType,
        status: 'ready',
        currentTask: null,
        capabilities: this.getAgentCapabilities(agentType)
      }
      
      this.activeAgents.set(agentType, agent)
      console.log(`🤖 Initialized ${agentType}`)
    }
  }

  /**
   * Get agent capabilities
   */
  private getAgentCapabilities(agentType: string): string[] {
    const capabilities: Record<string, string[]> = {
      'frontend-coder': ['react', 'typescript', 'tailwindcss', 'components', 'hooks'],
      'backend-coder': ['nestjs', 'typescript', 'api', 'controllers', 'services'],
      'database-agent': ['prisma', 'postgresql', 'schema', 'migrations'],
      'tester-agent': ['jest', 'testing-library', 'playwright', 'unit-tests', 'e2e-tests'],
      'devops-agent': ['docker', 'vercel', 'railway', 'ci-cd', 'deployment']
    }
    
    return capabilities[agentType] || []
  }

  /**
   * Execute coding tasks in parallel where possible
   */
  private async executeCodingTasks(): Promise<void> {
    const maxConcurrentTasks = 3
    const runningTasks: Promise<void>[] = []

    while (this.codingTasks.some(task => task.status === 'pending') || runningTasks.length > 0) {
      // Find tasks that can be started (dependencies met)
      const readyTasks = this.codingTasks.filter(task => 
        task.status === 'pending' && 
        this.areDependenciesMet(task) &&
        runningTasks.length < maxConcurrentTasks
      )

      // Start ready tasks
      for (const task of readyTasks.slice(0, maxConcurrentTasks - runningTasks.length)) {
        const taskPromise = this.executeTask(task)
        runningTasks.push(taskPromise)
        
        // Remove completed tasks from running list
        taskPromise.finally(() => {
          const index = runningTasks.indexOf(taskPromise)
          if (index > -1) {
            runningTasks.splice(index, 1)
          }
        })
      }

      // Wait a bit before checking again
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Update progress
      this.notifyProgress()
    }
  }

  /**
   * Check if task dependencies are met
   */
  private areDependenciesMet(task: CodingTask): boolean {
    return task.dependencies.every(depId => {
      const depTask = this.codingTasks.find(t => t.id === depId)
      return depTask?.status === 'completed'
    })
  }

  /**
   * Execute a single coding task with E2B integration
   */
  private async executeTask(task: CodingTask): Promise<void> {
    console.log(`🔨 Starting task: ${task.title}`)

    task.status = 'in_progress'
    task.startTime = Date.now()

    this.addStreamingUpdate({
      type: 'generation',
      message: `Starting ${task.title}...`,
      taskId: task.id,
      timestamp: Date.now()
    })

    try {
      // Execute task with agent (simulate for now)
      await this.simulateTaskExecution(task)

      // Create E2B sandbox for frontend tasks with generated files
      if (task.type === 'frontend' && task.output?.files) {
        await this.createSandboxForTask(task)
      }

      task.status = 'completed'
      task.endTime = Date.now()

      this.addStreamingUpdate({
        type: 'completion',
        message: `Completed ${task.title}`,
        taskId: task.id,
        timestamp: Date.now()
      })

      console.log(`✅ Completed task: ${task.title}`)

    } catch (error) {
      task.status = 'failed'
      task.endTime = Date.now()
      task.error = error instanceof Error ? error.message : 'Unknown error'

      this.addStreamingUpdate({
        type: 'error',
        message: `Failed ${task.title}: ${task.error}`,
        taskId: task.id,
        timestamp: Date.now()
      })

      console.error(`❌ Failed task: ${task.title}`, error)
    }
  }

  /**
   * Create E2B sandbox for a completed task
   */
  private async createSandboxForTask(task: CodingTask): Promise<void> {
    if (!task.output?.files) return

    try {
      // Generate file content for the task
      const files = this.generateFileContent(task)

      // Create sandbox
      const sandboxId = await this.createSandbox(task.id, files)

      // Update task output with sandbox info
      if (task.output) {
        task.output.sandboxId = sandboxId
        const sandbox = this.sandboxes.get(sandboxId)
        if (sandbox?.url) {
          task.output.sandboxUrl = sandbox.url
        }
      }
    } catch (error) {
      console.error(`Failed to create sandbox for task ${task.id}:`, error)
      // Don't fail the task just because sandbox creation failed
    }
  }

  /**
   * Simulate task execution (replace with actual agent calls)
   */
  private async simulateTaskExecution(task: CodingTask): Promise<void> {
    // Simulate work time
    const workTime = Math.min(task.estimatedTime, 5000) // Cap at 5 seconds for demo
    await new Promise(resolve => setTimeout(resolve, workTime))
    
    // Generate mock output based on task type
    task.output = this.generateTaskOutput(task)
  }

  /**
   * Generate mock output for tasks
   */
  private generateTaskOutput(task: CodingTask): any {
    const outputs: Record<string, any> = {
      'db-schema': {
        files: [
          'prisma/schema.prisma',
          'prisma/migrations/001_init.sql',
          'prisma/migrations/002_add_calculations.sql'
        ],
        tables: ['User', 'Calculation', 'History'],
        relationships: 2,
        models: 3
      },
      'backend-setup': {
        files: [
          'src/main.ts',
          'src/app.module.ts',
          'src/app.controller.ts',
          'src/app.service.ts',
          'package.json',
          'tsconfig.json',
          '.env.example'
        ],
        modules: ['AppModule', 'DatabaseModule', 'AuthModule', 'CalculatorModule'],
        dependencies: 18,
        scripts: 5
      },
      'backend-api': {
        files: [
          'src/calculator/calculator.controller.ts',
          'src/calculator/calculator.service.ts',
          'src/calculator/calculator.module.ts',
          'src/calculator/dto/calculate.dto.ts',
          'src/history/history.controller.ts',
          'src/history/history.service.ts'
        ],
        endpoints: ['/api/calculate', '/api/history', '/api/auth/login', '/api/auth/register'],
        controllers: ['CalculatorController', 'HistoryController', 'AuthController'],
        services: ['CalculatorService', 'HistoryService', 'AuthService'],
        dtos: 4
      },
      'frontend-setup': {
        files: [
          'src/App.tsx',
          'src/main.tsx',
          'src/index.html',
          'package.json',
          'vite.config.ts',
          'tailwind.config.js',
          'tsconfig.json'
        ],
        components: ['App', 'Calculator', 'Display'],
        dependencies: 22,
        devDependencies: 15
      },
      'frontend-components': {
        files: [
          'src/components/Calculator.tsx',
          'src/components/Display.tsx',
          'src/components/Button.tsx',
          'src/components/History.tsx',
          'src/components/ThemeProvider.tsx',
          'src/hooks/useCalculator.ts',
          'src/hooks/useHistory.ts',
          'src/hooks/useTheme.ts',
          'src/pages/HomePage.tsx',
          'src/pages/HistoryPage.tsx'
        ],
        components: ['Calculator', 'Display', 'Button', 'History', 'ThemeProvider'],
        hooks: ['useCalculator', 'useHistory', 'useTheme'],
        pages: ['HomePage', 'HistoryPage'],
        styles: 8
      },
      'frontend-styling': {
        files: [
          'src/styles/cyberpunk.css',
          'src/styles/neon-effects.css',
          'src/styles/animations.css',
          'src/components/ui/button.tsx',
          'src/components/ui/card.tsx'
        ],
        themes: ['cyberpunk', 'neon'],
        animations: 12,
        effects: ['glow', 'pulse', 'flicker']
      },
      'api-integration': {
        files: [
          'src/services/api.ts',
          'src/services/calculator.ts',
          'src/services/history.ts',
          'src/types/api.ts'
        ],
        services: ['ApiService', 'CalculatorService', 'HistoryService'],
        endpoints: 4,
        types: 8
      },
      'unit-tests': {
        files: [
          'src/components/__tests__/Calculator.test.tsx',
          'src/components/__tests__/Display.test.tsx',
          'src/components/__tests__/Button.test.tsx',
          'src/hooks/__tests__/useCalculator.test.ts',
          'src/services/__tests__/api.test.ts'
        ],
        tests: 25,
        coverage: '94%',
        suites: 5
      },
      'integration-tests': {
        files: [
          'tests/e2e/calculator.spec.ts',
          'tests/e2e/history.spec.ts',
          'tests/api/calculator.test.ts'
        ],
        tests: 12,
        scenarios: 8,
        coverage: '88%'
      },
      'deployment-config': {
        files: [
          'vercel.json',
          'railway.json',
          'Dockerfile',
          'docker-compose.yml',
          '.github/workflows/deploy.yml'
        ],
        platforms: ['Vercel', 'Railway'],
        environments: ['development', 'staging', 'production'],
        workflows: 2
      }
    }

    return outputs[task.id] || {
      status: 'completed',
      timestamp: Date.now(),
      files: [`generated/${task.id}.ts`]
    }
  }

  /**
   * Generate actual file content for E2B sandbox
   */
  private generateFileContent(task: CodingTask): { file_path: string; file_content: string }[] {
    const files: { file_path: string; file_content: string }[] = []

    switch (task.id) {
      case 'frontend-setup':
        files.push(
          {
            file_path: 'package.json',
            file_content: JSON.stringify({
              name: 'ag3nt-generated-app',
              version: '0.1.0',
              private: true,
              scripts: {
                dev: 'next dev',
                build: 'next build',
                start: 'next start',
                lint: 'next lint'
              },
              dependencies: {
                'next': '^14.0.0',
                'react': '^18.0.0',
                'react-dom': '^18.0.0',
                'typescript': '^5.0.0',
                '@types/node': '^20.0.0',
                '@types/react': '^18.0.0',
                '@types/react-dom': '^18.0.0',
                'tailwindcss': '^3.3.0',
                'autoprefixer': '^10.4.0',
                'postcss': '^8.4.0'
              }
            }, null, 2)
          },
          {
            file_path: 'pages/index.tsx',
            file_content: `import React from 'react'
import Head from 'next/head'

export default function Home() {
  return (
    <div className="min-h-screen bg-black text-green-400 font-mono">
      <Head>
        <title>AG3NT Generated App</title>
        <meta name="description" content="Generated by AG3NT autonomous coding system" />
      </Head>

      <main className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-4 text-green-400 animate-pulse">
            🤖 AG3NT Generated Application
          </h1>
          <p className="text-xl mb-8 text-green-300">
            This application was autonomously generated by AG3NT
          </p>

          <div className="bg-gray-900 border border-green-400 rounded-lg p-6 max-w-2xl mx-auto">
            <h2 className="text-2xl font-semibold mb-4">System Status</h2>
            <div className="space-y-2 text-left">
              <div className="flex justify-between">
                <span>Framework:</span>
                <span className="text-green-400">Next.js</span>
              </div>
              <div className="flex justify-between">
                <span>Language:</span>
                <span className="text-green-400">TypeScript</span>
              </div>
              <div className="flex justify-between">
                <span>Styling:</span>
                <span className="text-green-400">Tailwind CSS</span>
              </div>
              <div className="flex justify-between">
                <span>Generated by:</span>
                <span className="text-green-400">AG3NT Framework</span>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}`
          }
        )
        break

      case 'frontend-components':
        files.push({
          file_path: 'components/Calculator.tsx',
          file_content: `import React, { useState } from 'react'

export default function Calculator() {
  const [display, setDisplay] = useState('0')
  const [previousValue, setPreviousValue] = useState<number | null>(null)
  const [operation, setOperation] = useState<string | null>(null)
  const [waitingForOperand, setWaitingForOperand] = useState(false)

  const inputNumber = (num: string) => {
    if (waitingForOperand) {
      setDisplay(num)
      setWaitingForOperand(false)
    } else {
      setDisplay(display === '0' ? num : display + num)
    }
  }

  const calculate = (firstValue: number, secondValue: number, operation: string): number => {
    switch (operation) {
      case '+': return firstValue + secondValue
      case '-': return firstValue - secondValue
      case '*': return firstValue * secondValue
      case '/': return firstValue / secondValue
      default: return secondValue
    }
  }

  return (
    <div className="bg-gray-900 border-2 border-green-400 rounded-lg p-6 max-w-sm mx-auto">
      <div className="mb-4">
        <div className="bg-black border border-green-400 rounded p-4 text-right">
          <div className="text-2xl font-mono text-green-400 min-h-[2rem]">
            {display}
          </div>
        </div>
      </div>
      <div className="grid grid-cols-4 gap-2">
        <button className="col-span-2 bg-red-600 text-white font-bold py-3 px-4 rounded">
          Clear
        </button>
        <button className="bg-blue-600 text-white font-bold py-3 px-4 rounded">÷</button>
        <button className="bg-blue-600 text-white font-bold py-3 px-4 rounded">×</button>
        {[7, 8, 9, 4, 5, 6, 1, 2, 3, 0].map(num => (
          <button
            key={num}
            onClick={() => inputNumber(String(num))}
            className="bg-gray-700 text-green-400 font-bold py-3 px-4 rounded"
          >
            {num}
          </button>
        ))}
      </div>
    </div>
  )
}`
        })
        break

      default:
        files.push({
          file_path: 'pages/index.tsx',
          file_content: `import React from 'react'

export default function Home() {
  return (
    <div className="min-h-screen bg-black text-green-400 flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-4xl font-bold mb-4">🤖 AG3NT Generated</h1>
        <p className="text-xl">Task: ${task.title}</p>
        <p className="text-sm mt-4 text-green-300">Generated by AG3NT Framework</p>
      </div>
    </div>
  )
}`
        })
    }

    return files
  }

  /**
   * Get current progress
   */
  getProgress(): CodingProgress {
    const totalTasks = this.codingTasks.length
    const completedTasks = this.codingTasks.filter(t => t.status === 'completed').length
    const inProgressTasks = this.codingTasks.filter(t => t.status === 'in_progress').length
    const failedTasks = this.codingTasks.filter(t => t.status === 'failed').length
    
    const currentPhase = this.getCurrentPhase()
    const estimatedCompletion = this.calculateEstimatedCompletion()
    const activeAgents = Array.from(this.activeAgents.keys())

    return {
      totalTasks,
      completedTasks,
      inProgressTasks,
      failedTasks,
      currentPhase,
      estimatedCompletion,
      activeAgents
    }
  }

  /**
   * Get current development phase
   */
  private getCurrentPhase(): string {
    const phases = [
      { name: 'Database Setup', tasks: ['db-schema'] },
      { name: 'Backend Development', tasks: ['backend-setup', 'backend-api'] },
      { name: 'Frontend Development', tasks: ['frontend-setup', 'frontend-components', 'frontend-styling'] },
      { name: 'Integration', tasks: ['api-integration'] },
      { name: 'Testing', tasks: ['unit-tests', 'integration-tests'] },
      { name: 'Deployment', tasks: ['deployment-config'] }
    ]

    for (const phase of phases) {
      const phaseTasks = this.codingTasks.filter(t => phase.tasks.includes(t.id))
      const completedPhaseTasks = phaseTasks.filter(t => t.status === 'completed')
      
      if (completedPhaseTasks.length < phaseTasks.length) {
        return phase.name
      }
    }

    return 'Completed'
  }

  /**
   * Calculate estimated completion time
   */
  private calculateEstimatedCompletion(): number {
    const pendingTasks = this.codingTasks.filter(t => t.status === 'pending')
    const totalEstimatedTime = pendingTasks.reduce((sum, task) => sum + task.estimatedTime, 0)
    
    return Date.now() + totalEstimatedTime
  }

  /**
   * Subscribe to progress updates
   */
  onProgress(callback: (progress: CodingProgress) => void): void {
    this.progressCallbacks.push(callback)
  }

  /**
   * Notify progress subscribers
   */
  private notifyProgress(): void {
    const progress = this.getProgress()
    this.progressCallbacks.forEach(callback => callback(progress))
  }

  /**
   * Get all coding tasks
   */
  getTasks(): CodingTask[] {
    return [...this.codingTasks]
  }

  /**
   * Check if workflow is running
   */
  isWorkflowRunning(): boolean {
    return this.isRunning
  }

  /**
   * Create E2B sandbox for generated code with retry mechanism
   */
  async createSandbox(taskId: string, files: { file_path: string; file_content: string }[], retries: number = 3): Promise<string> {
    try {
      this.addStreamingUpdate({
        type: 'generation',
        message: `Creating sandbox for ${taskId}...`,
        taskId,
        timestamp: Date.now()
      })

      const fragment: FragmentSchema = {
        title: `AG3NT Generated - ${taskId}`,
        description: `Autonomous code generation for task: ${taskId}`,
        template: 'nextjs-developer',
        is_multi_file: true,
        files: files.map(f => ({
          file_path: f.file_path,
          file_content: f.file_content,
          file_type: this.getFileType(f.file_path)
        })),
        has_additional_dependencies: true,
        additional_dependencies: ['@types/node', '@types/react', '@types/react-dom'],
        install_dependencies_command: 'npm install',
        port: 3000
      }

      // Use absolute URL for server-side fetch
      const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
      const sandboxUrl = `${baseUrl}/api/sandbox`

      const response = await fetch(sandboxUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          template: fragment.template,
          is_multi_file: fragment.is_multi_file,
          files: fragment.files,
          has_additional_dependencies: fragment.has_additional_dependencies,
          additional_dependencies: fragment.additional_dependencies,
          install_dependencies_command: fragment.install_dependencies_command,
          port: fragment.port,
          userID: 'ag3nt-system',
          teamID: 'ag3nt-autonomous'
        }),
      })

      if (!response.ok) {
        throw new Error(`Sandbox creation failed: ${response.statusText}`)
      }

      const result: ExecutionResult = await response.json()

      if ('url' in result) {
        const sandboxInfo: SandboxInfo = {
          id: result.sbxId,
          template: result.template,
          url: result.url,
          status: 'ready',
          taskId
        }

        this.sandboxes.set(result.sbxId, sandboxInfo)

        this.addStreamingUpdate({
          type: 'completion',
          message: `Sandbox ready: ${result.url}`,
          taskId,
          timestamp: Date.now()
        })

        return result.sbxId
      } else {
        throw new Error('Unexpected sandbox result type')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'

      if (retries > 0) {
        this.addStreamingUpdate({
          type: 'error',
          message: `Sandbox creation failed, retrying... (${retries} attempts left): ${errorMessage}`,
          taskId,
          timestamp: Date.now()
        })

        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 2000))
        return this.createSandbox(taskId, files, retries - 1)
      } else {
        this.addStreamingUpdate({
          type: 'error',
          message: `Sandbox creation failed after all retries: ${errorMessage}`,
          taskId,
          timestamp: Date.now()
        })
        throw error
      }
    }
  }

  /**
   * Get file type from extension
   */
  private getFileType(filePath: string): string {
    const ext = filePath.split('.').pop()?.toLowerCase()
    switch (ext) {
      case 'tsx': return 'typescript-react'
      case 'ts': return 'typescript'
      case 'jsx': return 'javascript-react'
      case 'js': return 'javascript'
      case 'css': return 'css'
      case 'json': return 'json'
      case 'md': return 'markdown'
      default: return 'text'
    }
  }

  /**
   * Add streaming update with memory management
   */
  private addStreamingUpdate(update: Omit<StreamingUpdate, 'timestamp'>): void {
    const fullUpdate: StreamingUpdate = {
      ...update,
      timestamp: Date.now()
    }

    this.streamingUpdates.push(fullUpdate)

    // Safely call callbacks with error handling
    this.updateCallbacks.forEach(callback => {
      try {
        callback(fullUpdate)
      } catch (error) {
        console.error('Error in streaming update callback:', error)
      }
    })

    // Keep only last 50 updates to prevent memory issues
    if (this.streamingUpdates.length > 50) {
      this.streamingUpdates = this.streamingUpdates.slice(-50)
    }
  }

  /**
   * Get current project plan
   */
  getProjectPlan(): ProjectPlan | null {
    return this.projectPlan
  }

  /**
   * Clear current project plan
   */
  clearProjectPlan(): void {
    this.projectPlan = null
    this.codingTasks = []
  }

  /**
   * Stop current workflow
   */
  async stopWorkflow(): Promise<void> {
    this.isRunning = false

    // Clear active agents
    this.activeAgents.clear()

    // Add stopping update
    this.addStreamingUpdate({
      type: 'completion',
      message: 'Workflow stopped by user',
      timestamp: Date.now()
    })
  }

  /**
   * Cleanup resources and prevent memory leaks
   */
  cleanup(): void {
    // Clear callbacks
    this.updateCallbacks = []
    this.progressCallbacks = []

    // Clear data structures
    this.streamingUpdates = []
    this.sandboxes.clear()
    this.activeAgents.clear()

    // Reset state
    this.isRunning = false
    this.codingTasks = []
    this.projectPlan = null
  }

  /**
   * Subscribe to streaming updates
   */
  onStreamingUpdate(callback: (update: StreamingUpdate) => void): void {
    this.updateCallbacks.push(callback)
  }

  /**
   * Get all sandboxes
   */
  getSandboxes(): SandboxInfo[] {
    return Array.from(this.sandboxes.values())
  }

  /**
   * Map plan task type to coding task type
   */
  private mapTaskType(planType: string): 'frontend' | 'backend' | 'database' | 'testing' | 'deployment' | 'integration' {
    const typeMap: Record<string, 'frontend' | 'backend' | 'database' | 'testing' | 'deployment' | 'integration'> = {
      'frontend': 'frontend',
      'backend': 'backend',
      'database': 'database',
      'testing': 'testing',
      'deployment': 'deployment',
      'integration': 'integration',
      'fullstack': 'frontend', // Default fullstack to frontend
      'setup': 'frontend', // Default setup to frontend
      'ui': 'frontend',
      'api': 'backend',
      'db': 'database'
    }

    return typeMap[planType.toLowerCase()] || 'frontend'
  }

  /**
   * Parse estimated time string to seconds
   */
  private parseEstimatedTime(timeStr?: string): number {
    if (!timeStr) return 300 // Default 5 minutes

    // Extract numbers from string like "2-3 hours", "30 minutes", "1 hour"
    const hourMatch = timeStr.match(/(\d+)(?:-\d+)?\s*hours?/i)
    const minuteMatch = timeStr.match(/(\d+)(?:-\d+)?\s*minutes?/i)

    if (hourMatch) {
      return parseInt(hourMatch[1]) * 3600 // Convert hours to seconds
    } else if (minuteMatch) {
      return parseInt(minuteMatch[1]) * 60 // Convert minutes to seconds
    }

    return 300 // Default 5 minutes
  }

  /**
   * Get streaming updates
   */
  getStreamingUpdates(): StreamingUpdate[] {
    return [...this.streamingUpdates]
  }

  /**
   * Enhanced progress with E2B data
   */
  getProgress(): CodingProgress {
    const totalTasks = this.codingTasks.length
    const completedTasks = this.codingTasks.filter(t => t.status === 'completed').length
    const inProgressTasks = this.codingTasks.filter(t => t.status === 'in_progress').length
    const failedTasks = this.codingTasks.filter(t => t.status === 'failed').length

    return {
      totalTasks,
      completedTasks,
      inProgressTasks,
      failedTasks,
      currentPhase: this.getCurrentPhase(),
      estimatedCompletion: this.calculateEstimatedCompletion(),
      activeAgents: Array.from(this.activeAgents.keys()),
      sandboxes: this.getSandboxes(),
      streamingUpdates: this.getStreamingUpdates()
    }
  }
}

// Export singleton instance
export const codingOrchestrator = new CodingWorkflowOrchestrator()

export default CodingWorkflowOrchestrator
